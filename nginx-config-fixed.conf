# 修正后的 Nginx 配置

location / {
    try_files $uri $uri/ /index.html?$query_string;
}

# 修正：使用正确的目标域名 api-gai.metishon.co
location /api/ {
    proxy_pass https://api-gai.metishon.co/;
    
    # 设置正确的Host头
    proxy_set_header Host api-gai.metishon.co;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # SSL配置
    proxy_ssl_verify off;
    proxy_ssl_server_name on;
    
    # 超时配置
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # CORS处理
    add_header Access-Control-Allow-Origin * always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    
    # 处理OPTIONS预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
}

location /tts-api/ {
    # 代理到字节跳动TTS API
    proxy_pass https://openspeech.bytedance.com/;
    
    # 重要：设置正确的Host头
    proxy_set_header Host openspeech.bytedance.com;
    
    # 基础代理头
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 添加必要的浏览器头部来模拟正常请求
    proxy_set_header User-Agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
    proxy_set_header Accept "application/json, text/plain, */*";
    proxy_set_header Accept-Language "zh-CN,zh;q=0.9,en;q=0.8";
    proxy_set_header Accept-Encoding "gzip, deflate, br";
    
    # 保持连接
    proxy_set_header Connection "";
    proxy_http_version 1.1;
    
    # SSL配置
    proxy_ssl_verify off;
    proxy_ssl_server_name on;
    
    # 超时配置
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # CORS处理（如果需要）
    add_header Access-Control-Allow-Origin * always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    
    # 处理OPTIONS预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
}
