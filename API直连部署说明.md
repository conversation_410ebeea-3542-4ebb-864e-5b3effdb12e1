# API直连部署说明

## 🎯 更新内容

项目已更新为支持**环境自动切换**的API配置：

- **开发环境** (`yarn dev`): 使用代理路径，通过Vite代理转发API请求
- **生产环境** (`yarn build`): 直接使用原始API地址，无需反向代理

## 📁 新增文件

1. **src/utils/envConfig.ts** - 环境配置管理
2. **.env.development** - 开发环境配置
3. **.env.production** - 生产环境配置

## 🔧 配置说明

### 开发环境配置 (.env.development)
```env
VITE_AUTH_API_BASE_URL=/auth-api
VITE_SENSETIME_API_BASE_URL=/api
VITE_TTS_API_BASE_URL=/tts-api
```

### 生产环境配置 (.env.production)
```env
VITE_AUTH_API_BASE_URL=https://api.st.vup.tools
VITE_SENSETIME_API_BASE_URL=https://api-gai.metishon.co
VITE_TTS_API_BASE_URL=https://openspeech.bytedance.com
```

## 🚀 部署步骤

### 1. 构建项目
```bash
yarn run build
```

### 2. 部署静态文件
将 `dist` 目录下的所有文件上传到Web服务器即可。

### 3. 配置伪静态（仅用于SPA路由）
只需要配置SPA路由重定向，**不需要API代理**：

```nginx
# 简化的伪静态规则
location / {
    try_files $uri $uri/ /index.html;
}
```

## ✅ 优势

1. **简化部署**: 无需配置API反向代理
2. **减少服务器负载**: 直接请求API，减少代理转发
3. **提高性能**: 减少网络跳转，提升响应速度
4. **降低复杂度**: 部署配置更简单

## 🔍 验证部署

部署完成后，打开浏览器开发者工具的Network面板，检查API请求：

- ✅ 应该看到直接请求到 `https://api.st.vup.tools`
- ✅ 应该看到直接请求到 `https://api-gai.metishon.co`
- ✅ 不应该有 `/auth-api` 或 `/api` 的代理请求

## 🛠️ 自定义API地址

如果需要使用不同的API地址，可以：

1. **修改 .env.production 文件**:
```env
VITE_AUTH_API_BASE_URL=https://your-custom-api.com
```

2. **重新构建**:
```bash
yarn run build
```

## 📋 兼容性说明

- **开发环境**: 保持原有的代理配置，开发体验不变
- **生产环境**: 自动切换到直连模式
- **向后兼容**: 如果环境变量未设置，会使用默认的API地址

## 🔧 故障排除

### 问题1: API请求失败
**可能原因**: CORS跨域问题
**解决方案**: 确保API服务器已配置正确的CORS头

### 问题2: 环境变量未生效
**可能原因**: 环境变量名称错误或缓存问题
**解决方案**: 
1. 检查环境变量名称是否以 `VITE_` 开头
2. 清除构建缓存: `rm -rf dist && yarn build`

### 问题3: 开发环境API不通
**可能原因**: Vite代理配置问题
**解决方案**: 检查 `vite.config.ts` 中的代理配置是否正确
