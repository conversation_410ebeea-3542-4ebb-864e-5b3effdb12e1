# 用户信息显示和对话历史缓存优化

## 🎯 优化目标

1. **侧边栏底部用户信息部分**：使用缓存中的真实用户信息
2. **设置页面用户信息部分**：使用缓存中的真实用户信息  
3. **对话历史列表缓存**：缓存到localStorage，减少不必要的API请求

## 🔧 技术实现

### 1. 侧边栏用户信息优化

**文件**: `src/pages/ChatPage.tsx`

**主要修改**:
- 添加了 `userProfile` 状态来存储缓存的用户资料
- 修改了 `refreshUserInfo` 函数，优先使用缓存的用户资料
- 更新了侧边栏用户信息显示逻辑，优先显示服务器返回的真实信息

**新增导入**:
```typescript
import { getUserProfile, isUserProfileCached } from '../utils/userInfoUtils';
import { refreshUserProfile } from '../utils/auth';
```

**状态管理**:
```typescript
const [userProfile, setUserProfile] = useState<any>(null); // 缓存的用户资料
```

**刷新逻辑**:
```typescript
const refreshUserInfo = useCallback(async () => {
  // 首先设置本地存储的基本信息
  setUserAvatar(getAvatar());
  setUserNickname(getNickname());
  setUserPhone(getPhone());

  // 尝试获取缓存的用户资料
  const cachedProfile = getUserProfile();
  if (cachedProfile) {
    setUserProfile(cachedProfile);
    // 优先使用缓存中的真实信息
    if (cachedProfile.username) {
      setUserNickname(cachedProfile.username);
    }
    if (cachedProfile.phone) {
      setUserPhone(cachedProfile.phone);
    }
  } else {
    // 如果没有缓存，从服务器获取
    const result = await refreshUserProfile(false);
    if (result.success && result.userProfile) {
      setUserProfile(result.userProfile);
      // 更新显示信息
    }
  }
}, []);
```

**显示逻辑**:
```typescript
{/* 用户昵称 */}
{userProfile?.username || userNickname || '汽车街用户'}

{/* 用户电话 */}
{userProfile?.phone || userPhone || '未设置手机号'}

{/* 用户邮箱（如果有） */}
{userProfile?.email && (
  <p className="text-xs text-gray-400 truncate">{userProfile.email}</p>
)}
```

### 2. 设置页面用户信息优化

**文件**: `src/pages/SettingsPage.tsx`

**主要修改**:
- 添加了 `userProfile` 状态
- 修改了组件挂载时的用户信息加载逻辑
- 优先使用缓存的用户资料信息

**新增状态**:
```typescript
const [userProfile, setUserProfile] = useState<any>(null); // 缓存的用户资料
```

**加载逻辑**:
```typescript
useEffect(() => {
  const loadUserInfo = async () => {
    // 首先加载本地存储的基本信息
    const savedAvatar = getAvatar();
    const savedNickname = getNickname();
    // ... 其他本地信息

    // 尝试获取缓存的用户资料
    const cachedProfile = getUserProfile();
    if (cachedProfile) {
      setUserProfile(cachedProfile);
      // 优先使用缓存中的真实信息
      if (cachedProfile.username) {
        setCurrentNickname(cachedProfile.username);
      }
      if (cachedProfile.phone) {
        setCurrentPhone(cachedProfile.phone);
      }
    } else {
      // 从服务器获取
      const result = await refreshUserProfile(false);
      // 处理结果...
    }
  };

  loadUserInfo();
}, []);
```

### 3. 对话历史localStorage缓存

**文件**: `src/utils/conversationStorage.ts`

**主要修改**:
- 添加了localStorage缓存机制
- 实现了缓存过期检查（24小时）
- 支持强制刷新参数

**新增常量**:
```typescript
const CONVERSATION_HISTORY_CACHE_KEY = 'conversation_history_cache';
const CONVERSATION_HISTORY_CACHE_TIME_KEY = 'conversation_history_cache_time';
```

**缓存函数**:
```typescript
// 保存缓存
const saveConversationHistoryCache = (groupedConversations: any) => {
  localStorage.setItem(CONVERSATION_HISTORY_CACHE_KEY, JSON.stringify(groupedConversations));
  localStorage.setItem(CONVERSATION_HISTORY_CACHE_TIME_KEY, Date.now().toString());
};

// 获取缓存
const getConversationHistoryCache = () => {
  const cached = localStorage.getItem(CONVERSATION_HISTORY_CACHE_KEY);
  const cacheTime = localStorage.getItem(CONVERSATION_HISTORY_CACHE_TIME_KEY);
  
  if (cached && cacheTime) {
    const cacheTimestamp = parseInt(cacheTime);
    const isExpired = Date.now() - cacheTimestamp > 24 * 60 * 60 * 1000; // 24小时
    
    if (!isExpired) {
      return JSON.parse(cached);
    } else {
      clearConversationHistoryCache();
    }
  }
  
  return null;
};

// 清除缓存
const clearConversationHistoryCache = () => {
  localStorage.removeItem(CONVERSATION_HISTORY_CACHE_KEY);
  localStorage.removeItem(CONVERSATION_HISTORY_CACHE_TIME_KEY);
};
```

**修改getGroupedConversations函数**:
```typescript
export const getGroupedConversations = async (forceRefresh: boolean = false) => {
  try {
    // 如果不强制刷新，先尝试使用缓存
    if (!forceRefresh) {
      const cachedHistory = getConversationHistoryCache();
      if (cachedHistory) {
        return cachedHistory;
      }
    }

    // 从服务器获取数据
    const result = await getConversationsAPI();
    
    // 处理数据并分组
    // ...

    // 保存到localStorage缓存
    saveConversationHistoryCache(groups);

    return groups;
  } catch (error) {
    // 错误处理...
  }
};
```

### 4. 缓存清除策略

**触发缓存清除的场景**:
1. **创建新对话时** - 确保会话列表包含新创建的对话
2. **删除对话时** - 确保会话列表移除已删除的对话
3. **用户手动下拉刷新时** - 获取最新的服务器数据

**实现方式**:
```typescript
// 在startNewChat函数中
clearConversationHistoryCache_Export();
await loadConversationHistory(true);

// 在deleteConversationHandler函数中  
clearConversationHistoryCache_Export();
loadConversationHistory(true);

// 在下拉刷新中
await loadConversationHistory(true); // forceRefresh = true
```

## 📋 工作流程

### 用户信息显示流程
```mermaid
graph TD
    A[组件加载] --> B[获取本地存储信息]
    B --> C[检查用户资料缓存]
    C --> D{有缓存?}
    D -->|是| E[使用缓存信息]
    D -->|否| F[从服务器获取]
    F --> G[保存到缓存]
    G --> H[更新显示]
    E --> H
```

### 对话历史缓存流程
```mermaid
graph TD
    A[请求会话历史] --> B{强制刷新?}
    B -->|否| C[检查localStorage缓存]
    B -->|是| F[从服务器获取]
    C --> D{缓存有效?}
    D -->|是| E[返回缓存数据]
    D -->|否| F
    F --> G[保存到localStorage]
    G --> H[返回数据]
```

## ✅ 优化效果

1. **用户体验提升**:
   - 侧边栏和设置页面显示真实的用户信息
   - 减少了不必要的API请求
   - 提高了页面加载速度

2. **性能优化**:
   - localStorage缓存减少网络请求
   - 24小时缓存过期机制保证数据新鲜度
   - 智能缓存清除策略

3. **数据一致性**:
   - 优先使用服务器返回的真实数据
   - 本地存储作为备选方案
   - 缓存失效时自动重新获取

## 🔄 后续优化建议

1. **错误处理增强**: 添加更详细的错误提示和重试机制
2. **缓存策略优化**: 可以考虑根据用户活跃度调整缓存过期时间
3. **数据同步**: 考虑添加后台数据同步机制
4. **用户反馈**: 添加数据加载状态指示器
