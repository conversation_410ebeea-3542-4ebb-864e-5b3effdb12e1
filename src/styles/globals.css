@tailwind base;
@tailwind components;
@tailwind utilities;

/* 移动端专用样式 */
@layer base {
  html {
    /* 防止在移动设备上缩放 */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    /* 移动端优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 防止橡皮筋效果 */
    overscroll-behavior: none;
  }
}

@layer components {
  /* 移动端输入框优化 */
  .mobile-input {
    @apply text-base; /* 防止iOS缩放 */
  }

  /* 移动端按钮优化 */
  .mobile-button {
    @apply min-h-[44px]; /* 符合移动端触摸标准 */
  }

  /* 扁平化输入框 - 移除阴影，统一圆角 */
  .flat-input {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    background-color: #ffffff !important;
    border-radius: 12px !important; /* 统一圆角大小 */
  }

  .flat-input:focus-within {
    box-shadow: none !important;
    border-color: #00A76F !important;
    outline: none !important;
  }

  .flat-input.is-invalid {
    border-color: #ef4444 !important;
    box-shadow: none !important;
  }

  .flat-input.is-invalid:focus-within {
    border-color: #ef4444 !important;
    box-shadow: none !important;
  }

  /* 扁平化选择框 - 移除阴影，统一圆角 */
  .flat-select {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    background-color: #ffffff !important;
    border-radius: 12px !important; /* 与输入框圆角保持一致 */
  }

  .flat-select:focus-within {
    box-shadow: none !important;
    border-color: #00A76F !important;
    outline: none !important;
  }

  /* 扁平化按钮 - 移除阴影，统一圆角 */
  .flat-button {
    box-shadow: none !important;
    border-radius: 12px !important; /* 与HeroUI输入框圆角保持一致 */
  }

  .flat-button:hover {
    box-shadow: none !important;
  }

  .flat-button:focus {
    box-shadow: none !important;
    outline: none !important;
  }

  /* 企业主色按钮 */
  .btn-primary {
    background-color: #00A76F !important;
    border-color: #00A76F !important;
    color: white !important;
    box-shadow: none !important;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #008f5f !important;
    border-color: #008f5f !important;
    box-shadow: none !important;
  }

  .btn-primary:disabled {
    background-color: #cccccc !important;
    border-color: #cccccc !important;
    color: #666666 !important;
    box-shadow: none !important;
  }

  /* 企业主色边框按钮 */
  .btn-primary-outline {
    background-color: transparent !important;
    border-color: #00A76F !important;
    color: #00A76F !important;
    box-shadow: none !important;
  }

  .btn-primary-outline:hover:not(:disabled) {
    background-color: #00A76F !important;
    color: white !important;
    box-shadow: none !important;
  }

  /* 聊天页面样式 */
  .chat-message-enter {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease-out;
  }

  .chat-message-enter-active {
    opacity: 1;
    transform: translateY(0);
  }

  /* 语音录音动画 */
  .voice-recording-pulse {
    animation: voicePulse 1.5s ease-in-out infinite;
  }

  @keyframes voicePulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.7;
    }
    50% {
      transform: scale(1.1);
      opacity: 1;
    }
  }

  /* 语音波形动画 */
  .voice-wave {
    animation: voiceWave 0.6s ease-in-out infinite alternate;
  }

  @keyframes voiceWave {
    0% {
      height: 8px;
    }
    100% {
      height: 24px;
    }
  }



  /* 响应式文本输入框 */
  .chat-input-wrapper {
    min-height: 44px;
    max-height: 120px;
    transition: height 0.2s ease-out;
  }

  /* AI思考中的跳动圆点动画 */
  .thinking-dots {
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .thinking-dot {
    width: 6px;
    height: 6px;
    background-color: #9ca3af;
    border-radius: 50%;
    animation: thinkingBounce 1.4s ease-in-out infinite both;
  }

  .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
  .thinking-dot:nth-child(2) { animation-delay: -0.16s; }
  .thinking-dot:nth-child(3) { animation-delay: 0s; }

  @keyframes thinkingBounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  /* AI消息气泡样式 */
  .ai-message-bubble {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 18px;
    padding: 12px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Markdown 渲染样式优化 */
  .ai-message-bubble .markdown-content {
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
  }

  /* Markdown 代码块样式 */
  .ai-message-bubble pre {
    margin: 8px 0;
    border-radius: 8px;
    overflow-x: auto;
    background-color: #1a1a1a !important;
  }

  /* Markdown 行内代码样式 */
  .ai-message-bubble code:not(pre code) {
    background-color: #f3f4f6;
    color: #dc2626;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 13px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  /* Markdown 列表样式 */
  .ai-message-bubble ul, .ai-message-bubble ol {
    margin: 8px 0;
    padding-left: 20px;
  }

  .ai-message-bubble li {
    margin: 4px 0;
  }

  /* Markdown 表格样式 */
  .ai-message-bubble table {
    width: 100%;
    margin: 8px 0;
    border-collapse: collapse;
    font-size: 13px;
  }

  .ai-message-bubble th, .ai-message-bubble td {
    padding: 6px 8px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .ai-message-bubble th {
    background-color: #f9fafb;
    font-weight: 600;
  }

  /* Markdown 引用样式 */
  .ai-message-bubble blockquote {
    margin: 8px 0;
    padding: 8px 12px;
    border-left: 4px solid #00A76F;
    background-color: #f0fdf4;
    font-style: italic;
  }

  /* Markdown 标题样式 */
  .ai-message-bubble h1, .ai-message-bubble h2, .ai-message-bubble h3,
  .ai-message-bubble h4, .ai-message-bubble h5, .ai-message-bubble h6 {
    margin: 12px 0 8px 0;
    font-weight: 600;
    color: #111827;
  }

  .ai-message-bubble h1 { font-size: 18px; }
  .ai-message-bubble h2 { font-size: 16px; }
  .ai-message-bubble h3 { font-size: 15px; }
  .ai-message-bubble h4 { font-size: 14px; }
  .ai-message-bubble h5 { font-size: 13px; }
  .ai-message-bubble h6 { font-size: 12px; }

  /* 用户消息气泡样式 */
  .user-message-bubble {
    background: linear-gradient(135deg, #00A76F 0%, #00C878 100%);
    border-radius: 18px;
    padding: 12px 16px;
    max-width: 85%;
    color: white;
    margin-left: auto;
    box-shadow: 0 1px 3px rgba(0, 167, 111, 0.3);
  }

  /* 车辆推荐卡片样式 - 原版垂直布局 */
  .car-recommendation-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    margin-top: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 100%;
  }

  .car-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    background-color: #f8fafc;
  }

  .car-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 12px;
  }

  .car-info-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .car-info-label {
    color: #64748b;
    font-size: 14px;
  }

  .car-info-value {
    color: #1e293b;
    font-size: 14px;
    font-weight: 500;
  }

  /* 车辆推荐卡片样式 - 新版横向商务布局 */
  .car-recommendation-card-horizontal {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 12px;
    margin-top: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 100%;
    transition: all 0.2s ease;
  }

  .car-recommendation-card-horizontal:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  /* 车辆详情页面样式 */
  .car-detail-image-container {
    position: relative;
    width: 100%;
    height: 256px;
    background-color: #f8fafc;
  }

  .car-detail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .car-detail-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
  }

  .car-detail-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  .car-detail-info-label {
    color: #64748b;
    font-size: 14px;
  }

  .car-detail-info-value {
    color: #1e293b;
    font-size: 14px;
    font-weight: 500;
  }

  /* 星级评分样式 */
  .star-rating {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .star-rating .star {
    font-size: 16px;
  }

  /* 损伤状态颜色 */
  .damage-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .damage-indicator.no-damage {
    background-color: #10b981;
  }

  .damage-indicator.minor-damage {
    background-color: #f59e0b;
  }

  .damage-indicator.major-damage {
    background-color: #ef4444;
  }

  .damage-indicator.unknown {
    background-color: #6b7280;
  }

  /* 检测报告优化样式 */
  .damage-category-section {
    margin-bottom: 24px;
  }

  .damage-category-title {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
  }

  .damage-color-section {
    margin-bottom: 16px;
    margin-left: 8px;
  }

  .damage-color-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }

  .damage-item-card {
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
  }

  .damage-item-card:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  .damage-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .damage-item-info {
    flex: 1;
  }

  .damage-item-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .damage-item-status {
    color: #6b7280;
    font-size: 12px;
  }

  .damage-images-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e5e7eb;
  }

  .damage-images-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .damage-image-container {
    position: relative;
    aspect-ratio: 1;
    border-radius: 6px;
    overflow: hidden;
  }

  .damage-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .damage-image:hover {
    opacity: 0.8;
  }

  .damage-images-hint {
    font-size: 12px;
    color: #9ca3af;
    margin-top: 8px;
    text-align: center;
  }

  .expand-button {
    padding: 4px 8px;
    font-size: 12px;
    color: #2563eb;
    border: 1px solid #bfdbfe;
    border-radius: 4px;
    background-color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
  }

  .expand-button:hover {
    color: #1d4ed8;
    background-color: #eff6ff;
    border-color: #93c5fd;
  }

  /* 图片失效占位符样式 */
  .image-error-fallback {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .main-image-error-fallback {
    background-color: #f3f4f6;
    color: #6b7280;
    font-size: 16px;
    font-weight: 500;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 图片失效样式 - 统一样式 */
  .image-fallback {
    background-color: #f9fafb;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
  }

  /* 错误边界样式 */
  .error-boundary {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  /* Markdown错误降级样式 */
  .markdown-error-fallback {
    width: 100%;
  }

  .markdown-error-fallback .bg-yellow-50 {
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 折叠功能样式 */
  .collapsible-header {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
  }

  .collapsible-header:hover {
    background-color: #f3f4f6;
  }

  .category-header {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
  }

  .category-header:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  .color-header {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
  }

  .color-header:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }

  .stats-badge {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    margin-left: 4px;
  }

  .damage-item-card-compact {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
  }

  .damage-item-card-compact:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .expand-arrow {
    transition: transform 0.2s ease;
  }

  .expand-arrow.expanded {
    transform: rotate(90deg);
  }

  /* 两列布局优化 */
  .two-column-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  @media (max-width: 640px) {
    .two-column-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    /* 小屏幕上强制保持5列，缩小字体 */
    .basic-info-5cols .text-base {
      font-size: 12px !important;
    }

    .basic-info-5cols .text-xs {
      font-size: 10px !important;
    }

    .basic-info-5cols {
      gap: 8px 6px !important;
    }
  }

  @media (max-width: 480px) {
    /* 超小屏幕进一步缩小字体 */
    .basic-info-5cols .text-base {
      font-size: 11px !important;
    }

    .basic-info-5cols .text-xs {
      font-size: 9px !important;
    }

    .basic-info-5cols {
      gap: 6px 4px !important;
    }
  }

  /* 基础信息5列3行布局优化 */
  .basic-info-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px 12px;
  }

  .basic-info-item {
    text-align: center;
    min-width: 0;
  }

  .basic-info-value {
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
    word-break: break-all;
  }

  .basic-info-label {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.2;
  }

  /* 车况评级样式 */
  .rating-section {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
  }

  .rating-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
  }

  .rating-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    font-size: 12px;
  }

  .rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .rating-label {
    color: #6b7280;
  }

  .rating-stars {
    display: flex;
    align-items: center;
  }

  .rating-score {
    margin-left: 4px;
    font-size: 12px;
    color: #6b7280;
  }

  /* 固定高度图片容器 */
  .fixed-height-image-container {
    height: 256px;
    position: relative;
    background-color: #f3f4f6;
  }

  .image-error-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    color: #6b7280;
    font-size: 18px;
    font-weight: 500;
  }

  .car-image-horizontal {
    width: 120px;
    height: 67.5px;
    object-fit: cover;
    border-radius: 8px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
  }

  /* 动态高度图片容器 */
  .car-image-container-dynamic {
    width: 120px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 8px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    overflow: hidden;
  }

  .car-image-dynamic {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }

  /* 图片加载状态 */
  .car-image-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #9ca3af;
    font-size: 12px;
  }

  .car-image-loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 4px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #9ca3af;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 商务风格信息网格 - 3列布局 */
  .car-info-grid-business {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px 8px;
    margin-top: 8px;
  }

  .car-info-item-business {
    display: flex;
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .car-info-label-business {
    color: #9ca3af;
    font-size: 11px;
    font-weight: 280;
    line-height: 1.2;
  }

  .car-info-value-business {
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
  }

  /* 响应式调整 */
  @media (max-width: 480px) {
    .car-image-horizontal {
      width: 100px;
      height: 56.25px;
    }

    .car-info-grid-business {
      grid-template-columns: 1fr 1fr;
      gap: 8px 6px;
    }

    .car-info-label-business {
      font-size: 10px;
    }

    .car-info-value-business {
      font-size: 11px;
    }
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 车辆推荐记录页面样式 */
  .h-15 {
    height: 3.75rem; /* 60px */
  }

  .h-21 {
    height: 5.25rem; /* 84px */
  }



  /* 词云头部区域样式 */
  .word-cloud-header {
    position: fixed;
    top: 48px; /* 导航栏高度 */
    left: 0;
    right: 0;
    height: 20vh; /* 占据20%视口高度 */
    background: rgba(255, 255, 255);
    pointer-events: none;
    z-index: 30;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 添加大圆角，让底部两个角更加美观 */
    border-radius: 0 0 24px 24px;
    /* 增加阴影效果 */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.01), 0 10px 8px rgba(0, 0, 0, 0.01);
  }

  .word-cloud-header-text {
    position: absolute;
    color: #00A76F;
    font-weight: 700;
    white-space: nowrap;
    opacity: 0.8;
    font-size: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translate(-50%, -50%); /* 以文字中心为定位点 */
    z-index: 30; /* 确保在波浪线之上 */
  }

  @keyframes headerFloatVertical {
    0%, 100% {
      transform: translate(-50%, -50%) translateY(0px);
      opacity: 0.4;
    }
    50% {
      transform: translate(-50%, -50%) translateY(-15px);
      opacity: 0.8;
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .robot-avatar-header {
    width: 280px;  /* 280px * 1.8 = 280px (再放大80%) */
    height: 280px;
    object-fit: contain;
    z-index: 35;
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
    animation: robotPulse 3s ease-in-out infinite;
    transform: translateY(40%); /* 向下移动20%，让头部完整展示 */
  }

  /* Spine 动画容器样式 */
  .spine-robot-container {
    position: relative;
    width: 280px;  /* 280px * 1.8 = 280px (再放大80%) */
    height: 280px;
    transform: translateY(40%); /* 向下移动20%，让头部完整展示 */
  }

  .spine-robot-container .spine-robot-canvas {
    width: 280px !important;
    height: 280px !important;
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
    transform: translateY(15%); /* 画布容器向下移动30%，让头部完整展示 */
  }

  /* Spine Player 内部 canvas 样式调整 */
  .spine-robot-container canvas {
    width: 280px !important;
    height: 280px !important;
    max-width: 280px !important;
    max-height: 280px !important;
  }

  /* Spine Robot 加载和错误状态样式 */
  .spine-robot-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 36;
  }

  .spine-loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #00A76F;
    border-radius: 50%;
    animation: spineSpinnerRotate 1s linear infinite;
  }

  @keyframes spineSpinnerRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }



  @keyframes robotPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.08);
    }
  }

  /* 科幻交织波形动画容器 */
  .wave-animation-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    overflow: hidden;
    pointer-events: none;
  }

  /* 优雅科幻波浪线条效果 */
  .word-cloud-header svg {
    opacity: 0.8;
  }

  .word-cloud-header svg path {
    stroke-linecap: round;
    stroke-linejoin: round;
    filter: drop-shadow(0 0 1px rgba(0, 167, 111, 0.1));
  }

  @keyframes float {
    0% {
      transform: translateX(-100px);
      opacity: 0;
    }
    5% {
      opacity: 1;
    }
    95% {
      opacity: 1;
    }
    100% {
      transform: translateX(calc(100vw + 100px));
      opacity: 0;
    }
  }



  /* 毛玻璃效果增强 */
  .glassmorphism {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glassmorphism-dark {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  /* 科技感动画 */
  .tech-glow {
    animation: techGlow 2s ease-in-out infinite alternate;
  }

  @keyframes techGlow {
    0% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
    }
  }

  /* 渐变边框动画 */
  .gradient-border {
    position: relative;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* 词云浮动动画 */
  .animate-float-slow {
    animation: floatSlow 8s ease-in-out infinite;
  }

  .animate-float-medium {
    animation: floatMedium 6s ease-in-out infinite;
  }

  .animate-float-fast {
    animation: floatFast 4s ease-in-out infinite;
  }

  @keyframes floatSlow {
    0%, 100% {
      transform: translateY(0px) translateX(0px);
      opacity: 0.4;
    }
    25% {
      transform: translateY(-10px) translateX(5px);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-5px) translateX(-3px);
      opacity: 0.5;
    }
    75% {
      transform: translateY(-15px) translateX(8px);
      opacity: 0.7;
    }
  }

  @keyframes floatMedium {
    0%, 100% {
      transform: translateY(0px) translateX(0px);
      opacity: 0.5;
    }
    33% {
      transform: translateY(-8px) translateX(-6px);
      opacity: 0.7;
    }
    66% {
      transform: translateY(-12px) translateX(4px);
      opacity: 0.6;
    }
  }

  @keyframes floatFast {
    0%, 100% {
      transform: translateY(0px) translateX(0px);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-6px) translateX(-4px);
      opacity: 0.8;
    }
  }

  /* 安全区域适配 */
  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }

  /* 输入法适配 */
  @supports (height: 100dvh) {
    .h-screen {
      height: 100dvh;
    }
  }

  /* Chat 页面全高度适配 */
  .chat-page-container {
    height: 100vh; /* 降级方案 */
  }

  /* 支持动态视口高度的浏览器使用更精确的高度 */
  @supports (height: 100dvh) {
    .chat-page-container {
      height: 100dvh; /* 动态视口高度，移动端更准确 */
    }
  }

  /* 头像抽屉动画 */
  .avatar-drawer-enter {
    transform: translateY(100%);
    opacity: 0;
  }

  .avatar-drawer-enter-active {
    transform: translateY(0);
    opacity: 1;
    transition: transform 300ms ease-out, opacity 300ms ease-out;
  }

  .avatar-drawer-exit {
    transform: translateY(0);
    opacity: 1;
  }

  .avatar-drawer-exit-active {
    transform: translateY(100%);
    opacity: 0;
    transition: transform 300ms ease-in, opacity 300ms ease-in;
  }

  /* 输入抽屉样式 */
  .input-drawer-keyboard-adjustment {
    transition: transform 0.3s ease-out;
  }

  /* 移动端输入框优化 */
  .mobile-input-optimized {
    font-size: 16px !important; /* 防止iOS缩放 */
    -webkit-appearance: none;
    -webkit-border-radius: 0;
    border-radius: 12px;
  }

  .mobile-input-optimized:focus {
    -webkit-appearance: none;
    outline: none;
  }

  /* 输入法适配提示 */
  .keyboard-hint {
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 移动端视口适配 */
  @media screen and (max-width: 768px) {
    .fixed-bottom-input {
      bottom: 0;
      bottom: env(keyboard-inset-height, 0);
    }

    /* 小屏幕首页布局优化 */
    .home-container {
      min-height: 100vh;
      min-height: 100dvh; /* 动态视口高度，更准确 */
    }

    /* 聊天页面自适应优化 */
    .chat-container-adaptive {
      /* 确保聊天容器能够自适应内容高度 */
      display: flex;
      flex-direction: column;
      min-height: 0; /* 允许收缩 */
    }
  }

  /* 防止页面在输入法弹出时缩放 */
  @media screen and (max-width: 768px) {
    html {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    input, textarea {
      font-size: 16px !important;
    }
  }

  /* 文本输入框优化 */
  .text-input-container {
    position: relative;
    transition: all 0.2s ease-out;
  }

  .text-input-container:focus-within {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 167, 111, 0.15);
  }

  /* 发送按钮悬停效果 */
  .send-button-enabled {
    background: linear-gradient(135deg, #00A76F 0%, #00C878 100%);
    box-shadow: 0 2px 8px rgba(0, 167, 111, 0.3);
  }

  .send-button-enabled:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 167, 111, 0.4);
  }

  .send-button-disabled {
    background: #9ca3af;
    opacity: 0.6;
  }
}

@layer utilities {
  /* 安全区域适配 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 移动端专用工具类 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* PC端模拟环境专用 - 确保滚动正常 */
  .desktop-simulation-scroll {
    touch-action: auto !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* 阻止长按选择和上下文菜单 */
  .no-context-menu {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* 扁平化工具类 */
  .flat {
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  .flat-rounded {
    box-shadow: none !important;
    border-radius: 4px !important;
  }

  .no-shadow {
    box-shadow: none !important;
  }

  .minimal-border {
    border: 1px solid #e5e7eb !important;
  }

  /* Markdown内容样式 - 修复DOM嵌套问题 */
  .markdown-content {
    display: block;
  }

  .markdown-content > * {
    display: block;
  }

  .markdown-content table {
    display: table;
    width: 100%;
    border-collapse: collapse;
  }
}
