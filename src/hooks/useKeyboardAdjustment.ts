/**
 * 移动端输入法适配Hook
 * 监听虚拟键盘弹出/收起，动态调整UI位置
 */

import { useState, useEffect, useCallback } from 'react';

interface KeyboardAdjustment {
  keyboardHeight: number;
  isKeyboardOpen: boolean;
  adjustedStyle: React.CSSProperties;
}

/**
 * 移动端输入法适配Hook
 * @param enabled 是否启用适配
 * @returns 键盘状态和调整样式
 */
export const useKeyboardAdjustment = (enabled: boolean = true): KeyboardAdjustment => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [initialViewportHeight, setInitialViewportHeight] = useState(0);

  // 计算键盘高度
  const calculateKeyboardHeight = useCallback(() => {
    if (!enabled) return 0;

    // 优先使用 Visual Viewport API
    if (window.visualViewport) {
      const currentHeight = window.visualViewport.height;
      const heightDiff = initialViewportHeight - currentHeight;
      return Math.max(0, heightDiff);
    }

    // 降级方案：使用 window.innerHeight
    const currentHeight = window.innerHeight;
    const heightDiff = initialViewportHeight - currentHeight;
    return Math.max(0, heightDiff);
  }, [enabled, initialViewportHeight]);

  // 处理视口变化
  const handleViewportChange = useCallback(() => {
    if (!enabled) return;

    const newKeyboardHeight = calculateKeyboardHeight();
    const threshold = 150; // 键盘高度阈值

    setKeyboardHeight(newKeyboardHeight);
    setIsKeyboardOpen(newKeyboardHeight > threshold);
  }, [enabled, calculateKeyboardHeight]);

  // 初始化和事件监听
  useEffect(() => {
    if (!enabled) return;

    // 记录初始视口高度
    const initHeight = window.visualViewport?.height || window.innerHeight;
    setInitialViewportHeight(initHeight);

    // 监听 Visual Viewport API
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
      window.visualViewport.addEventListener('scroll', handleViewportChange);
    } else {
      // 降级方案：监听 window resize
      window.addEventListener('resize', handleViewportChange);
    }

    // 监听输入框聚焦/失焦（额外保险）
    const handleFocusIn = () => {
      // 延迟检查，等待键盘动画完成
      setTimeout(handleViewportChange, 300);
    };

    const handleFocusOut = () => {
      // 延迟检查，等待键盘收起动画完成
      setTimeout(() => {
        setKeyboardHeight(0);
        setIsKeyboardOpen(false);
      }, 300);
    };

    document.addEventListener('focusin', handleFocusIn);
    document.addEventListener('focusout', handleFocusOut);

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange);
        window.visualViewport.removeEventListener('scroll', handleViewportChange);
      } else {
        window.removeEventListener('resize', handleViewportChange);
      }
      
      document.removeEventListener('focusin', handleFocusIn);
      document.removeEventListener('focusout', handleFocusOut);
    };
  }, [enabled, handleViewportChange]);

  // 计算调整后的样式
  const adjustedStyle: React.CSSProperties = {
    transform: isKeyboardOpen ? `translateY(-${Math.min(keyboardHeight * 0.3, 200)}px)` : 'translateY(0)',
    transition: 'transform 0.3s ease-out',
  };

  return {
    keyboardHeight,
    isKeyboardOpen,
    adjustedStyle,
  };
};

/**
 * 检查是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 检查是否支持 Visual Viewport API
 */
export const supportsVisualViewport = (): boolean => {
  return typeof window !== 'undefined' && 'visualViewport' in window;
};

/**
 * 获取安全区域信息
 */
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement);
  
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0'),
  };
};

export default useKeyboardAdjustment;
