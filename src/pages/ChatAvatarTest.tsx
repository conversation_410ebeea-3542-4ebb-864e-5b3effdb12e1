/**
 * 聊天头像测试页面
 * 用于验证AI头像的放大和移动效果
 */

import React from 'react';

const ChatAvatarTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6 text-gray-800">
          聊天头像测试
        </h1>
        
        <div className="space-y-8">
          {/* 原始尺寸对比 */}
          <div className="flex flex-col items-center">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">
              原始尺寸 (40px × 40px)
            </h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <div className="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                <img
                  src="/images/robot.png"
                  alt="AI助手"
                  className="w-full h-full object-contain"
                  loading="lazy"
                />
              </div>
            </div>
          </div>

          {/* 修改后的尺寸 */}
          <div className="flex flex-col items-center">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">
              修改后尺寸 (60px × 60px，向下移动30px)
            </h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <div className="rounded-full flex items-center justify-center flex-shrink-0 shadow-sm chat-ai-avatar-container">
                <img
                  src="/images/robot.png"
                  alt="AI助手"
                  className="w-full h-full object-contain"
                  loading="lazy"
                />
              </div>
            </div>
          </div>

          {/* 模拟聊天消息布局 */}
          <div className="flex flex-col items-center">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">
              模拟聊天消息布局
            </h2>
            <div className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4">
              {/* 模拟AI消息 */}
              <div className="flex justify-start p-3 rounded-2xl bg-gray-50/30">
                <div className="flex items-start space-x-3 w-full">
                  {/* 头像 - 使用修改后的样式 */}
                  <div className="rounded-full flex items-center justify-center flex-shrink-0 shadow-sm chat-ai-avatar-container">
                    <img
                      src="/images/robot.png"
                      alt="AI助手"
                      className="w-full h-full object-contain"
                      loading="lazy"
                    />
                  </div>

                  {/* 消息气泡 */}
                  <div className="ai-message-bubble flex-1">
                    <p className="text-sm leading-relaxed">
                      这是一条测试消息，用于验证AI头像的放大和向下移动效果是否正确应用。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex space-x-4 justify-center">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              onClick={() => window.location.reload()}
            >
              重新加载
            </button>
            <button
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
              onClick={() => window.history.back()}
            >
              返回
            </button>
          </div>
        </div>
      </div>

      {/* 说明信息 */}
      <div className="mt-8 bg-gray-800 text-white p-4 rounded-lg max-w-2xl w-full">
        <h3 className="text-lg font-semibold mb-2">修改说明</h3>
        <div className="text-sm space-y-1">
          <p>• 原始尺寸：40px × 40px</p>
          <p>• 修改后尺寸：60px × 60px（放大50%）</p>
          <p>• 向下移动：30px（相当于原尺寸的75%）</p>
          <p>• 增加底部边距：30px（避免与下方内容重叠）</p>
        </div>
      </div>
    </div>
  );
};

export default ChatAvatarTest;
