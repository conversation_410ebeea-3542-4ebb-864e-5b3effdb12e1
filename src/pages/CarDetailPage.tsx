/**
 * 车辆详情页面组件
 * 显示车辆的详细信息、图片轮播和检测报告
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { getVehiclePictures, getVehicleDamage, VehiclePicture, VehicleDamage } from '../utils/chatApi';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';

interface CarDetailPageProps {
  carData: any;
  onBack: () => void;
}

// 使用 React.memo 优化车辆卡片组件
const VehicleCard = React.memo<{ picture: VehiclePicture; isActive: boolean; onClick: () => void }>(
  ({ picture, isActive, onClick }) => (
    <img
      src={picture.pic_url}
      alt={picture.pic_name || '车辆图片'}
      className={`w-full h-full object-cover cursor-pointer transition-opacity ${
        isActive ? 'opacity-100' : 'opacity-70'
      }`}
      onClick={onClick}
    />
  )
);

// 经销商信息卡片组件
const DealerInfoCard = React.memo<{ carData: any }>(({ carData }) => {
  return (
    <div className="bg-white mt-2">
      {/* 经销商信息标题 */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800">经销商信息</h3>
      </div>

      {/* 经销商信息内容 */}
      <div className="p-4">
        <div className="flex items-center">
          {/* 左侧汽车图标 */}
          <div className="flex-shrink-0 mr-4">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h1a3 3 0 006 0h2a3 3 0 006 0h1a2 2 0 002-2V9zM5 13a1 1 0 100 2 1 1 0 000-2zm10 0a1 1 0 100 2 1 1 0 000-2z" />
              </svg>
            </div>
          </div>

          {/* 中间经销商信息 */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-800 mb-1 truncate">
              {carData?.seller_store || '经销商信息'}
            </h3>
            <div className="text-gray-600 text-sm space-y-2">
              {/* 卖家联系方式 - 在上面 */}
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                <span className="text-sm">{carData?.seller_phone || '联系方式'}</span>
              </div>
              {/* 卖家地址 - 在下面 */}
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span className="truncate">{carData?.seller_address || '地址信息'}</span>
              </div>
            </div>
          </div>

          {/* 右侧联系卖家按钮 */}
          <div className="flex-shrink-0 ml-4">
            <button
              className="px-6 py-2 text-white text-sm font-medium rounded-full transition-colors hover:opacity-90"
              style={{ backgroundColor: '#00A26C' }}
              onClick={() => {
                // 这里可以添加联系卖家的逻辑，比如拨打电话或打开联系方式
                if (carData?.seller_phone) {
                  window.location.href = `tel:${carData.seller_phone}`;
                }
              }}
            >
              联系卖家
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

// 损伤标签组件 - 胶囊形状，tag和损伤原因在同一行
const DamageTag = React.memo<{
  damage: VehicleDamage;
  maxTagWidth: number;
}>(({ damage, maxTagWidth }) => {
  // 检查是否为无损状态
  const isNoDamage = (damageColor: string): boolean => {
    const colorLower = damageColor.toLowerCase();
    return colorLower.includes('无损') || colorLower.includes('正常') || colorLower.includes('良好');
  };

  const isDamaged = !isNoDamage(damage.damage_color || '');

  return (
    <div className="mb-3 w-1/2 pr-2 flex items-center">
      {/* 胶囊形状的tag标签，统一宽度，文字大小与损伤原因一致 */}
      <div
        className="inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium text-white flex-shrink-0"
        style={{
          backgroundColor: '#EAB30A',
          minWidth: `${maxTagWidth}px`
        }}
      >
        {damage.item_name}
      </div>
      {/* 损伤原因在tag右侧同一行 */}
      {isDamaged && damage.damage_name && (
        <span className="text-xs text-gray-600 ml-2 flex-1">
          {damage.damage_name}
        </span>
      )}
    </div>
  );
});

// 分类图片轮播组件
const CategoryImageCarousel = React.memo<{
  damages: VehicleDamage[];
  categoryName: string;
  onImageClick: (imageUrls: string[], index: number, damage: VehicleDamage) => void;
}>(({ damages, categoryName: _categoryName, onImageClick }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 触摸滑动状态管理
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // 过滤出有图片的损伤项
  const damagesWithImages = useMemo(() => {
    return damages.filter(damage => damage.url && damage.url.trim().length > 0);
  }, [damages]);

  if (damagesWithImages.length === 0) return null;

  const currentDamage = damagesWithImages[currentIndex];

  const parseImageUrls = (urlString: string | null): string[] => {
    if (!urlString) return [];
    return urlString.split(',').map(url => url.trim()).filter(url => url.length > 0);
  };

  const imageUrls = parseImageUrls(currentDamage.url);
  if (imageUrls.length === 0) return null;

  const goToPrevious = () => {
    setCurrentIndex(prev => (prev === 0 ? damagesWithImages.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setCurrentIndex(prev => (prev === damagesWithImages.length - 1 ? 0 : prev + 1));
  };

  // 触摸滑动处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && damagesWithImages.length > 1) {
      goToNext();
    }
    if (isRightSwipe && damagesWithImages.length > 1) {
      goToPrevious();
    }
  };

  return (
    <div className="mt-4 mb-2">
      <div
        className="relative rounded-lg overflow-hidden"
        style={{ height: '200px' }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <img
          src={imageUrls[0]}
          alt={`${currentDamage.item_name} 损伤图片`}
          className="w-full h-full object-cover cursor-pointer"
          onClick={() => onImageClick(imageUrls, 0, currentDamage)}
        />

        {/* 半透明黑色覆盖层显示部位名称 */}
        <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-center">
          {currentDamage.item_name}
        </div>

        {/* 左右切换按钮 */}
        {damagesWithImages.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={goToNext}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}

        {/* 指示器 */}
        {damagesWithImages.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {damagesWithImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

const CarDetailPage: React.FC<CarDetailPageProps> = ({ carData, onBack }) => {
  const [pictures, setPictures] = useState<VehiclePicture[]>([]);
  const [damages, setDamages] = useState<VehicleDamage[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // 灯箱状态管理
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxSlides, setLightboxSlides] = useState<any[]>([]);

  // 自动轮播状态管理
  const [isAutoPlaying] = useState(true);
  const autoPlayIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 触摸滑动状态管理
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // 展开状态管理（简化，只需要分类展开状态）
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // 获取车辆ID
  const vehicleId = carData?.id || carData?.vehicle_id;

  // 使用 useMemo 优化灯箱数据计算
  const vehicleLightboxSlides = useMemo(() => {
    return pictures.map((picture) => ({
      src: picture.pic_url,
      alt: picture.pic_name || '车辆图片',
      title: picture.pic_name || '车辆图片',
      description: picture.pic_name || '车辆图片'
    }));
  }, [pictures]);

  // 按分类分组损伤数据（包含无损数据）
  const groupedDamages = useMemo(() => {
    return damages.reduce((acc, damage) => {
      const category = damage.category_name?.trim() || '其他';

      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(damage);
      return acc;
    }, {} as Record<string, VehicleDamage[]>);
  }, [damages]);

  // 使用 useCallback 优化回调函数
  const toggleCategoryExpansion = useCallback((categoryName: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  }, []);

  const handleDamageImageClick = useCallback((imageUrls: string[], index: number, damage: VehicleDamage) => {
    const damageImages = imageUrls.map((url, idx) => ({
      src: url,
      alt: `${damage.item_name} 损伤图片 ${idx + 1}`,
      title: `${damage.item_name} - ${damage.damage_color}`
    }));
    setLightboxSlides(damageImages);
    setLightboxIndex(index);
    setLightboxOpen(true);
  }, []);

  useEffect(() => {
    const fetchCarDetails = async () => {
      if (!vehicleId) {
        setError('车辆ID不存在');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const [picturesData, damagesData] = await Promise.all([
          getVehiclePictures(vehicleId),
          getVehicleDamage(vehicleId)
        ]);

        setPictures(picturesData);
        setDamages(damagesData);
      } catch (err) {
        console.error('获取车辆详情失败:', err);
        setError('获取车辆详情失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchCarDetails();
  }, [vehicleId]);

  // 自动轮播生命周期管理
  useEffect(() => {
    if (pictures.length > 1 && isAutoPlaying) {
      startAutoPlay();
    }
    return () => {
      stopAutoPlay();
    };
  }, [pictures.length, isAutoPlaying]);

  // 鼠标悬停时暂停自动轮播
  const handleMouseEnter = () => {
    stopAutoPlay();
  };

  const handleMouseLeave = () => {
    if (isAutoPlaying && pictures.length > 1) {
      startAutoPlay();
    }
  };

  // 格式化函数
  const formatPrice = (price: number) => {
    return `${(price / 10000).toFixed(1)}万元`;
  };

  const formatMileage = (mileage: number) => {
    return `${(mileage / 10000).toFixed(2)}万公里`;
  };

  // 打开车辆图片灯箱
  const openVehicleLightbox = (index: number) => {
    setLightboxSlides(vehicleLightboxSlides);
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  // 自动轮播功能
  const startAutoPlay = () => {
    if (pictures.length <= 1) return;
    autoPlayIntervalRef.current = setInterval(() => {
      setCurrentImageIndex(prevIndex =>
        prevIndex === pictures.length - 1 ? 0 : prevIndex + 1
      );
    }, 3000);
  };

  const stopAutoPlay = () => {
    if (autoPlayIntervalRef.current) {
      clearInterval(autoPlayIntervalRef.current);
      autoPlayIntervalRef.current = null;
    }
  };

  // 手动切换图片
  const goToPrevious = () => {
    setCurrentImageIndex(prevIndex =>
      prevIndex === 0 ? pictures.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentImageIndex(prevIndex =>
      prevIndex === pictures.length - 1 ? 0 : prevIndex + 1
    );
  };

  // 触摸滑动处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && pictures.length > 1) {
      goToNext();
    }
    if (isRightSwipe && pictures.length > 1) {
      goToPrevious();
    }
  };

  // 渲染星级评分
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating); // 直接使用真实数据，不再乘以5
    const hasHalfStar = (rating % 1) >= 0.5; // 直接使用真实数据，不再乘以5

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<span key={i} style={{ color: '#00A26C' }}>★</span>);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<span key={i} style={{ color: '#00A26C' }}>☆</span>);
      } else {
        stars.push(<span key={i} className="text-gray-300">☆</span>);
      }
    }
    return stars;
  };

  // 根据分类名称获取对应的星级评分
  const getCategoryStarRating = (categoryName: string): number => {
    const categoryMap: Record<string, string> = {
      '骨架': 'star_skeleton',
      '外观': 'star_facade',
      '内饰': 'star_interior',
      '工况': 'star_condition',
      '电器': 'star_electric'
    };

    const starKey = categoryMap[categoryName];
    return starKey ? (carData?.[starKey] || 0) : 0;
  };

  // 检查分类是否有损伤（用于判断是否可以展开）
  const categoryHasDamage = (damageList: VehicleDamage[]): boolean => {
    return damageList.some(damage => {
      const color = damage.damage_color?.trim().toLowerCase() || '';
      return !color.includes('无损') && !color.includes('正常') && !color.includes('良好');
    });
  };



  const getDamageColorClass = (damageColor: string): string => {
    const colorLower = damageColor.toLowerCase();
    // "未见明显异常"标注为绿色
    if (colorLower.includes('未见明显异常')) {
      return 'bg-green-500';
    }
    // "一般损伤"标注为黄色
    if (colorLower.includes('一般')) {
      return 'bg-yellow-500';
    }
    // 其他损伤类型标注为红色
    return 'bg-red-500';
  };

  // 计算分类中最大tag宽度
  const calculateMaxTagWidth = (damageList: VehicleDamage[]): number => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return 100; // 默认宽度

    context.font = '12px system-ui, -apple-system, sans-serif'; // 对应text-xs

    let maxWidth = 0;
    damageList.forEach(damage => {
      const color = damage.damage_color?.trim().toLowerCase() || '';
      const isNoDamage = color.includes('无损') || color.includes('正常') || color.includes('良好');
      if (!isNoDamage) {
        const textWidth = context.measureText(damage.item_name || '').width;
        maxWidth = Math.max(maxWidth, textWidth);
      }
    });

    // 加上padding (px-3 = 12px * 2 = 24px)
    return Math.max(maxWidth + 24, 70); // 最小宽度70px
  };

  // 计算一级分类的统计信息（修改为适应新的数据结构）
  const getCategoryStats = (damageList: VehicleDamage[]) => {
    // 检查是否全部为无损
    const hasAnyDamage = damageList.some(damage => {
      const color = damage.damage_color?.trim().toLowerCase() || '';
      return !color.includes('无损') && !color.includes('正常') && !color.includes('良好');
    });

    if (!hasAnyDamage) {
      return [{ color: '未见明显异常', count: damageList.length }];
    }

    const colorCounts: Record<string, number> = {};
    damageList.forEach(damage => {
      const color = damage.damage_color?.trim() || '未知状态';
      // 只统计有损伤的项目
      const colorLower = color.toLowerCase();
      if (!colorLower.includes('无损') && !colorLower.includes('正常') && !colorLower.includes('良好')) {
        colorCounts[color] = (colorCounts[color] || 0) + 1;
      }
    });

    const stats: Array<{ color: string; count: number }> = [];
    Object.entries(colorCounts).forEach(([color, count]) => {
      stats.push({ color, count });
    });
    return stats;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载车辆详情中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="flex items-center px-4 py-3">
          <button
            onClick={onBack}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800 truncate">
            {carData?.vehicle_name || '车辆详情'}
          </h1>
        </div>
      </div>

      {/* 图片轮播区域 */}
      <div className="relative bg-white">
        <div
          className="relative bg-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
          style={{ height: '256px' }}
          onClick={() => pictures.length > 0 && openVehicleLightbox(currentImageIndex)}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {pictures.length > 0 ? (
            <>
              <VehicleCard
                picture={pictures[currentImageIndex]}
                isActive={true}
                onClick={() => openVehicleLightbox(currentImageIndex)}
              />

              {/* 左右切换按钮 */}
              {pictures.length > 1 && (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      goToPrevious();
                    }}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 rounded-full p-2 transition-colors"
                  >
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      goToNext();
                    }}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 rounded-full p-2 transition-colors"
                  >
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}

              {/* pic_name 显示在右上角 */}
              {pictures[currentImageIndex]?.pic_name && (
                <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg text-sm font-medium">
                  {pictures[currentImageIndex].pic_name}
                </div>
              )}

              {/* 放大镜图标 */}
              <div className="absolute bottom-4 right-4 bg-black/50 rounded-full p-2">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <span className="text-gray-500 text-lg">暂无图片</span>
            </div>
          )}
        </div>

        {/* 图片指示器 */}
        {pictures.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {pictures.map((_, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(index);
                }}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* 车辆基本信息 */}
      <div className="bg-white mt-2 p-4">
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          {carData?.vehicle_name || '未知车型'}
        </h2>
      </div>

      {/* 经销商信息卡片 */}
      <DealerInfoCard carData={carData} />

      {/* 基本信息 - 移除折叠功能，缩小文字，添加分割线 */}
      <div className="bg-white mt-2">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">基本信息</h3>
        </div>

        <div className="p-4">
          {/* 使用更小的文字和分割线 */}
          <div className="space-y-3">
              {/* 第一行 */}
              <div className="flex justify-between items-start">
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.price ? formatPrice(carData.price) : '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>在售价格</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.model_year ? `${carData.model_year}年${carData.registration_month || '08'}月` : '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>首次上牌</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.display_mileage ? formatMileage(carData.display_mileage) : '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>表显里程</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.usage_nature || '非营运'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>使用性质</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.city || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>车辆所在地</div>
                </div>
              </div>

              {/* 第二行 */}
              <div className="flex justify-between items-start">
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.transmission_type || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>变速箱</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.swept_volume ? `${carData.swept_volume}T` : '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>排气量</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.fuel_type || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>燃油类型</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.body_color || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>车身颜色</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.production_date || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>出厂日期</div>
                </div>
              </div>

              {/* 第三行 */}
              <div className="flex justify-between items-start">
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.emission_standard || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>发动机号</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.environmental_standard || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>环保标准</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.max_power || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>最大功率</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.interior_color || '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>内饰颜色</div>
                </div>
                <div className="w-px h-6 bg-gray-300 mx-1"></div>
                <div className="flex-1 text-center">
                  <div className="text-xs font-bold text-gray-800 leading-tight">
                    {carData?.seat_number ? `${carData.seat_number}座` : '-'}
                  </div>
                  <div className="text-gray-500" style={{ fontSize: '10px' }}>座位数</div>
                </div>
              </div>
            </div>
          </div>
      </div>

      {/* 检测报告 */}
      <div className="bg-white mt-2 mb-4">
        {/* 检测报告标题 - 添加图例说明 */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">检测报告</h3>
          {/* 图例说明 */}
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
              <span>未见明显异常</span>
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
              <span>一般损伤</span>
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
              <span>其他损伤</span>
            </div>
          </div>
        </div>

        {/* 损伤详情 */}
        <div className="p-4">
          {Object.entries(groupedDamages).map(([categoryName, damageList]) => {
            const categoryStats = getCategoryStats(damageList);
            const isCategoryExpanded = expandedCategories[categoryName];
            const hasDamage = categoryHasDamage(damageList);

            return (
              <div key={categoryName} className="mb-1">
                {/* 一级分类：category_name */}
                <div
                  className={`flex items-center justify-between py-3 px-2 border-b border-gray-200 transition-colors ${
                    hasDamage ? 'cursor-pointer hover:bg-gray-50' : 'cursor-not-allowed'
                  }`}
                  onClick={() => hasDamage && toggleCategoryExpansion(categoryName)}
                >
                  <div className="flex items-center flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-800 truncate">
                      {categoryName}
                    </h3>
                    {/* 在分类名称右侧显示星星评分 */}
                    <div className="flex items-center ml-3">
                      {renderStars(getCategoryStarRating(categoryName))}
                      <span className="ml-2 text-sm text-gray-600">
                        {getCategoryStarRating(categoryName).toFixed(1)}
                      </span>
                    </div>
                  </div>

                  {/* 右侧显示圆点数字和折叠按钮 */}
                  <div className="flex items-center space-x-3">
                    {/* 只显示圆点和数字，不显示文字，未见明显异常不显示数字 */}
                    <div className="flex items-center space-x-2">
                      {categoryStats.map((stat) => (
                        <span
                          key={stat.color}
                          className="inline-flex items-center text-xs text-gray-600"
                        >
                          <span
                            className={`w-2 h-2 rounded-full mr-1 flex-shrink-0 ${getDamageColorClass(stat.color)}`}
                          />
                          {/* 未见明显异常不显示数字 */}
                          {stat.color !== '未见明显异常' && <span>{stat.count}</span>}
                        </span>
                      ))}
                    </div>

                    {/* 只有有损伤的分类才显示可点击的展开按钮 */}
                    {hasDamage && (
                      <svg
                        className={`w-5 h-5 transform transition-transform text-gray-500 flex-shrink-0 ${
                          isCategoryExpanded ? 'rotate-90' : ''
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    )}
                  </div>
                </div>

                {/* 一级分类内容 - 只有有损伤的分类才可以展开，全宽显示 */}
                {hasDamage && isCategoryExpanded && (
                  <div className="mt-3 px-4">
                    {/* 显示所有损伤标签，使用flex布局每行两个 */}
                    <div className="flex flex-wrap mb-4">
                      {(() => {
                        const damagedItems = damageList.filter(damage => {
                          const color = damage.damage_color?.trim().toLowerCase() || '';
                          return !color.includes('无损') && !color.includes('正常') && !color.includes('良好');
                        });
                        const maxTagWidth = calculateMaxTagWidth(damagedItems);

                        return damagedItems.map((damage) => (
                          <DamageTag
                            key={damage.id}
                            damage={damage}
                            maxTagWidth={maxTagWidth}
                          />
                        ));
                      })()}
                    </div>

                    {/* 显示图片轮播组件 */}
                    <CategoryImageCarousel
                      damages={damageList}
                      categoryName={categoryName}
                      onImageClick={handleDamageImageClick}
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* 图片灯箱 */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={lightboxSlides}
        index={lightboxIndex}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
        carousel={{
          finite: lightboxSlides.length <= 1,
        }}
        render={{
          buttonPrev: lightboxSlides.length <= 1 ? () => null : undefined,
          buttonNext: lightboxSlides.length <= 1 ? () => null : undefined,
        }}
        styles={{
          container: { backgroundColor: "rgba(0, 0, 0, .9)" },
        }}
      />
    </div>
  );
};

export default CarDetailPage;
