/**
 * PC端不支持页面
 * 当检测到PC端访问时显示此页面
 */

import React from 'react';

const PCNotSupported: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg 
              className="w-8 h-8 text-red-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            服务不可用
          </h1>
          <p className="text-lg text-red-600 font-medium">
            当前服务不面向 PC 用户开放！
          </p>
        </div>
        
        <div className="space-y-4 text-gray-600">
          <p>
            此服务专为移动设备优化，请使用手机或平板电脑访问。
          </p>
          
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2">
              如何访问：
            </h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 使用手机浏览器打开此链接</li>
              <li>• 使用平板电脑访问</li>
              <li>• 扫描二维码（如有提供）</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            如有疑问，请联系技术支持
          </p>
        </div>
      </div>
    </div>
  );
};

export default PCNotSupported;
