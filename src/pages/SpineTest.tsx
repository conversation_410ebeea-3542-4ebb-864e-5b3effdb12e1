/**
 * Spine 动画测试页面
 * 用于验证 Spine 动画是否正常工作
 */

import React, { useState } from 'react';
import SpineRobot from '../components/SpineRobot';

const SpineTest: React.FC = () => {
  const [currentAnimation, setCurrentAnimation] = useState<'think' | 'listen' | 'talk'>('think');

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6 text-gray-800">
          Spine 动画测试
        </h1>

        <div className="flex flex-col items-center space-y-6">
          {/* 动画切换按钮 */}
          <div className="flex space-x-2 mb-4">
            <button
              onClick={() => setCurrentAnimation('think')}
              className={`px-4 py-2 rounded ${currentAnimation === 'think' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              Think
            </button>
            <button
              onClick={() => setCurrentAnimation('listen')}
              className={`px-4 py-2 rounded ${currentAnimation === 'listen' ? 'bg-green-500 text-white' : 'bg-gray-200'}`}
            >
              Listen
            </button>
            <button
              onClick={() => setCurrentAnimation('talk')}
              className={`px-4 py-2 rounded ${currentAnimation === 'talk' ? 'bg-red-500 text-white' : 'bg-gray-200'}`}
            >
              Talk (图片)
            </button>
          </div>

          {/* Spine 动画测试 */}
          <div className="flex flex-col items-center">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">
              当前动画: {currentAnimation}
            </h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <SpineRobot
                animation={currentAnimation}
                loop={true}
                timeScale={1.0}
                onError={(error) => {
                  console.error('Spine animation error:', error);
                  alert(`动画加载失败: ${error.message}`);
                }}
                onLoad={() => {
                  console.log(`${currentAnimation} animation loaded successfully!`);
                }}
              />
            </div>
          </div>

          {/* 静态图片对比 */}
          <div className="flex flex-col items-center">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">
              静态图片对比
            </h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <img
                src="/images/robot.png"
                alt="静态机器人"
                className="robot-avatar-header"
                loading="lazy"
              />
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex space-x-4">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              onClick={() => window.location.reload()}
            >
              重新加载
            </button>
            <button
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
              onClick={() => window.history.back()}
            >
              返回
            </button>
          </div>
        </div>
      </div>

      {/* 调试信息 */}
      <div className="mt-8 bg-gray-800 text-white p-4 rounded-lg max-w-2xl w-full">
        <h3 className="text-lg font-semibold mb-2">调试信息</h3>
        <div className="text-sm space-y-1">
          <p>• Spine 资源路径: /spine/think/ROBOT.json</p>
          <p>• Atlas 路径: /spine/think/ROBOT.atlas</p>
          <p>• 动画名称: think</p>
          <p>• 请检查浏览器控制台查看详细日志</p>
        </div>
      </div>
    </div>
  );
};

export default SpineTest;
