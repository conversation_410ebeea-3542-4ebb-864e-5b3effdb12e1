/**
 * AI 对话页面
 * 类似 OpenAI 手机端的聊天界面
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { preloadAndCacheImage, getImageLoadingState } from '../utils/imageCache';
import { flushSync } from 'react-dom';
import { Button } from '@heroui/button';
import { sendChatMessage, getSummaryTopic, sendVoiceToASR } from '../utils/chatApi';
import {
  getCurrentConversationId,
  setCurrentConversationId as setStorageConversationId,
  startNewConversation,
  updateConversation,
  incrementMessageCount,
  getGroupedConversations,
  getConversationMessages,
  addMessageToConversation,
  updateMessageInConversation,
  deleteConversation,
  addRecommendationToConversation,
  type StoredMessage
} from '../utils/conversationStorage';
import { createVoiceRecorder, VoiceRecorder } from '../utils/voiceRecorder';
import { synthesizeTextControllable, isTTSAvailable, ControllableAudio, requestAudioPermission, hasAudioPermission, forceRequestAudioPermission } from '../utils/ttsService';
import { getAvatar } from '../utils/avatarUtils';
import { getNickname, getPhone, getUserProfile } from '../utils/userInfoUtils';
import { refreshUserProfile } from '../utils/auth';

import WordCloudHeader from '../components/WordCloudHeader';
import MarkdownErrorBoundary from '../components/MarkdownErrorBoundary';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';

// Markdown 渲染相关导入
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';


interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  carRecommendations?: any[];
  activeTaskType?: string;
  slots?: Record<string, any>;
}

// Markdown 渲染组件
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  return (
    <div className="markdown-content">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
        // 代码块渲染
        code({ node, inline, className, children, ...props }: any) {
          const match = /language-(\w+)/.exec(className || '');
          return !inline && match ? (
            <SyntaxHighlighter
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              className="rounded-lg text-sm"
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          ) : (
            <code
              className="bg-gray-100 text-red-600 px-1 py-0.5 rounded text-sm font-mono"
              {...props}
            >
              {children}
            </code>
          );
        },
        // 段落样式 - 修复DOM嵌套问题
        p({ children }) {
          return <div className="text-sm leading-relaxed mb-2 last:mb-0">{children}</div>;
        },
        // 标题样式
        h1({ children }) {
          return <h1 className="text-lg font-bold mb-2 text-gray-800">{children}</h1>;
        },
        h2({ children }) {
          return <h2 className="text-base font-semibold mb-2 text-gray-800">{children}</h2>;
        },
        h3({ children }) {
          return <h3 className="text-sm font-semibold mb-1 text-gray-700">{children}</h3>;
        },
        // 列表样式
        ul({ children }) {
          return <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>;
        },
        ol({ children }) {
          return <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>;
        },
        li({ children }) {
          return <li className="text-sm leading-relaxed">{children}</li>;
        },
        // 链接样式
        a({ href, children }) {
          return (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {children}
            </a>
          );
        },
        // 引用块样式
        blockquote({ children }) {
          return (
            <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-2">
              {children}
            </blockquote>
          );
        },
        // 表格样式 - 修复DOM嵌套问题
        table({ children }) {
          return (
            <table className="min-w-full border border-gray-200 rounded-lg overflow-x-auto mb-2 block">
              {children}
            </table>
          );
        },
        thead({ children }) {
          return <thead className="bg-gray-50">{children}</thead>;
        },
        th({ children }) {
          return (
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase border-b border-gray-200">
              {children}
            </th>
          );
        },
        td({ children }) {
          return (
            <td className="px-3 py-2 text-sm text-gray-900 border-b border-gray-200">
              {children}
            </td>
          );
        },
        // 强调样式
        strong({ children }) {
          return <strong className="font-semibold text-gray-900">{children}</strong>;
        },
        em({ children }) {
          return <em className="italic text-gray-700">{children}</em>;
        }
      }}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  };

interface ChatPageProps {
  onLogout: () => void;
  onCarDetailClick?: (carData: any, scrollPosition?: number) => void;
  onSettingsClick: () => void;
  onCarComparisonClick?: (cars: any[], scrollPosition?: number) => void;
  restoreScrollPosition?: number | null;
  onScrollPositionRestored?: () => void;
  autoLoadConversationId?: string | null; // 🔥 新增：自动加载的对话ID
  onAutoLoadCompleted?: () => void; // 🔥 新增：自动加载完成回调
}

const ChatPage: React.FC<ChatPageProps> = ({
  onLogout: _,
  onCarDetailClick,
  onSettingsClick,
  onCarComparisonClick,
  restoreScrollPosition,
  onScrollPositionRestored,
  autoLoadConversationId, // 🔥 新增：自动加载的对话ID
  onAutoLoadCompleted // 🔥 新增：自动加载完成回调
}) => {

  const [messages, setMessages] = useState<Message[]>([]);

  // 消息转换工具函数
  const convertStoredToMessage = (stored: StoredMessage): Message => ({
    ...stored,
    timestamp: new Date(stored.timestamp)
  });

  const convertMessageToStored = (message: Message): StoredMessage => ({
    ...message,
    timestamp: message.timestamp.getTime()
  });
  const [inputMode, setInputMode] = useState<'voice' | 'text'>('voice');
  const [textInput, setTextInput] = useState('');
  const [textareaHeight, setTextareaHeight] = useState(48); // 默认高度48px
  const [isRecording, setIsRecording] = useState(false);
  const [recordingCanceled, setRecordingCanceled] = useState(false);
  const [dragStartY, setDragStartY] = useState<number | null>(null);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);
  const [currentASRController, setCurrentASRController] = useState<AbortController | null>(null);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [voiceRecorder, setVoiceRecorder] = useState<VoiceRecorder | null>(null);
  const [voiceStartCanceled, setVoiceStartCanceled] = useState(false); // 标记用户是否在录音开始前松手
  const [voicePermissionGranted, setVoicePermissionGranted] = useState<boolean | null>(null);
  const [voicePermissionTipClosed, setVoicePermissionTipClosed] = useState<boolean>(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [sidebarWasOpened, setSidebarWasOpened] = useState(false); // 跟踪侧边栏是否曾经打开过
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isAIResponding, setIsAIResponding] = useState(false);
  // const [currentAIMessage, setCurrentAIMessage] = useState(''); // 暂时注释，只使用setter
  const [conversationHistory, setConversationHistory] = useState({
    today: [] as any[],
    yesterday: [] as any[],
    thisWeek: [] as any[],
    older: [] as any[]
  });

  const [activeTaskType, setActiveTaskType] = useState<string | null>(null);
  const [slotsData, setSlotsData] = useState<Record<string, any> | null>(null);

  // 聊天历史分页状态
  const [historyPagination, setHistoryPagination] = useState({
    page: 1,
    limit: 50,
    hasMore: false,
    loading: false,
    error: null as string | null
  });

  // 历史记录同步状态
  const [historySyncStatus, setHistorySyncStatus] = useState({
    lastSyncTime: null as number | null,
    syncing: false,
    hasNewData: false
  });

  // 侧边栏下拉刷新状态 - 优化版本
  const [pullRefreshState, setPullRefreshState] = useState({
    isPulling: false,
    pullDistance: 0,
    isRefreshing: false,
    canRefresh: false
  });

  // 使用ref存储实时动画状态，避免频繁的状态更新
  const pullRefreshRef = useRef({
    isPulling: false,
    pullDistance: 0,
    canRefresh: false,
    animationId: null as number | null,
    lastUpdateTime: 0
  });


  // 灯箱状态管理
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxSlides, setLightboxSlides] = useState<any[]>([]);

  // TTS自动播放相关状态
  const [isAutoPlayEnabled, setIsAutoPlayEnabled] = useState(() => {
    const saved = localStorage.getItem('tts_auto_play_enabled');
    return saved ? JSON.parse(saved) : false;
  });
  const [isTTSPlaying, setIsTTSPlaying] = useState(false);
  // const [lastTTSMessageId, setLastTTSMessageId] = useState<string | null>(null); // 防止重复播放 - 暂时注释

  // 使用ref确保TTS防重复检查是同步的
  const ttsStateRef = useRef({
    isPlaying: false,
    lastMessageId: null as string | null,
    pendingCalls: new Set<string>() // 记录正在处理的消息ID
  });

  // 新建对话按钮防重复点击状态
  const [isCreatingNewChat, setIsCreatingNewChat] = useState(false);

  // 删除对话确认对话框状态
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    isOpen: boolean;
    conversationId: string | null;
    isDeleting: boolean;
  }>({
    isOpen: false,
    conversationId: null,
    isDeleting: false
  });

  // 消息播放状态管理
  type MessagePlayState = 'idle' | 'loading' | 'playing';
  const [messagePlayStates, setMessagePlayStates] = useState<Record<string, MessagePlayState>>({});
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<ControllableAudio | null>(null);
  const [currentPlayingMessageId, setCurrentPlayingMessageId] = useState<string | null>(null);

  // 用户信息状态
  const [userAvatar, setUserAvatar] = useState<string | null>(null);
  const [userNickname, setUserNickname] = useState<string>('');
  const [userPhone, setUserPhone] = useState<string>('');
  const [userProfile, setUserProfile] = useState<any>(null); // 缓存的用户资料

  const voiceButtonRef = useRef<HTMLButtonElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // 加载指定会话的所有数据（每次都从服务器实时获取）
  const loadConversation = useCallback(async (conversationId: string, forceRefresh: boolean = false) => {
    try {
      console.log(`🔄 从服务器实时加载会话 ${conversationId}...`);

      // 重置分页状态
      setHistoryPagination({
        page: 1,
        limit: 50,
        hasMore: false,
        loading: false,
        error: null
      });

      let finalMessages: any[] = [];

      // 每次都从服务器获取最新数据
      try {
        const { loadConversationFromServer } = await import('../utils/conversationStorage');
        const serverMessages = await loadConversationFromServer(conversationId, 1, 50, true);
        finalMessages = serverMessages.map(convertStoredToMessage);

        // 更新分页状态
        setHistoryPagination(prev => ({
          ...prev,
          hasMore: serverMessages.length >= 50 // 如果返回满页，可能还有更多
        }));

        console.log(`✅ 从服务器实时加载会话 ${conversationId}，消息数量: ${finalMessages.length}`);
      } catch (serverError) {
        console.warn('从服务器加载失败:', serverError);
        finalMessages = []; // 不再使用本地缓存，失败时显示空列表
      }

      setMessages(finalMessages);

      // 恢复任务类型和slots数据（从最后一条AI消息中获取）
      const storedMessages = getConversationMessages(conversationId);
      const lastAIMessage = storedMessages.filter(msg => msg.type === 'ai').pop();
      if (lastAIMessage && lastAIMessage.activeTaskType) {
        setActiveTaskType(lastAIMessage.activeTaskType);
        if (lastAIMessage.slots) {
          setSlotsData(lastAIMessage.slots);
        }
      } else {
        setActiveTaskType(null);
        setSlotsData(null);
      }

      console.log(`已加载会话 ${conversationId}，消息数量: ${finalMessages.length}`);
    } catch (error) {
      console.error('加载会话失败:', error);
      setMessages([]);
      setActiveTaskType(null);
      setSlotsData(null); // 确保在加载失败时也清空slots数据
    }
  }, [currentConversationId, historySyncStatus.lastSyncTime]);

  // 刷新用户信息的函数
  const refreshUserInfo = useCallback(async () => {
    // 首先设置本地存储的基本信息
    setUserAvatar(getAvatar());
    setUserNickname(getNickname());
    setUserPhone(getPhone());

    // 尝试获取缓存的用户资料
    const cachedProfile = getUserProfile();
    if (cachedProfile) {
      setUserProfile(cachedProfile);
      // 如果有缓存的用户资料，优先使用其中的信息
      if (cachedProfile.username) {
        setUserNickname(cachedProfile.username);
      }
      if (cachedProfile.phone) {
        setUserPhone(cachedProfile.phone);
      }
      // 如果有服务器头像URL，优先使用
      if (cachedProfile.avatar_url) {
        setUserAvatar(`https://api.st.vup.tools/api/v1/user/avatar/${cachedProfile.avatar_url}`);
      }
      console.log('📋 使用缓存的用户资料信息');
    } else {
      // 如果没有缓存，尝试从服务器获取
      try {
        const result = await refreshUserProfile(false); // 不强制刷新，优先使用缓存
        if (result.success && result.userProfile) {
          setUserProfile(result.userProfile);
          if (result.userProfile.username) {
            setUserNickname(result.userProfile.username);
          }
          if (result.userProfile.phone) {
            setUserPhone(result.userProfile.phone);
          }
          // 如果有服务器头像URL，优先使用
          if (result.userProfile.avatar_url) {
            setUserAvatar(`https://api.st.vup.tools/api/v1/user/avatar/${result.userProfile.avatar_url}`);
          }
          console.log('✅ 从服务器获取用户资料成功');
        }
      } catch (error) {
        console.error('❌ 获取用户资料失败:', error);
        // 失败时保持使用本地存储的信息
      }
    }
  }, []);

  // 加载会话历史（每次都从服务器实时获取）
  const loadConversationHistory = useCallback(async (forceRefresh: boolean = false) => {
    try {
      console.log('🔄 从服务器实时获取会话历史...');
      // 每次都从服务器实时获取
      const history = await getGroupedConversations(true);
      setConversationHistory(history);
      console.log('✅ 会话历史加载成功');
    } catch (error) {
      console.error('❌ 加载会话历史失败:', error);
      // 保持当前状态，不更新
    }
  }, []);

  // 🔥 处理自动加载对话（登录后自动加载最近对话或新对话）
  useEffect(() => {
    if (autoLoadConversationId) {
      console.log('🔄 自动加载指定对话:', autoLoadConversationId);
      setCurrentConversationId(autoLoadConversationId);
      setStorageConversationId(autoLoadConversationId);
      loadConversation(autoLoadConversationId, true); // 强制刷新以获取最新数据

      // 通知父组件自动加载完成
      if (onAutoLoadCompleted) {
        onAutoLoadCompleted();
      }
    }
  }, [autoLoadConversationId]); // 🔥 移除loadConversation和onAutoLoadCompleted依赖，避免重复触发

  // 初始化会话（仅在没有自动加载对话时执行）
  useEffect(() => {
    // 如果有自动加载对话ID，跳过常规初始化
    if (autoLoadConversationId) {
      return;
    }

    const existingConversationId = getCurrentConversationId();
    if (existingConversationId) {
      setCurrentConversationId(existingConversationId);
      loadConversation(existingConversationId);
    }
  }, [autoLoadConversationId]); // 🔥 移除loadConversation依赖，避免重复触发

  // 加载用户信息
  useEffect(() => {
    refreshUserInfo();

    // 监听storage变化，当用户在设置页面修改信息时自动更新
    const handleStorageChange = () => {
      refreshUserInfo();
    };

    // 监听页面可见性变化，当从设置页面返回时刷新用户信息
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshUserInfo();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []); // refreshUserInfo是稳定的useCallback，移除依赖避免不必要的重新渲染

  // 初始化时加载会话历史（仅一次，使用缓存）
  useEffect(() => {
    loadConversationHistory(false); // 默认使用缓存
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 监听侧边栏状态变化（移除自动刷新，改为缓存优先）
  useEffect(() => {
    if (isSidebarOpen) {
      // 侧边栏打开时，标记为已打开过
      setSidebarWasOpened(true);
      // 不再自动刷新，使用缓存数据
      console.log('📋 侧边栏打开，使用缓存数据');
    } else if (sidebarWasOpened) {
      // 侧边栏关闭，重置状态但不刷新
      console.log('📋 侧边栏关闭，保持缓存数据');
      setSidebarWasOpened(false); // 重置状态
    }
  }, [isSidebarOpen, sidebarWasOpened]);

  // 初始化语音录制器并检查权限
  useEffect(() => {
    // 从 localStorage 读取语音权限提示关闭状态
    const tipClosed = localStorage.getItem('voicePermissionTipClosed') === 'true';
    setVoicePermissionTipClosed(tipClosed);

    const initVoiceRecorder = async () => {
      try {
        console.log('ChatPage: 初始化语音录制器并检查权限');

        // Safari 兼容性检查
        if (typeof AbortController === 'undefined') {
          console.warn('ChatPage: AbortController 不支持，语音转文字取消功能将不可用');
        }

        // 静默检查支持性，不设置错误信息
        if (!VoiceRecorder.isSupported()) {
          console.warn('ChatPage: 浏览器不支持语音录制');
          setVoicePermissionGranted(false);
          setVoiceError(null);
          return;
        }

        // 创建录制器实例
        const recorder = createVoiceRecorder({
          maxDuration: 60000,
        });

        setVoiceRecorder(recorder);
        setVoiceError(null); // 确保没有错误信息

        // 自动检查麦克风权限状态
        console.log('ChatPage: 检查麦克风权限状态...');
        const permissionState = await recorder.checkPermission();
        console.log('ChatPage: 权限状态:', permissionState);

        if (permissionState === 'granted') {
          // 权限已授权，预先初始化MediaRecorder以避免时序问题
          console.log('ChatPage: 权限已授权，预先初始化MediaRecorder...');
          try {
            await recorder.initialize();
            setVoicePermissionGranted(true);
            console.log('ChatPage: MediaRecorder预初始化完成，可直接使用');
          } catch (error) {
            console.warn('ChatPage: MediaRecorder预初始化失败，将在用户点击时重试:', error);
            // Safari中即使预初始化失败，也不一定意味着权限被拒绝
            // 设置为null，让用户点击时再次尝试
            setVoicePermissionGranted(null);
          }
        } else if (permissionState === 'denied') {
          setVoicePermissionGranted(false);
          console.log('ChatPage: 麦克风权限被明确拒绝');
        } else {
          // prompt状态，设置为null，等待用户交互
          setVoicePermissionGranted(null);
          console.log('ChatPage: 麦克风权限未确定，需要用户点击申请');
        }

        console.log('ChatPage: 语音录制器初始化完成');
      } catch (error) {
        console.error('ChatPage: 语音录制器初始化失败:', error);
        setVoicePermissionGranted(false);
        setVoiceError(null);
      }
    };

    initVoiceRecorder();
  }, []);

  // 清理语音录制器
  useEffect(() => {
    return () => {
      if (voiceRecorder) {
        voiceRecorder.cleanup();
      }
    };
  }, [voiceRecorder]);

  // 清理TTS播放器 - 页面卸载时停止所有音频播放
  useEffect(() => {
    return () => {
      console.log('TTS: 页面卸载，清理所有音频播放');
      // 停止当前播放的音频
      if (currentPlayingAudio) {
        currentPlayingAudio.destroy();
      }
      // 清理ref状态
      ttsStateRef.current.isPlaying = false;
      ttsStateRef.current.lastMessageId = null;
      ttsStateRef.current.pendingCalls.clear();
    };
  }, [currentPlayingAudio]);

  // 语音错误提示自动消失
  useEffect(() => {
    if (voiceError) {
      console.log('ChatPage: 语音错误提示显示，2秒后自动消失');
      const timer = setTimeout(() => {
        setVoiceError(null);
        console.log('ChatPage: 语音错误提示自动消失');
      }, 2000);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [voiceError]);

  // 聊天历史分页加载功能
  const loadMoreHistory = useCallback(async () => {
    if (!currentConversationId || historyPagination.loading || !historyPagination.hasMore) {
      return;
    }

    setHistoryPagination(prev => ({ ...prev, loading: true, error: null }));

    try {
      // 动态导入以避免循环依赖
      const { getChatHistoryAPI } = await import('../utils/chatApi');
      const { convertChatHistoryToStored } = await import('../utils/conversationStorage');

      const nextPage = historyPagination.page + 1;
      console.log(`🔄 加载会话 ${currentConversationId} 第 ${nextPage} 页历史记录...`);

      const response = await getChatHistoryAPI(currentConversationId, nextPage, historyPagination.limit);

      if (response.success && response.data) {
        const { history, pagination } = response.data;

        // 转换服务器数据为本地格式
        const newMessages = convertChatHistoryToStored(history);

        if (newMessages.length > 0) {
          // 合并新消息到现有消息列表
          const currentMessages = messages;
          const mergedMessages = [...newMessages, ...currentMessages];

          // 去重并按时间排序
          const uniqueMessages = mergedMessages.filter((msg, index, array) =>
            array.findIndex(m => m.id === msg.id) === index
          ).sort((a, b) => {
            const aTime = a.timestamp instanceof Date ? a.timestamp.getTime() : a.timestamp;
            const bTime = b.timestamp instanceof Date ? b.timestamp.getTime() : b.timestamp;
            return aTime - bTime;
          });

          setMessages(uniqueMessages.map(msg => ({
            ...msg,
            timestamp: msg.timestamp instanceof Date ? msg.timestamp : new Date(msg.timestamp)
          })));

          console.log(`✅ 成功加载第 ${nextPage} 页历史记录，新增 ${newMessages.length} 条消息`);
        }

        // 更新分页状态
        setHistoryPagination(prev => ({
          ...prev,
          page: nextPage,
          hasMore: pagination.has_more,
          loading: false
        }));
      } else {
        throw new Error(response.message || '加载历史记录失败');
      }
    } catch (error) {
      console.error('加载更多历史记录失败:', error);
      setHistoryPagination(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '加载失败'
      }));
    }
  }, [currentConversationId, historyPagination.loading, historyPagination.hasMore, historyPagination.page, historyPagination.limit, messages]);

  // 滚动监听，实现向上滚动加载更多
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    const handleScroll = () => {
      const { scrollTop } = chatContainer;

      // 检查是否滚动到顶部附近（距离顶部小于100px）
      if (scrollTop < 100 && historyPagination.hasMore && !historyPagination.loading) {
        console.log('🔄 检测到滚动到顶部，触发加载更多历史记录');
        loadMoreHistory();
      }
    };

    chatContainer.addEventListener('scroll', handleScroll);
    return () => chatContainer.removeEventListener('scroll', handleScroll);
  }, [loadMoreHistory, historyPagination.hasMore, historyPagination.loading]);

  // 下拉刷新功能
  const handlePullRefresh = useCallback(async () => {
    if (pullRefreshState.isRefreshing) return;

    setPullRefreshState(prev => ({ ...prev, isRefreshing: true }));

    try {
      console.log('🔄 用户触发下拉刷新会话历史...');
      await loadConversationHistory(true); // 强制刷新
      console.log('✅ 下拉刷新完成');
    } catch (error) {
      console.error('❌ 下拉刷新失败:', error);
    } finally {
      // 延迟重置状态，让用户看到完成反馈
      setTimeout(() => {
        setPullRefreshState({
          isPulling: false,
          pullDistance: 0,
          isRefreshing: false,
          canRefresh: false
        });
      }, 500);
    }
  }, [pullRefreshState.isRefreshing, loadConversationHistory]);

  // 触摸事件处理
  const touchStartY = useRef<number>(0);
  const sidebarScrollRef = useRef<HTMLDivElement>(null);

  // 优化的触摸事件处理 - 使用requestAnimationFrame
  const updatePullRefreshUI = useCallback(() => {
    const { isPulling, pullDistance, canRefresh } = pullRefreshRef.current;

    setPullRefreshState(prev => ({
      ...prev,
      isPulling,
      pullDistance,
      canRefresh
    }));
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    touchStartY.current = touch.clientY;

    // 重置ref状态
    pullRefreshRef.current = {
      ...pullRefreshRef.current,
      isPulling: false,
      pullDistance: 0,
      canRefresh: false
    };

    // 取消之前的动画
    if (pullRefreshRef.current.animationId) {
      cancelAnimationFrame(pullRefreshRef.current.animationId);
      pullRefreshRef.current.animationId = null;
    }

    updatePullRefreshUI();
  }, [updatePullRefreshUI]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const currentY = touch.clientY;
    const deltaY = currentY - touchStartY.current;
    const scrollContainer = sidebarScrollRef.current;

    // 只有在列表顶部且向下拖拽时才触发下拉刷新
    if (scrollContainer && scrollContainer.scrollTop === 0 && deltaY > 0) {
      e.preventDefault(); // 阻止默认滚动行为

      const pullDistance = Math.min(deltaY * 0.5, 80); // 限制最大下拉距离
      const canRefresh = pullDistance > 60; // 超过60px可以刷新

      // 更新ref状态
      pullRefreshRef.current = {
        ...pullRefreshRef.current,
        isPulling: true,
        pullDistance,
        canRefresh
      };

      // 使用requestAnimationFrame进行节流更新
      const now = Date.now();
      if (now - pullRefreshRef.current.lastUpdateTime > 16) { // 约60fps
        pullRefreshRef.current.lastUpdateTime = now;

        if (pullRefreshRef.current.animationId) {
          cancelAnimationFrame(pullRefreshRef.current.animationId);
        }

        pullRefreshRef.current.animationId = requestAnimationFrame(() => {
          updatePullRefreshUI();
          pullRefreshRef.current.animationId = null;
        });
      }
    }
  }, [updatePullRefreshUI]);

  const handleTouchEnd = useCallback(() => {
    // 取消任何进行中的动画
    if (pullRefreshRef.current.animationId) {
      cancelAnimationFrame(pullRefreshRef.current.animationId);
      pullRefreshRef.current.animationId = null;
    }

    if (pullRefreshRef.current.canRefresh && !pullRefreshState.isRefreshing) {
      handlePullRefresh();
    } else {
      // 重置状态
      pullRefreshRef.current = {
        ...pullRefreshRef.current,
        isPulling: false,
        pullDistance: 0,
        canRefresh: false
      };

      updatePullRefreshUI();
    }
  }, [pullRefreshState.isRefreshing, handlePullRefresh, updatePullRefreshUI]);

  // 清理函数 - 组件卸载时取消动画
  useEffect(() => {
    return () => {
      if (pullRefreshRef.current.animationId) {
        cancelAnimationFrame(pullRefreshRef.current.animationId);
      }
    };
  }, []);

  // 滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 获取当前滚动位置 - 使用useCallback优化
  const getCurrentScrollPosition = useCallback(() => {
    return chatContainerRef.current?.scrollTop || 0;
  }, []);

  // 恢复滚动位置
  const restoreScrollToPosition = (position: number) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = position;
    }
  };

  // 处理车辆推荐数据 - 使用缓存避免重复更新
  const messageUpdateCache = useRef(new Map<string, { carData?: any[], taskType?: string, slotsData?: Record<string, any> }>());

  const handleCarRecommendation = useCallback((delta: string, aiMessageId?: string) => {
    try {
      let hasData = false;
      let carData: any[] | null = null;
      let taskType: string | null = null;

      // 检查是否是车辆推荐数据（数组格式）
      if (delta.startsWith('[{') && delta.includes('"vehicle_name":')) {
        carData = JSON.parse(delta);
        if (Array.isArray(carData) && carData.length > 0) {
          console.log(`解析到 ${carData.length} 条车辆推荐`);
          hasData = true;
        }
      }

      // 检查是否是任务类型数据
      if (delta.includes('"active_task_type":')) {
        const taskData = JSON.parse(delta);
        if (taskData.active_task_type) {
          taskType = taskData.active_task_type;
          setActiveTaskType(taskType);
          hasData = true;

          // 同时解析slots数据
          if (taskData.slots) {
            setSlotsData(taskData.slots);
            console.log('解析到slots数据:', taskData.slots);
          }
        }
      }

      // 如果有数据且有AI消息ID，存储数据但不立即更新视图
      if (hasData && aiMessageId && currentConversationId) {
        // 获取或创建缓存条目
        let cached = messageUpdateCache.current.get(aiMessageId);
        if (!cached) {
          cached = {};
          messageUpdateCache.current.set(aiMessageId, cached);
        }

        // 更新缓存
        if (carData) cached.carData = carData;
        if (taskType) cached.taskType = taskType;
        if (slotsData) cached.slotsData = slotsData;

        // 只在AI响应完成时更新一次，避免中间状态导致重新渲染
        if (!isAIResponding) {
          const finalCached = messageUpdateCache.current.get(aiMessageId);
          if (!finalCached) return hasData;

          // 清除缓存
          messageUpdateCache.current.delete(aiMessageId);

          const updateData: Partial<StoredMessage> = {};
          if (finalCached.carData) updateData.carRecommendations = finalCached.carData;
          if (finalCached.taskType) updateData.activeTaskType = finalCached.taskType;
          if (finalCached.slotsData) updateData.slots = finalCached.slotsData;

          updateMessageInConversation(currentConversationId, aiMessageId, updateData);

          // 只在AI响应完成后一次性更新视图状态
          setMessages(prevMessages => {
            // 找到需要更新的消息
            const messageNeedsUpdate = prevMessages.some(msg => 
              msg.id === aiMessageId && 
              (!msg.carRecommendations && finalCached.carData || 
               !msg.activeTaskType && finalCached.taskType)
            );
            
            // 如果没有需要更新的内容，直接返回原状态
            if (!messageNeedsUpdate) return prevMessages;
            
            console.log('一次性更新消息数据:', aiMessageId);
            return prevMessages.map(msg => {
              if (msg.id === aiMessageId) {
                return {
                  ...msg,
                  carRecommendations: finalCached.carData || msg.carRecommendations,
                  activeTaskType: finalCached.taskType || msg.activeTaskType,
                  slots: finalCached.slotsData || msg.slots
                };
              }
              return msg;
            });
          });

          // 保存到独立的推荐记录存储
          if (finalCached.carData && finalCached.carData.length > 0) {
            addRecommendationToConversation(currentConversationId, aiMessageId, finalCached.carData, finalCached.taskType || undefined);
          }
        }
      }

      return hasData;
    } catch (error) {
      console.error('解析车辆推荐数据失败:', error, 'delta:', delta.substring(0, 100) + '...');
      return false;
    }
  }, [currentConversationId, isAIResponding, slotsData]);

  // 创建流式更新回调，实现逐字显示 - 优化为避免过多的状态更新
  const createStreamingCallback = useCallback((aiMessageId: string) => {
    // 使用缓冲机制减少状态更新
    let contentBuffer = '';
    let bufferUpdateTimer: NodeJS.Timeout | null = null;
    const flushBuffer = () => {
      if (contentBuffer) {
        flushSync(() => {
          setMessages(prevMessages =>
            prevMessages.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, content: msg.content + contentBuffer }
                : msg
            )
          );
        });

        // 更新当前AI消息状态
        // setCurrentAIMessage(prev => prev + contentBuffer); // 暂时注释
        contentBuffer = '';

        // 滚动到底部
        setTimeout(() => scrollToBottom(), 0);
      }
      bufferUpdateTimer = null;
    };

    // 添加强制刷新方法，用于响应完成时确保所有内容都已更新
    const forceFlush = () => {
      if (bufferUpdateTimer) {
        clearTimeout(bufferUpdateTimer);
        bufferUpdateTimer = null;
      }
      flushBuffer();
    };

    const callback = (delta: string) => {
      // 检查是否是车辆推荐或任务数据，如果是则不显示
      if (handleCarRecommendation(delta, aiMessageId)) {
        return;
      }

      // 过滤掉JSON格式的数据，只显示纯文本
      if (delta.startsWith('{') || delta.startsWith('[')) {
        return;
      }

      // 添加到缓冲区
      contentBuffer += delta;

      // 设置延迟更新，减少状态更新次数
      if (!bufferUpdateTimer) {
        bufferUpdateTimer = setTimeout(flushBuffer, 100); // 每100ms更新一次UI
      }
    };

    // 将强制刷新方法附加到回调函数上
    (callback as any).forceFlush = forceFlush;

    return callback;
  }, [handleCarRecommendation]);

  // 检查语音录制支持（用户点击时的完整检查）
  const checkVoiceSupport = () => {
    console.log('ChatPage: 用户点击录音，进行完整检查');

    // 检查浏览器支持
    if (!VoiceRecorder.isSupported()) {
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      let errorMsg = '';
      if (isMobile) {
        errorMsg = '请确保使用最新版Chrome或Safari浏览器，并允许麦克风权限';
      } else {
        errorMsg = '当前浏览器不支持语音录制，请使用Chrome、Firefox或Safari浏览器';
      }

      throw new Error(errorMsg);
    }

    // 检查录制器实例
    if (!voiceRecorder) {
      // 如果录制器不存在，尝试重新创建
      try {
        const recorder = createVoiceRecorder({ maxDuration: 60000 });
        setVoiceRecorder(recorder);
        return; // 成功创建，继续执行
      } catch (error) {
        throw new Error('语音录制器初始化失败');
      }
    }
  };

  // 关闭语音权限提示
  const handleCloseVoicePermissionTip = () => {
    setVoicePermissionTipClosed(true);
    localStorage.setItem('voicePermissionTipClosed', 'true');
  };

  // 检测是否为iOS设备
  const isIOSDevice = useCallback(() => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }, []);

  // TTS自动播放功能
  const toggleAutoPlay = useCallback(async () => {
    const newState = !isAutoPlayEnabled;

    // 如果要开启自动播放，先请求音频权限
    if (newState && !hasAudioPermission()) {
      console.log('TTS: 请求音频播放权限...');
      try {
        // iOS设备需要用户交互才能获取权限
        if (isIOSDevice()) {
          console.log('TTS: iOS设备，使用强制权限获取');
          const permissionGranted = await forceRequestAudioPermission();
          if (!permissionGranted) {
            console.warn('TTS: iOS设备无法获取音频播放权限，但仍允许开启（使用Web Audio API）');
            // iOS设备即使权限获取失败，也允许开启自动播放
            // 因为我们有Web Audio API作为备用方案
            console.log('TTS: iOS设备将使用Web Audio API进行自动播放');
          }
        } else {
          const permissionGranted = await requestAudioPermission();
          if (!permissionGranted) {
            console.warn('TTS: 无法获取音频播放权限，自动播放可能无法正常工作');
            // 仍然允许用户开启，但会在实际播放时提示
          }
        }
      } catch (error) {
        console.error('TTS: 权限请求失败:', error);
        if (isIOSDevice()) {
          console.log('TTS: iOS设备权限请求失败，但仍允许开启自动播放（使用Web Audio API）');
          // 不再阻止用户开启自动播放，让Web Audio API处理
        }
      }
    }

    setIsAutoPlayEnabled(newState);
    localStorage.setItem('tts_auto_play_enabled', JSON.stringify(newState));
    console.log('TTS自动播放:', newState ? '已开启' : '已关闭');

    // 🔥 移除iOS设备上的弹窗提示，直接开启功能
    if (newState && isIOSDevice()) {
      console.log('iOS设备TTS自动播放已开启，无需弹窗提示');
    }
  }, [isAutoPlayEnabled, isIOSDevice]);

  // 播放TTS语音 - 带防重复调用机制（自动播放）
  const playTTSForMessage = useCallback(async (text: string, messageId: string) => {
    console.log('TTS: 自动播放被调用', {
      messageId,
      isAutoPlayEnabled,
      refState: ttsStateRef.current,
      textLength: text.length
    });

    if (!isAutoPlayEnabled || !text.trim()) {
      console.log('TTS: 跳过自动播放 - 自动播放未开启或文本为空');
      return;
    }

    // 使用ref进行同步检查，防止重复调用
    if (ttsStateRef.current.isPlaying) {
      console.log('TTS: 跳过自动播放 - 正在播放中');
      return;
    }

    if (ttsStateRef.current.lastMessageId === messageId) {
      console.log('TTS: 跳过自动播放 - 消息已播放过:', messageId);
      return;
    }

    if (ttsStateRef.current.pendingCalls.has(messageId)) {
      console.log('TTS: 跳过自动播放 - 消息正在处理中:', messageId);
      return;
    }

    // 检查是否有手动播放正在进行
    if (currentPlayingAudio && !currentPlayingAudio.isDestroyed) {
      console.log('TTS: 跳过自动播放 - 手动播放正在进行');
      return;
    }

    if (!isTTSAvailable()) {
      console.warn('TTS功能不可用');
      return;
    }

    try {
      console.log('TTS: 开始自动播放消息:', messageId);

      // 同步更新ref状态
      ttsStateRef.current.isPlaying = true;
      ttsStateRef.current.lastMessageId = messageId;
      ttsStateRef.current.pendingCalls.add(messageId);

      // 异步更新state状态
      setIsTTSPlaying(true);
      // setLastTTSMessageId(messageId); // 暂时注释

      // 设置消息播放状态为加载中
      setMessagePlayStates(prev => ({ ...prev, [messageId]: 'loading' }));

      // 合成可控制的音频对象
      const controllableAudio = await synthesizeTextControllable(text);

      // 设置播放状态和音频对象到state中，确保暂停按钮能控制
      setMessagePlayStates(prev => ({ ...prev, [messageId]: 'playing' }));
      setCurrentPlayingAudio(controllableAudio);
      setCurrentPlayingMessageId(messageId);

      // 设置播放结束回调
      controllableAudio.onPlayEnd(() => {
        console.log('TTS: 自动播放自然结束:', messageId);
        // 同步更新ref状态
        ttsStateRef.current.isPlaying = false;
        ttsStateRef.current.pendingCalls.delete(messageId);
        // 异步更新state状态
        setIsTTSPlaying(false);
        setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
        setCurrentPlayingAudio(null);
        setCurrentPlayingMessageId(null);
      });

      // 设置播放错误回调
      controllableAudio.onPlayError((error) => {
        console.error('TTS: 自动播放出错:', messageId, error);
        // 同步更新ref状态
        ttsStateRef.current.isPlaying = false;
        ttsStateRef.current.pendingCalls.delete(messageId);
        // 异步更新state状态
        setIsTTSPlaying(false);
        setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
        setCurrentPlayingAudio(null);
        setCurrentPlayingMessageId(null);
      });

      // 开始播放
      await controllableAudio.play();
      console.log('TTS: 自动播放完成:', messageId);
    } catch (error) {
      console.error('TTS自动播放失败:', error);

      // 如果是权限问题，提示用户
      if (error instanceof Error && error.message.includes('阻止')) {
        console.warn('TTS: 自动播放被浏览器阻止，建议用户手动点击TTS按钮');
      }
    } finally {
      // 如果发生错误，清理状态（正常结束的状态清理在回调中处理）
      if (ttsStateRef.current.pendingCalls.has(messageId)) {
        ttsStateRef.current.isPlaying = false;
        ttsStateRef.current.pendingCalls.delete(messageId);
        setIsTTSPlaying(false);
        setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
        setCurrentPlayingAudio(null);
        setCurrentPlayingMessageId(null);
      }
    }
  }, [isAutoPlayEnabled, currentPlayingAudio]);

  // 强制停止所有TTS播放
  const forceStopAllTTS = useCallback(() => {
    console.log('TTS: 强制停止所有播放', {
      currentPlayingAudio: !!currentPlayingAudio,
      currentPlayingMessageId,
      refState: ttsStateRef.current,
      messageStates: messagePlayStates
    });

    // 停止当前播放的音频
    if (currentPlayingAudio) {
      try {
        currentPlayingAudio.destroy(); // 使用destroy而不是stop，确保彻底停止
      } catch (error) {
        console.warn('TTS: 销毁音频对象时出错:', error);
      }
      setCurrentPlayingAudio(null);
      setCurrentPlayingMessageId(null);
    }

    // 清理ref状态，确保自动播放状态也被重置
    ttsStateRef.current.isPlaying = false;
    ttsStateRef.current.lastMessageId = null;
    ttsStateRef.current.pendingCalls.clear();

    // 重置React状态
    setIsTTSPlaying(false);

    // 重置所有消息的播放状态为idle
    setMessagePlayStates(prev => {
      const newStates: Record<string, MessagePlayState> = {};
      Object.keys(prev).forEach(id => {
        newStates[id] = 'idle';
      });
      console.log('TTS: 重置所有消息状态为idle:', Object.keys(newStates));
      return newStates;
    });
  }, [currentPlayingAudio, currentPlayingMessageId, messagePlayStates]);

  // 强制停止语音转文字处理
  const forceStopASR = useCallback(() => {
    console.log('ChatPage: 强制停止语音转文字处理');

    // 取消当前的ASR请求（兼容性检查）
    if (currentASRController && typeof currentASRController.abort === 'function') {
      console.log('ChatPage: 取消ASR请求');
      currentASRController.abort();
      setCurrentASRController(null);
    }

    // 重置语音处理状态
    setIsProcessingVoice(false);
    setIsRecording(false);
    setRecordingCanceled(false);
    setDragStartY(null);
    setVoiceError(null); // 清除语音错误状态
    setVoiceStartCanceled(false); // 重置取消标志

    console.log('ChatPage: 语音转文字处理已停止');
  }, [currentASRController]);

  // 播放指定消息的TTS
  const playMessageTTS = useCallback(async (messageId: string, text: string) => {
    try {
      console.log('TTS: 开始播放消息:', messageId, '当前播放中的消息:', currentPlayingMessageId);

      // 强制停止所有当前播放的音频（包括自动播放的TTS）
      forceStopAllTTS();

      // 立即设置加载状态，确保UI响应
      setMessagePlayStates(prev => {
        const newStates = { ...prev };
        // 确保所有其他消息都是idle状态
        Object.keys(newStates).forEach(id => {
          if (id !== messageId) {
            newStates[id] = 'idle';
          }
        });
        // 设置当前消息为loading
        newStates[messageId] = 'loading';
        return newStates;
      });

      // 如果没有音频权限，先请求权限
      if (!hasAudioPermission()) {
        console.log('TTS: 请求音频播放权限...');
        try {
          await requestAudioPermission();
        } catch (error) {
          console.warn('TTS: 权限请求失败，但继续尝试播放:', error);
        }
      }

      // 合成语音
      const controllableAudio = await synthesizeTextControllable(text);

      // 用户主动点击的播放请求不应该被打断检查阻止
      // 移除状态检查，避免React状态更新时机问题导致的误判

      // 设置播放状态
      setMessagePlayStates(prev => ({ ...prev, [messageId]: 'playing' }));
      setCurrentPlayingAudio(controllableAudio);
      setCurrentPlayingMessageId(messageId);

      // 设置播放结束回调
      controllableAudio.onPlayEnd(() => {
        console.log('TTS: 消息播放自然结束:', messageId);
        setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
        setCurrentPlayingAudio(null);
        setCurrentPlayingMessageId(null);
      });

      // 设置播放错误回调
      controllableAudio.onPlayError((error) => {
        console.error('TTS: 消息播放出错:', messageId, error);
        setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
        setCurrentPlayingAudio(null);
        setCurrentPlayingMessageId(null);
      });

      // 开始播放
      await controllableAudio.play();

    } catch (error) {
      console.error('TTS: 消息播放失败:', messageId, error);
      // 确保状态被正确重置，即使发生错误
      setMessagePlayStates(prev => ({ ...prev, [messageId]: 'idle' }));
      setCurrentPlayingAudio(null);
      setCurrentPlayingMessageId(null);

      // 清理ref状态
      ttsStateRef.current.isPlaying = false;
      ttsStateRef.current.pendingCalls.delete(messageId);
      setIsTTSPlaying(false);

      // 可选：向用户显示错误提示
      console.warn('TTS: 播放失败，请重试');
    }
  }, [currentPlayingAudio, messagePlayStates, forceStopAllTTS]);

  // 停止当前播放（用于按钮点击）
  const stopCurrentPlayback = useCallback(() => {
    if (currentPlayingAudio && currentPlayingMessageId) {
      console.log('TTS: 用户停止播放:', currentPlayingMessageId);
      forceStopAllTTS();
    }
  }, [currentPlayingAudio, currentPlayingMessageId, forceStopAllTTS]);

  // TTS播放按钮组件
  const TTSPlayButton = React.memo(({ messageId, text }: { messageId: string; text: string }) => {
    const playState = messagePlayStates[messageId] || 'idle';
    const isCurrentlyPlaying = currentPlayingMessageId === messageId;

    const handleClick = () => {
      console.log('TTS: 播放按钮被点击', {
        messageId,
        playState,
        isCurrentlyPlaying,
        currentPlayingMessageId,
        allPlayStates: messagePlayStates
      });

      // 防止在加载状态下点击
      if (playState === 'loading') {
        console.log('TTS: 加载中，忽略点击');
        return;
      }

      // 防止任何消息正在加载时点击其他按钮
      const hasLoadingMessage = Object.values(messagePlayStates).some(state => state === 'loading');
      if (hasLoadingMessage && playState !== 'playing') {
        console.log('TTS: 有消息正在加载中，忽略其他点击');
        return;
      }

      if (isCurrentlyPlaying && playState === 'playing') {
        // 当前正在播放，点击停止
        console.log('TTS: 停止当前播放:', messageId);
        stopCurrentPlayback();
      } else {
        // 开始播放（会自动停止其他播放）
        console.log('TTS: 开始新的播放:', messageId, '停止之前的播放:', currentPlayingMessageId);
        playMessageTTS(messageId, text);
      }
    };

    return (
      <button
        onClick={handleClick}
        disabled={playState === 'loading'}
        className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 ${
          playState === 'loading'
            ? 'bg-gradient-to-r from-blue-50 to-indigo-50 cursor-not-allowed text-blue-400 border border-blue-100'
            : playState === 'playing' && isCurrentlyPlaying
            ? 'bg-gradient-to-r from-red-50 to-pink-50 hover:from-red-100 hover:to-pink-100 text-red-500 border border-red-200 hover:border-red-300'
            : 'bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 text-green-600 border border-green-200 hover:border-green-300'
        }`}
        title={
          playState === 'loading'
            ? '正在合成语音...'
            : playState === 'playing' && isCurrentlyPlaying
            ? '点击停止播放'
            : '点击播放语音'
        }
      >
        {playState === 'loading' ? (
          // 加载中的现代化音频波纹动画
          <div className="flex items-center space-x-0.5">
            <div className="w-0.5 h-3 bg-current rounded-full animate-pulse" style={{ animationDelay: '0ms', animationDuration: '800ms' }}></div>
            <div className="w-0.5 h-4 bg-current rounded-full animate-pulse" style={{ animationDelay: '100ms', animationDuration: '800ms' }}></div>
            <div className="w-0.5 h-5 bg-current rounded-full animate-pulse" style={{ animationDelay: '200ms', animationDuration: '800ms' }}></div>
            <div className="w-0.5 h-4 bg-current rounded-full animate-pulse" style={{ animationDelay: '300ms', animationDuration: '800ms' }}></div>
            <div className="w-0.5 h-3 bg-current rounded-full animate-pulse" style={{ animationDelay: '400ms', animationDuration: '800ms' }}></div>
          </div>
        ) : playState === 'playing' && isCurrentlyPlaying ? (
          // 播放中显示现代化停止图标
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor" className="drop-shadow-sm"/>
          </svg>
        ) : (
          // 空闲状态显示现代化播放图标
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path d="M8 5.14v13.72L19 12L8 5.14z" fill="currentColor" className="drop-shadow-sm"/>
          </svg>
        )}
      </button>
    );
  });

  // 车辆推荐卡片组件 - 横向商务布局 - 使用React.memo优化
  const CarRecommendationCard = React.memo(({ car }: { car: any }) => {
    if (!car) return null;

    // 状态管理
    const [imageHeight, setImageHeight] = useState<number>(67.5); // 默认高度
    const [cachedImageSrc, setCachedImageSrc] = useState<string | null>(null);
    const [isImageLoading, setIsImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);

    // 引用
    const rightContentRef = useRef<HTMLDivElement>(null);
    const resizeObserverRef = useRef<ResizeObserver | null>(null);

    // 生成稳定的车辆ID - 优先使用最稳定的标识符
    const carId = React.useMemo(() => {
      return car.vehicle_id || car.id || car.vehicle_name || `car_${Date.now()}_${Math.random()}`;
    }, [car.vehicle_id, car.id, car.vehicle_name]);

    // 使用useRef存储图片URL，避免不必要的重新计算
    const imageUrlRef = useRef<string | null>(null);
    const imageUrl = React.useMemo(() => {
      const newUrl = car.pic_url || (car.first_photo_url ? `http://images.autostreets.com/${car.first_photo_url}` : null);
      imageUrlRef.current = newUrl;
      return newUrl;
    }, [carId, car.pic_url, car.first_photo_url]); // 使用carId作为主要依赖

    // 图片缓存和加载逻辑 - 优化版本，避免重复加载
    useEffect(() => {
      if (!imageUrl) {
        setIsImageLoading(false);
        setImageError(true);
        setCachedImageSrc(null);
        return;
      }

      // 获取当前图片的加载状态
      const currentState = getImageLoadingState(imageUrl);

      // 如果已经有缓存，直接使用
      if (currentState.cachedSrc) {
        setCachedImageSrc(currentState.cachedSrc);
        setIsImageLoading(false);
        setImageError(false);
        return;
      }

      // 如果有错误，设置错误状态
      if (currentState.error) {
        setIsImageLoading(false);
        setImageError(true);
        setCachedImageSrc(null);
        return;
      }

      // 如果正在加载，设置加载状态
      if (currentState.isLoading) {
        setIsImageLoading(true);
        setImageError(false);
        setCachedImageSrc(null);
        return;
      }

      // 开始加载图片
      const loadImage = async () => {
        try {
          setIsImageLoading(true);
          setImageError(false);

          const cachedData = await preloadAndCacheImage(imageUrl);
          setCachedImageSrc(cachedData);
          setIsImageLoading(false);
        } catch (error) {
          console.warn('图片缓存失败:', imageUrl, error);
          setImageError(true);
          setIsImageLoading(false);
          setCachedImageSrc(null);
        }
      };

      loadImage();
    }, [carId, imageUrl]); // 使用carId作为主要依赖，imageUrl作为辅助依赖

    // 动态高度监听
    useEffect(() => {
      if (!rightContentRef.current) return;

      // 检查ResizeObserver支持
      if (typeof ResizeObserver !== 'undefined') {
        resizeObserverRef.current = new ResizeObserver((entries) => {
          for (const entry of entries) {
            const height = entry.contentRect.height;
            setImageHeight(Math.max(height, 60)); // 最小60px高度
          }
        });

        resizeObserverRef.current.observe(rightContentRef.current);
      } else {
        // 降级方案：使用固定高度
        setImageHeight(67.5);
      }

      return () => {
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
        }
      };
    }, []);

    const handleCardClick = () => {
      if (onCarDetailClick) {
        const scrollPosition = getCurrentScrollPosition();
        onCarDetailClick(car, scrollPosition);
      }
    };

    const handleImageClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (cachedImageSrc || imageUrl) {
        setLightboxSlides([{
          src: cachedImageSrc || imageUrl || '',
          alt: car.vehicle_name || '车辆图片',
          title: `${car.brand || ''} ${car.brand_series || ''} ${car.model_year || ''}款`.trim()
        }]);
        setLightboxIndex(0);
        setLightboxOpen(true);
      }
    };

    return (
      <div
        className="car-recommendation-card-horizontal cursor-pointer"
        onClick={handleCardClick}
      >
        {/* 上半部分：图片和标题价格的横向布局 */}
        <div className="flex flex-row space-x-4 mb-3">
          {/* 左侧：车辆图片 */}
          <div className="flex-shrink-0">
            <div
              className="car-image-container-dynamic cursor-pointer hover:opacity-90 transition-opacity"
              style={{ height: `${imageHeight}px` }}
              onClick={handleImageClick}
            >
              {isImageLoading ? (
                <div className="car-image-loading">
                  加载中
                </div>
              ) : cachedImageSrc && !imageError ? (
                <img
                  src={cachedImageSrc}
                  alt={car.vehicle_name || '车辆图片'}
                  className="car-image-dynamic"
                  key={carId}
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-500 text-sm">
                  {imageError ? '图片加载失败' : '暂无图片'}
                </div>
              )}

              {/* 放大镜图标 */}
              {cachedImageSrc && !imageError && !isImageLoading && (
                <div className="absolute top-2 right-2 bg-black/50 rounded-full p-1">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：车辆名称和价格 */}
          <div ref={rightContentRef} className="flex-1 flex flex-col justify-center min-w-0">
            {/* 车辆名称 */}
            <h3 className="text-base font-semibold text-gray-800 leading-tight line-clamp-2 mb-2">
              {car.vehicle_name || '未知车型'}
            </h3>

            {/* 价格 */}
            <div className="text-lg font-bold" style={{ color: '#00A76F' }}>
              ¥{car.price ? (car.price / 10000).toFixed(1) : '0'}万
            </div>
          </div>
        </div>

        {/* 下半部分：车辆详细信息网格 - 每行3个，共2行 */}
        <div className="grid grid-cols-3 gap-3">
          <div className="car-info-item-business">
            <span className="car-info-label-business">上牌时间</span>
            <span className="car-info-value-business">{car.model_year || '未知'}年</span>
          </div>
          <div className="car-info-item-business">
            <span className="car-info-label-business">行驶里程</span>
            <span className="car-info-value-business">
              {car.display_mileage ? `${(car.display_mileage / 10000).toFixed(1)}万公里` : '未知'}
            </span>
          </div>
          <div className="car-info-item-business">
            <span className="car-info-label-business">排量</span>
            <span className="car-info-value-business">{car.swept_volume || '未知'}L</span>
          </div>
          <div className="car-info-item-business">
            <span className="car-info-label-business">座位数</span>
            <span className="car-info-value-business">{car.seat_number || '未知'}座</span>
          </div>
          <div className="car-info-item-business">
            <span className="car-info-label-business">车身颜色</span>
            <span className="car-info-value-business">{car.body_color || '未知'}</span>
          </div>
          <div className="car-info-item-business">
            <span className="car-info-label-business">变速箱</span>
            <span className="car-info-value-business">{car.transmission_type || '未知'}</span>
          </div>
        </div>
      </div>
    );
  }, (prevProps, nextProps) => {
    // 优化的比较函数，避免不必要的重新渲染
    const prevCar = prevProps.car;
    const nextCar = nextProps.car;

    if (!prevCar && !nextCar) return true;
    if (!prevCar || !nextCar) return false;

    // 生成稳定的车辆ID进行比较
    const getPrimaryId = (car: any) => car.vehicle_id || car.id || car.vehicle_name;
    const prevId = getPrimaryId(prevCar);
    const nextId = getPrimaryId(nextCar);

    // 如果车辆ID相同，比较关键显示字段
    if (prevId === nextId) {
      return (
        prevCar.price === nextCar.price &&
        prevCar.pic_url === nextCar.pic_url &&
        prevCar.first_photo_url === nextCar.first_photo_url &&
        prevCar.vehicle_name === nextCar.vehicle_name &&
        prevCar.model_year === nextCar.model_year &&
        prevCar.display_mileage === nextCar.display_mileage
      );
    }

    return false;
  });



  // 处理文本输入变化和高度自适应
  const handleTextInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setTextInput(value);

    // 自动调整高度
    const textarea = e.target;
    const minHeight = 48; // 最小高度（与语音按钮同高）
    const maxHeight = 120; // 最大高度（5行）
    // const lineHeight = 24; // 每行高度 - 暂时不使用

    // 重置高度以获取正确的scrollHeight
    textarea.style.height = `${minHeight}px`;

    // 计算所需高度
    const scrollHeight = textarea.scrollHeight;
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);

    setTextareaHeight(newHeight);
    textarea.style.height = `${newHeight}px`;
  };

  // 处理滚动位置恢复和自动滚动
  useEffect(() => {
    // 如果有需要恢复的滚动位置，则恢复到该位置
    if (restoreScrollPosition !== null && restoreScrollPosition !== undefined && messages.length > 0) {
      setTimeout(() => {
        restoreScrollToPosition(restoreScrollPosition);
        // 通知父组件已恢复滚动位置
        onScrollPositionRestored?.();
      }, 100); // 稍微延迟以确保DOM已更新
    } else {
      // 否则正常滚动到底部
      scrollToBottom();
    }
  }, [messages, restoreScrollPosition]); // 移除onScrollPositionRestored依赖，避免频繁重新渲染

  // 生成唯一消息ID
  const generateMessageId = (type: 'user' | 'ai' = 'user'): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${timestamp}_${random}${type === 'ai' ? '_ai' : ''}`;
  };

  // 添加消息
  const addMessage = (content: string, type: 'user' | 'ai', carRecommendations?: any[], activeTaskType?: string, customId?: string, forceConversationId?: string) => {
    const newMessage: Message = {
      id: customId || generateMessageId(type),
      type,
      content,
      timestamp: new Date(),
      carRecommendations,
      activeTaskType
    };
    setMessages(prev => [...prev, newMessage]);

    // 持久化消息到localStorage - 使用传入的conversationId或当前的conversationId
    const conversationId = forceConversationId || currentConversationId;
    if (conversationId) {
      const storedMessage = convertMessageToStored(newMessage);
      addMessageToConversation(conversationId, storedMessage);
    }

    return newMessage;
  };

  // 删除整个会话
  const deleteConversationHandler = async (conversationId: string) => {
    // 打开确认对话框
    setDeleteConfirmDialog({
      isOpen: true,
      conversationId,
      isDeleting: false
    });
  };

  // 确认删除会话
  const confirmDeleteConversation = async () => {
    const { conversationId } = deleteConfirmDialog;
    if (!conversationId) return;

    setDeleteConfirmDialog(prev => ({ ...prev, isDeleting: true }));

    try {
      // 🔥 调用真实API删除服务器端数据
      const { deleteConversationAPI } = await import('../utils/chatApi');
      console.log(`🗑️ 正在删除会话: ${conversationId}`);

      const result = await deleteConversationAPI(conversationId);
      console.log('✅ 服务器端删除成功:', result.data);

      // 删除本地会话数据
      deleteConversation(conversationId);

      // 如果删除的是当前会话，切换到新会话
      if (conversationId === currentConversationId) {
        startNewChat();
      }

      // 删除对话后强制刷新会话历史（现在每次都从API实时获取）
      await loadConversationHistory(true);

      console.log(`🎉 会话删除完成: ${conversationId}`);

      // 关闭对话框
      setDeleteConfirmDialog({
        isOpen: false,
        conversationId: null,
        isDeleting: false
      });
    } catch (error) {
      console.error('❌ 删除会话失败:', error);
      alert(`删除会话失败: ${error instanceof Error ? error.message : '未知错误'}`);

      // 重置删除状态但保持对话框打开
      setDeleteConfirmDialog(prev => ({ ...prev, isDeleting: false }));
    }
  };

  // 取消删除会话
  const cancelDeleteConversation = () => {
    setDeleteConfirmDialog({
      isOpen: false,
      conversationId: null,
      isDeleting: false
    });
  };

  // 开始新对话
  const startNewChat = async () => {
    // 防止重复点击
    if (isCreatingNewChat) {
      console.log('正在创建新对话中，忽略重复点击');
      return;
    }

    setIsCreatingNewChat(true);

    // 先停止所有正在播放的音频
    console.log('TTS: 开始新对话，停止所有音频播放');
    forceStopAllTTS();

    try {
      const newConversationId = await startNewConversation();
      setCurrentConversationId(newConversationId);
      setStorageConversationId(newConversationId);
      setMessages([]);
      // setCurrentAIMessage(''); // 暂时注释
      setActiveTaskType(null);
      setSlotsData(null); // 清空旧对话的slots数据，防止词云数据混合

      await loadConversationHistory(true); // 创建新对话时强制刷新会话历史（现在每次都从API实时获取）
      console.log('Started new chat, conversation ID:', newConversationId);
    } catch (error) {
      console.error('创建新会话失败:', error);
      // 即使失败也要清理UI状态
      setMessages([]);
      setActiveTaskType(null);
      setSlotsData(null);
    } finally {
      // 无论成功还是失败，都要重置按钮状态
      setIsCreatingNewChat(false);
    }
  };



  // 发送文本消息
  const sendTextMessage = async () => {
    if (!textInput.trim() || isAIResponding) {
      return;
    }

    // 发送新消息前，停止所有正在播放的音频
    console.log('TTS: 发送新消息，停止所有音频播放');
    forceStopAllTTS();

    const messageContent = textInput.trim();
    setTextInput('');
    // 重置文本框高度到初始状态
    setTextareaHeight(48);

    // 确保有会话ID
    let conversationId = currentConversationId;
    if (!conversationId) {
      try {
        conversationId = await startNewConversation();
        setCurrentConversationId(conversationId);
        setStorageConversationId(conversationId);
      } catch (error) {
        console.error('创建会话失败:', error);
        // 如果创建会话失败，仍然尝试发送消息，使用临时ID
        conversationId = 'temp-' + Date.now();
        setCurrentConversationId(conversationId);
        setStorageConversationId(conversationId);
      }
    }

    // 添加用户消息 - 传入conversationId确保消息被正确保存
    addMessage(messageContent, 'user', undefined, undefined, undefined, conversationId);
    const isFirstMessage = messages.length === 0;

    // 增加消息计数
    incrementMessageCount(conversationId);

    // 开始AI响应
    setIsAIResponding(true);
    // setCurrentAIMessage(''); // 暂时注释

    // 创建AI消息占位符 - 传入conversationId确保消息被正确保存
    const aiMessageId = generateMessageId('ai');
    addMessage('', 'ai', undefined, undefined, aiMessageId, conversationId);
    console.log('🔄 创建AI消息占位符:', { aiMessageId, conversationId });

    // 🔥 如果是第一条消息，同时发起标题生成请求
    let titlePromise: Promise<string> | null = null;
    if (isFirstMessage) {
      console.log('🔄 第一条消息，同时发起标题生成请求...', {
        conversationId,
        message: messageContent
      });
      titlePromise = getSummaryTopic(conversationId, messageContent);
    }

    try {
      await sendChatMessage(
        {
          conversation_id: conversationId,
          message: messageContent
        },
        // onChunk - 处理流式响应
        createStreamingCallback(aiMessageId),
        // onComplete - 完成响应
        async () => {
          setIsAIResponding(false);
          incrementMessageCount(conversationId!);

          // 延迟一小段时间确保所有缓冲内容都已刷新
          await new Promise(resolve => setTimeout(resolve, 150));

          // 获取AI消息内容用于TTS和持久化，此时消息内容已经完整
          let finalAIMessageContent = '';

          // 从当前messages状态中获取AI消息内容，但不再更新UI
          setMessages(currentMessages => {
            const aiMessage = currentMessages.find(msg => msg.id === aiMessageId);
            if (aiMessage && conversationId) {
              finalAIMessageContent = aiMessage.content;
              console.log('TTS: 获取到AI消息内容，长度:', finalAIMessageContent.length);

              // 持久化消息
              const storedMessage = convertMessageToStored(aiMessage);
              updateMessageInConversation(conversationId, aiMessageId, storedMessage);
              console.log('✅ AI消息已存储到localStorage:', { messageId: aiMessageId, contentLength: aiMessage.content.length });

              // 在这里调用TTS，确保能获取到消息内容
              if (finalAIMessageContent.trim() && isAutoPlayEnabled) {
                console.log('TTS: 文本发送流程调用TTS', { aiMessageId, contentLength: finalAIMessageContent.length });
                // 先停止所有手动播放，然后开始自动播放
                if (currentPlayingAudio && !currentPlayingAudio.isDestroyed) {
                  console.log('TTS: 停止手动播放，开始自动播放');
                  forceStopAllTTS();
                }
                playTTSForMessage(finalAIMessageContent, aiMessageId).catch(error => {
                  console.error('TTS播放失败:', error);
                });
              }
            }
            // 返回原状态，不触发重新渲染
            return currentMessages;
          });

          // 🔥 如果是第一条消息，处理并发的标题生成请求
          if (isFirstMessage && titlePromise) {
            try {
              console.log('🔄 AI响应完成，等待并发的标题生成请求结果...');

              const title = await titlePromise;
              console.log('✅ 对话标题获取成功:', title);

              // 更新本地会话标题
              updateConversation(conversationId!, { title });

              // 🔥 静默刷新对话列表，确保新标题在侧边栏显示
              console.log('🔄 静默刷新对话列表...');
              await loadConversationHistory(true); // 强制刷新会话历史
              console.log('✅ 对话列表刷新完成');
            } catch (error) {
              console.error('❌ 获取话题标题失败:', error);
              // 即使标题获取失败，也要刷新对话列表以确保新会话显示
              try {
                await loadConversationHistory(true);
                console.log('✅ 对话列表刷新完成（标题获取失败后的补偿刷新）');
              } catch (refreshError) {
                console.error('❌ 对话列表刷新也失败:', refreshError);
              }
            }
          }
        },
        // onError - 错误处理
        (error: string) => {
          setIsAIResponding(false);
          // 移除失败的AI消息
          setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
          // 添加错误消息
          addMessage(`抱歉，发生了错误：${error}`, 'ai');
        }
      );
    } catch (error) {
      setIsAIResponding(false);
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      addMessage('抱歉，网络连接出现问题，请稍后重试。', 'ai');
    }
  };

  // 检测是否为PC端模拟移动设备环境
  const isDesktopSimulation = () => {
    const hasTouch = 'ontouchstart' in window;
    const isMobile = window.navigator.userAgent.indexOf('Mobile') !== -1;
    const isDesktop = !hasTouch && !isMobile;

    // 在开发环境下输出检测结果
    if (process.env.NODE_ENV === 'development') {
      console.log('设备检测:', { hasTouch, isMobile, isDesktop, userAgent: window.navigator.userAgent });
    }

    return isDesktop;
  };

  // 语音按钮事件处理（移动端优化版本）
  const handleVoiceStart = async (e: React.TouchEvent | React.MouseEvent) => {
    console.log('ChatPage: 用户开始语音录制，事件类型:', e.type);

    // 阻止默认行为，防止长按触发右键菜单
    e.preventDefault();
    e.stopPropagation();

    // 如果正在处理语音转文字，直接返回，避免冲突
    if (isProcessingVoice) {
      console.log('ChatPage: 正在处理语音转文字，忽略录音请求');
      return;
    }

    // 重置取消标志，表示开始新的录音尝试
    setVoiceStartCanceled(false);

    try {
      // 检查基础支持
      checkVoiceSupport();

      // 打断所有正在播放的TTS和正在进行的语音转文字
      if (currentPlayingAudio || isTTSPlaying) {
        console.log('ChatPage: 录音开始，打断TTS播放');
        forceStopAllTTS();
      }

      // 打断正在进行的语音转文字处理
      if (isProcessingVoice || currentASRController) {
        console.log('ChatPage: 录音开始，打断语音转文字处理');
        forceStopASR();
      }

      setVoiceError(null);
      setRecordingCanceled(false);

      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      setDragStartY(clientY);

      // 确保有录制器实例
      if (!voiceRecorder) {
        throw new Error('语音录制器未初始化');
      }

      // 先检查权限状态
      console.log('ChatPage: 检查麦克风权限状态...');
      const permissionState = await voiceRecorder.checkPermission();
      console.log('ChatPage: 当前权限状态:', permissionState);

      if (permissionState === 'granted') {
        // 权限已授权，直接开始录音
        console.log('ChatPage: 权限已授权，开始录音');
        await voiceRecorder.startRecording();
        console.log('ChatPage: 录音已开始，检查是否已被取消');

        // 检查用户是否在录音开始前就松手了
        if (voiceStartCanceled) {
          console.log('ChatPage: 用户已松手，取消录音并重置状态');
          if (voiceRecorder && voiceRecorder.isRecording()) {
            voiceRecorder.cancelRecording();
          }
          return;
        }

        // 只有录音真正开始且用户未松手时才设置UI状态
        setIsRecording(true);
        setVoicePermissionGranted(true);
      } else if (permissionState === 'denied') {
        // 权限被明确拒绝
        console.log('ChatPage: 权限被明确拒绝');
        setVoicePermissionGranted(false);
        throw new Error('麦克风权限被拒绝。请在浏览器地址栏点击麦克风图标，允许麦克风访问后重试。');
      } else {
        // 权限未确定或需要申请，先申请权限
        console.log('ChatPage: 权限未确定，开始申请权限');
        try {
          await voiceRecorder.requestPermission();
          console.log('ChatPage: 权限申请完成，重新检查权限状态');

          // 权限申请后，重新检查权限状态但不自动开始录音
          const newPermissionState = await voiceRecorder.checkPermission();
          console.log('ChatPage: 重新检查后的权限状态:', newPermissionState);

          if (newPermissionState === 'granted') {
            // 权限申请成功，检查用户是否已松手
            console.log('ChatPage: 权限申请成功，检查用户是否已松手');
            if (voiceStartCanceled) {
              console.log('ChatPage: 用户已松手，仅更新权限状态，不开始录音');
              setVoicePermissionGranted(true);
              return;
            }
            // 权限申请成功，更新权限状态但不开始录音
            console.log('ChatPage: 权限申请成功，更新权限状态');
            setVoicePermissionGranted(true);
            // 用户需要再次点击才能开始录音
            console.log('ChatPage: 权限已获取，用户可以再次点击开始录音');
          } else {
            // 权限仍未授权，显示错误信息但不刷新页面
            console.log('ChatPage: 权限申请失败或被拒绝');
            setVoicePermissionGranted(false);
            throw new Error('麦克风权限被拒绝。请在浏览器地址栏点击麦克风图标，允许麦克风访问后重试。');
          }
        } catch (permissionError) {
          console.error('ChatPage: 权限申请过程中出错:', permissionError);
          setVoicePermissionGranted(false);
          // 重新抛出错误，让外层catch处理
          throw permissionError;
        }
      }
    } catch (error) {
      console.error('ChatPage: 语音录制失败:', error);
      setIsRecording(false);
      setVoiceStartCanceled(false); // 重置取消标志

      // 提供移动端友好的错误信息
      let errorMessage = '开始录音失败';
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

      // 判断是否为权限相关错误
      let isPermissionError = false;

      if (error instanceof Error) {
        console.error('ChatPage: 录音错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack,
          isMobile,
          isIOS,
          userAgent: navigator.userAgent
        });

        if (error.message.includes('NotAllowedError') || error.message.includes('权限被拒绝')) {
          isPermissionError = true;
          if (isIOS) {
            errorMessage = '麦克风权限被拒绝。请在Safari地址栏点击"aA"按钮，选择"网站设置"，然后允许麦克风访问。或在iOS设置→Safari→麦克风中允许访问。';
          } else if (isMobile) {
            errorMessage = '麦克风权限被拒绝。请在浏览器地址栏点击麦克风图标，允许麦克风访问后重试。';
          } else {
            errorMessage = '麦克风权限被拒绝。请在浏览器地址栏点击麦克风图标，允许麦克风访问后重试。';
          }
        } else if (error.message.includes('NotFoundError')) {
          errorMessage = '未找到麦克风设备，请检查设备连接。';
        } else if (error.message.includes('NotSupportedError')) {
          errorMessage = '当前浏览器不支持语音录制，请使用Chrome、Firefox或Safari浏览器。';
        } else if (error.message.includes('Load failed')) {
          if (isIOS) {
            errorMessage = 'iOS Safari录音初始化失败。请确保使用HTTPS访问，并尝试刷新页面重试。如果问题持续，请尝试重启Safari或设备。';
          } else {
            errorMessage = '录音功能加载失败，请刷新页面重试。';
          }
        } else if (error.message.includes('点击速度太快咯，请慢一些！')) {
          errorMessage = '录音器正在使用中，请稍后重试';
          // 对于录音器占用错误，重新检查权限状态而不是直接设置为false
          if (voiceRecorder) {
            voiceRecorder.checkPermission().then(permissionState => {
              console.log('ChatPage: 重新检查权限状态:', permissionState);
              setVoicePermissionGranted(permissionState === 'granted');
            }).catch(err => {
              console.warn('ChatPage: 重新检查权限失败:', err);
            });
          }
        } else if (error.message.includes('录音器状态异常')) {
          errorMessage = '录音器状态异常，请刷新页面重试';
          // 对于状态异常错误，也重新检查权限状态
          if (voiceRecorder) {
            voiceRecorder.checkPermission().then(permissionState => {
              console.log('ChatPage: 重新检查权限状态:', permissionState);
              setVoicePermissionGranted(permissionState === 'granted');
            }).catch(err => {
              console.warn('ChatPage: 重新检查权限失败:', err);
            });
          }
        } else {
          errorMessage = error.message;
        }
      }

      // 只有明确的权限错误才设置权限状态为false
      if (isPermissionError) {
        setVoicePermissionGranted(false);
      }

      setVoiceError(errorMessage);
    }
  };

  const handleVoiceMove = (e: React.TouchEvent | React.MouseEvent) => {
    // 只有在录音状态下才处理移动事件
    if (!isRecording || dragStartY === null) {
      // 如果不在录音状态，不阻止默认行为，允许正常滚动
      return;
    }

    // 在PC端模拟环境下，只处理真正的拖拽操作（鼠标按下状态）
    if (isDesktopSimulation() && e.type === 'mousemove') {
      // 检查鼠标是否处于按下状态
      const mouseEvent = e as React.MouseEvent;
      if (mouseEvent.buttons === 0) {
        // 鼠标没有按下，不处理移动事件，允许正常滚动
        console.log('ChatPage: 鼠标未按下，忽略移动事件');
        return;
      }
    }

    // 只在录音状态下阻止默认行为
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡，避免影响其他元素

    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    const deltaY = dragStartY - clientY;

    // 向上滑动超过 50px 时显示取消状态
    if (deltaY > 50) {
      setRecordingCanceled(true);
    } else {
      setRecordingCanceled(false);
    }
  };



  // 包装的事件处理函数，添加调试日志
  const handleVoiceEndWithLog = async (eventType: string) => {
    console.log(`ChatPage: handleVoiceEnd 被调用 (${eventType})`, {
      isRecording,
      hasVoiceRecorder: !!voiceRecorder,
      recordingCanceled,
      voiceStartCanceled,
      timestamp: Date.now(),
      eventType
    });

    // 设置取消标志，表示用户已松手
    setVoiceStartCanceled(true);

    if (!isRecording || !voiceRecorder) {
      console.log('ChatPage: handleVoiceEnd 提前返回，录音状态或录制器无效');
      return;
    }

    await handleVoiceEnd();
  };

  const handleVoiceEnd = async () => {
    try {
      if (recordingCanceled) {
        // 取消录音
        console.log('ChatPage: 用户取消录音');
        if (voiceRecorder) {
          voiceRecorder.cancelRecording();
        }
      } else {
        // 停止录音并处理
        console.log('ChatPage: 开始处理录音结果');
        setIsProcessingVoice(true);

        // 检查录音器状态，如果不在录音状态则跳过
        if (!voiceRecorder || !voiceRecorder.isRecording()) {
          console.warn('ChatPage: 录音器不在录音状态，跳过停止录音');
          setIsProcessingVoice(false); // 清除处理状态
          return;
        }

        const result = await voiceRecorder.stopRecording();

        console.log('ChatPage: 录音完成', {
          originalFormat: voiceRecorder.getActualMimeType(),
          originalSize: result.originalBlob.size + ' bytes',
          wavSize: result.wavBlob.size + ' bytes',
          duration: result.duration + 'ms'
        });

        try {
          // 创建ASR请求的取消控制器（兼容性检查）
          let asrController: AbortController | null = null;
          if (typeof AbortController !== 'undefined') {
            asrController = new AbortController();
            setCurrentASRController(asrController);
          }

          // 调用ASR API进行语音识别（使用标准WAV格式）
          console.log('ChatPage: 发送WAV格式音频到ASR接口...', {
            wavSize: result.wavBlob.size,
            wavType: result.wavBlob.type,
            originalSize: result.originalBlob.size,
            originalType: result.originalBlob.type
          });
          const recognizedText = await sendVoiceToASR(result.wavBlob, asrController || undefined);

          // ASR调用完成，清除处理状态并重置录音状态
          setCurrentASRController(null);
          setIsProcessingVoice(false);
          setIsRecording(false);
          setRecordingCanceled(false);
          setDragStartY(null);

          if (recognizedText.trim()) {
            // 发送语音消息前，停止所有正在播放的音频
            console.log('TTS: 发送语音消息，停止所有音频播放');
            forceStopAllTTS();

            // 重置文本框高度
            setTextareaHeight(48);

            // 确保有会话ID
            let conversationId = currentConversationId;
            if (!conversationId) {
              try {
                conversationId = await startNewConversation();
                setCurrentConversationId(conversationId);
                setStorageConversationId(conversationId);
              } catch (error) {
                console.error('创建会话失败:', error);
                // 如果创建会话失败，仍然尝试发送消息，使用临时ID
                conversationId = 'temp-' + Date.now();
                setCurrentConversationId(conversationId);
                setStorageConversationId(conversationId);
              }
            }

            // 添加用户消息并发送 - 传入conversationId确保消息被正确保存
            addMessage(recognizedText, 'user', undefined, undefined, undefined, conversationId);
            const isFirstMessage = messages.length === 0;

            // 增加消息计数
            incrementMessageCount(conversationId);

            // 开始AI响应
            setIsAIResponding(true);
            // setCurrentAIMessage(''); // 暂时注释

            // 创建AI消息占位符 - 传入conversationId确保消息被正确保存
            const aiMessageId = generateMessageId('ai');
            addMessage('', 'ai', undefined, undefined, aiMessageId, conversationId);

            // 🔥 如果是第一条消息，同时发起标题生成请求
            let titlePromise: Promise<string> | null = null;
            if (isFirstMessage) {
              console.log('🔄 第一条语音消息，同时发起标题生成请求...', {
                conversationId,
                message: recognizedText
              });
              titlePromise = getSummaryTopic(conversationId, recognizedText);
            }

            // 发送到聊天API
            await sendChatMessage(
              {
                conversation_id: conversationId,
                message: recognizedText
              },
              // onChunk - 处理流式响应
              createStreamingCallback(aiMessageId),
              // onComplete - 完成响应
              async () => {
                setIsAIResponding(false);
                incrementMessageCount(conversationId!);

                // 延迟一小段时间确保所有缓冲内容都已刷新
                await new Promise(resolve => setTimeout(resolve, 150));

                // 获取AI消息内容用于TTS和持久化
                let finalAIMessageContent = '';

                // 先从当前messages状态中获取AI消息内容
                setMessages(currentMessages => {
                  const aiMessage = currentMessages.find(msg => msg.id === aiMessageId);
                  if (aiMessage && conversationId) {
                    finalAIMessageContent = aiMessage.content;
                    console.log('TTS: 获取到AI消息内容，长度:', finalAIMessageContent.length);

                    // 持久化消息
                    const storedMessage = convertMessageToStored(aiMessage);
                    updateMessageInConversation(conversationId, aiMessageId, storedMessage);
                    console.log('✅ AI消息已存储到localStorage (语音):', { messageId: aiMessageId, contentLength: aiMessage.content.length });

                    // 在这里调用TTS，确保能获取到消息内容
                    if (finalAIMessageContent.trim() && isAutoPlayEnabled) {
                      console.log('TTS: 语音输入流程调用TTS', { aiMessageId, contentLength: finalAIMessageContent.length });
                      // 先停止所有手动播放，然后开始自动播放
                      if (currentPlayingAudio && !currentPlayingAudio.isDestroyed) {
                        console.log('TTS: 停止手动播放，开始自动播放');
                        forceStopAllTTS();
                      }
                      playTTSForMessage(finalAIMessageContent, aiMessageId).catch(error => {
                        console.error('TTS播放失败:', error);
                      });
                    }
                  }
                  return currentMessages;
                });

                // 🔥 如果是第一条消息，处理并发的标题生成请求
                if (isFirstMessage && titlePromise) {
                  try {
                    console.log('🔄 AI响应完成，等待并发的标题生成请求结果...');

                    const title = await titlePromise;
                    console.log('✅ 对话标题获取成功:', title);

                    // 更新本地会话标题
                    updateConversation(conversationId!, { title });

                    // 🔥 静默刷新对话列表，确保新标题在侧边栏显示
                    console.log('🔄 静默刷新对话列表...');
                    await loadConversationHistory(true); // 强制刷新会话历史
                    console.log('✅ 对话列表刷新完成');
                  } catch (error) {
                    console.error('❌ 获取话题标题失败:', error);
                    // 即使标题获取失败，也要刷新对话列表以确保新会话显示
                    try {
                      await loadConversationHistory(true);
                      console.log('✅ 对话列表刷新完成（标题获取失败后的补偿刷新）');
                    } catch (refreshError) {
                      console.error('❌ 对话列表刷新也失败:', refreshError);
                    }
                  }
                }
              },
              // onError - 错误处理
              (error: string) => {
                setIsAIResponding(false);
                // 移除失败的AI消息
                setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
                // 添加错误消息
                addMessage(`抱歉，发生了错误：${error}`, 'ai');
              }
            );
          }
        } catch (asrError) {
          // ASR调用失败，清除处理状态并重置录音状态
          setCurrentASRController(null);
          setIsProcessingVoice(false);
          setIsRecording(false);
          setRecordingCanceled(false);
          setDragStartY(null);

          // 检查是否是用户主动取消
          if (asrError instanceof Error && asrError.name === 'AbortError') {
            console.log('ChatPage: 语音转文字被用户取消');
            return; // 用户主动取消，不显示错误信息
          }

          // 检查是否是 AbortController 不支持的错误
          if (asrError instanceof Error && asrError.message.includes('AbortController')) {
            console.warn('ChatPage: AbortController 不支持，但语音转文字功能仍可正常使用');
            return; // 不显示错误信息，功能仍可使用
          }

          throw asrError; // 重新抛出错误，让外层catch处理
        }
      }
    } catch (error) {
      setVoiceError(error instanceof Error ? error.message : '语音识别失败');
      // 确保在错误情况下也清除处理状态
      setIsProcessingVoice(false);
    } finally {
      // 确保所有录音相关状态都被重置
      setIsRecording(false);
      setRecordingCanceled(false);
      setDragStartY(null);
      setIsProcessingVoice(false); // 确保处理状态也被清除
      setVoiceStartCanceled(false); // 重置取消标志
    }
  };

  return (
    <div
      className="chat-page-container bg-gray-50 flex flex-col relative"
      style={{
        // 确保在PC端模拟环境下页面可以正常滚动
        touchAction: isDesktopSimulation() ? 'auto' : 'pan-y',
        // 移除主容器的overflow，让子容器处理滚动
        overflow: 'hidden'
      }}
    >
      {/* 词云头部区域 - 显示robot.png和动态词云 */}
      <WordCloudHeader
        activeTaskType={activeTaskType}
        slotsData={slotsData}
        conversationId={currentConversationId}
        isRecording={isRecording}
        isProcessingVoice={isProcessingVoice}
        isAIResponding={isAIResponding}
        currentPlayingMessageId={currentPlayingMessageId}
      />

      {/* 顶部导航栏 - 固定在顶部，不透明背景 */}
      <div className="fixed top-0 left-0 right-0 z-40 flex items-center justify-between px-4 py-3"
           style={{
             background: '#ffffff'
           }}>
        <button
          onClick={() => setIsSidebarOpen(true)}
          className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <div className="flex items-center space-x-2">
          <button
            className={`flex items-center justify-center w-6 h-6 rounded-full transition-all duration-200 ${
              isAutoPlayEnabled
                ? 'bg-green-100 hover:bg-green-200 text-green-600'
                : 'hover:bg-gray-100 text-gray-600'
            } ${isTTSPlaying ? 'animate-pulse' : ''}`}
            onClick={toggleAutoPlay}
            title={
              isIOSDevice()
                ? (isAutoPlayEnabled
                    ? '关闭自动播放'
                    : '开启自动播放（iOS优化版本）')
                : (isAutoPlayEnabled ? '关闭自动播放' : '开启自动播放')
            }
          >
            {isAutoPlayEnabled ? (
              // 开启状态 - 音量图标
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5L6 9H2v6h4l5 4V5z" />
              </svg>
            ) : (
              // 关闭状态 - 静音图标
              <>
                {isIOSDevice() && !hasAudioPermission() ? (
                  // iOS设备无权限时显示权限图标
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                ) : (
                  // 普通静音图标
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H2v-6h3.586l5.707-5.707v17.414L5.586 15z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                  </svg>
                )}
              </>
            )}
          </button>
          <button
            className={`flex items-center justify-center w-6 h-6 rounded-full transition-colors ${
              isCreatingNewChat
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-100'
            }`}
            onClick={startNewChat}
            disabled={isCreatingNewChat}
            title={isCreatingNewChat ? "正在创建新对话..." : "创建新对话"}
          >
            {isCreatingNewChat ? (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
            ) : (
              <img
                src="/icon/new_chat.svg"
                alt="新对话"
                className="w-4 h-4"
                style={{ filter: 'brightness(0) saturate(100%) invert(42%) sepia(8%) saturate(1084%) hue-rotate(169deg) brightness(95%) contrast(89%)' }}
              />
            )}
          </button>
        </div>
      </div>

      {/* 聊天内容区域 - 智能自适应布局 */}
      <div
        ref={chatContainerRef}
        className={`flex-1 px-4 py-6 chat-container-adaptive ${isDesktopSimulation() ? 'desktop-simulation-scroll' : ''}`}
        style={{
          paddingTop: "calc(48px + 20vh + 16px)", // 顶部导航栏 + 词云头部 + 间距
          paddingBottom: "120px", // 底部输入区域预留空间
          // 确保在PC端模拟环境下可以正常滚动
          touchAction: isDesktopSimulation() ? "auto" : "pan-y",
          // 智能滚动：只在需要时显示滚动条
          overflowY: "auto",
          // 设置精确的高度，确保占据全部可用空间
          height: "100%",
          // 使用 box-sizing 确保 padding 不会增加总高度
          boxSizing: "border-box"
        }}
      >
        {messages.length === 0 ? (
          // 简洁的欢迎界面 - 移除词云背景和欢迎文字
          <div className="relative flex flex-col items-center justify-center min-h-[200px] px-6">
            {/* 空的欢迎界面，保持简洁 */}
          </div>
        ) : (
          // 聊天消息列表 - 自适应高度
          <div className="space-y-4 min-h-0">
            {/* 历史记录加载状态 */}
            {historyPagination.loading && (
              <div className="flex items-center justify-center py-4">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  <span className="text-sm">正在加载更多历史记录...</span>
                </div>
              </div>
            )}

            {/* 历史记录加载错误 */}
            {historyPagination.error && (
              <div className="flex items-center justify-center py-4">
                <div className="flex items-center space-x-2 text-red-500">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm">{historyPagination.error}</span>
                  <button
                    onClick={() => loadMoreHistory()}
                    className="text-blue-500 hover:text-blue-700 text-sm underline ml-2"
                  >
                    重试
                  </button>
                </div>
              </div>
            )}

            {/* 数据同步提示 */}
            {historySyncStatus.hasNewData && (
              <div className="flex items-center justify-center py-2">
                <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-2 flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-blue-700">检测到新消息，已自动更新</span>
                  <button
                    onClick={() => setHistorySyncStatus(prev => ({ ...prev, hasNewData: false }))}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {messages.map((message, index) => (
              <div key={`${message.id}_${index}`}>
                {/* 消息气泡 */}
                <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} p-3 rounded-2xl bg-gray-50/30`}>
                  {/* AI消息：头像+消息+播放按钮占据全宽 */}
                  {message.type === 'ai' ? (
                    <div className="flex items-start space-x-3 w-full">
                      {/* 头像 */}
                      <div className="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                        <img
                          key="ai-avatar"
                          src="/images/robot.png"
                          alt="AI助手"
                          className="w-full h-full object-contain"
                          loading="lazy"
                        />
                      </div>

                      {/* 消息气泡 - 占据剩余空间 */}
                      <div className="ai-message-bubble flex-1">
                        {message.content === '' && isAIResponding ? (
                          // AI思考中状态
                          <div className="thinking-dots">
                            <div className="thinking-dot"></div>
                            <div className="thinking-dot"></div>
                            <div className="thinking-dot"></div>
                          </div>
                        ) : (
                          <div className="flex flex-col">
                            <MarkdownErrorBoundary content={message.content}>
                              <MarkdownRenderer content={message.content} />
                            </MarkdownErrorBoundary>

                            {/* 对比按钮 - 放在AI消息内部 */}
                            {message.carRecommendations && message.carRecommendations.length === 2 && (
                              <button
                                className="inline-flex items-center px-3 py-2 mt-3 bg-gradient-to-r from-cyan-50 to-blue-50 hover:from-cyan-100 hover:to-blue-100 border border-cyan-200 rounded-lg transition-all duration-200 group self-start"
                                onClick={() => {
                                  console.log('🔄 对比按钮点击，onCarComparisonClick存在:', !!onCarComparisonClick);
                                  if (onCarComparisonClick) {
                                    console.log('🔄 调用onCarComparisonClick，车辆数据:', message.carRecommendations);
                                    const scrollPosition = getCurrentScrollPosition();
                                    onCarComparisonClick(message.carRecommendations!, scrollPosition);
                                  } else {
                                    console.log('❌ onCarComparisonClick不存在');
                                  }
                                }}
                              >
                                <span className="text-cyan-600 font-medium text-xs mr-2">
                                  点击查看两款车辆的对比详情
                                </span>
                                <svg
                                  className="w-3 h-3 text-cyan-600 group-hover:translate-x-1 transition-transform duration-200"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                              </button>
                            )}
                          </div>
                        )}
                      </div>

                      {/* TTS播放按钮 - 在消息卡片外部右侧 */}
                      {message.content.trim() && (
                        <div className="flex items-start pt-1">
                          <TTSPlayButton messageId={message.id} text={message.content} />
                        </div>
                      )}
                    </div>
                  ) : (
                    /* 用户消息：保持原有布局 */
                    <div className="flex items-start space-x-3 max-w-[85%] flex-row-reverse space-x-reverse">
                      {/* 头像 */}
                      <div className="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm overflow-hidden">
                        {userAvatar ? (
                          <img
                            src={userAvatar}
                            alt="用户头像"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* 消息气泡 */}
                      <div className="user-message-bubble">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 推荐卡片 - 紧跟在AI消息下方 */}
                {(() => {
                  // 直接渲染逻辑，不使用useMemo（因为在循环中）
                  const shouldShowRecommendations = message.type === 'ai' && message.carRecommendations && message.carRecommendations.length > 0;

                  if (shouldShowRecommendations) {
                    const recommendationCount = message.carRecommendations!.length;

                      // 如果是两条推荐，不显示推荐卡片（因为已经有对比按钮了）
                      if (recommendationCount === 2) {
                        return null;
                      }

                      // 其他情况显示推荐卡片
                      return (
                        <div className="mt-3">
                          <div className="text-sm text-gray-600 font-medium">为您推荐以下车辆：</div>
                          <div className="grid grid-cols-1 gap-3">
                            {message.carRecommendations!.map((car, carIndex) => {
                              try {
                                // 使用消息ID + 车辆索引作为稳定的key，避免包含变化的数据
                                const stableKey = `${message.id}-car-${carIndex}`;
                                return <CarRecommendationCard key={stableKey} car={car} />;
                              } catch (error) {
                                console.error('渲染车辆卡片失败:', error, car);
                                return <div key={`error-${message.id}-${carIndex}`} className="text-red-500 text-sm">车辆信息渲染失败</div>;
                              }
                            })}
                          </div>
                        </div>
                      );
                  }
                  return null;
                })()}
              </div>
            ))}

            {/* 在最后一条AI消息下方添加额外空白区域，避免被底部按钮遮挡 */}
            {messages.length > 0 && messages[messages.length - 1].type === 'ai' && (
              <div className="h-20 w-full"></div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* 语音错误提示 */}
      {voiceError && (
        <div className="fixed bottom-20 left-4 right-4 z-50 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-medium text-sm mb-1">语音录制失败</div>
              <div className="text-sm opacity-90">{voiceError}</div>
              {voiceError.includes('权限') && (
                <div className="mt-2 text-xs opacity-80">
                  <div className="font-medium">解决方法：</div>
                  <div>1. 点击地址栏的🔒图标</div>
                  <div>2. 允许麦克风权限</div>
                  <div>3. 刷新页面重试</div>
                </div>
              )}
            </div>
            <button
              onClick={() => setVoiceError(null)}
              className="ml-3 text-white hover:text-gray-200 flex-shrink-0"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}



      {/* 语音权限提示 */}
      {voicePermissionGranted === false && !voiceError && !voicePermissionTipClosed && (
        <div className="fixed bottom-24 left-4 right-4 z-40 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex items-center flex-1">
              <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-sm">
                <div className="font-medium">需要麦克风权限</div>
                <div>点击下方按钮申请麦克风权限以使用语音功能</div>
              </div>
            </div>
            <button
              onClick={handleCloseVoicePermissionTip}
              className="ml-2 flex-shrink-0 text-blue-500 hover:text-blue-700 transition-colors"
              aria-label="关闭提示"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* 底部输入区域 - 固定在底部 */}
      <div className="fixed bottom-0 left-0 right-0 z-40 bg-gray-50/80 backdrop-blur-sm px-4 py-3 safe-area-inset-bottom fixed-bottom-input">
        {inputMode === 'voice' ? (
          // 语音输入模式 - 占据全部宽度
          <div className="flex items-center space-x-3 w-full relative">

            <Button
              ref={voiceButtonRef}
              variant="solid"
              size="lg"
              className="flex-1 h-12 rounded-2xl flat-button text-white transition-colors duration-200 touch-manipulation select-none no-context-menu"
              style={{
                touchAction: 'manipulation', // 临时改为manipulation，允许基本触摸手势
                userSelect: 'none',
                WebkitUserSelect: 'none',
                backgroundColor: isRecording && recordingCanceled ? '#ef4444' :
                                isProcessingVoice ? '#6b7280' : '#00A76F'
              }}
              disabled={isProcessingVoice || isAIResponding}
              onTouchStart={handleVoiceStart}
              onTouchMove={handleVoiceMove}
              onTouchEnd={() => handleVoiceEndWithLog('touchEnd')}
              // 在PC端模拟环境下，只使用鼠标事件；移动端只使用触摸事件，避免事件冲突
              onMouseDown={isDesktopSimulation() ? handleVoiceStart : undefined}
              onMouseMove={isDesktopSimulation() ? handleVoiceMove : undefined}
              onMouseUp={isDesktopSimulation() ? () => handleVoiceEndWithLog('mouseUp') : undefined}
              onMouseLeave={isDesktopSimulation() ? () => handleVoiceEndWithLog('mouseLeave') : undefined}
              onContextMenu={(e) => {
                // 阻止右键菜单
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {isProcessingVoice ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span className="text-sm font-medium">识别中...</span>
                </div>
              ) : isRecording ? (
                <div className="flex items-center space-x-2">
                  <div className="flex items-end space-x-0.5">
                    <div className="w-1 bg-white rounded-full voice-wave" style={{ animationDelay: '0s' }}></div>
                    <div className="w-1 bg-white rounded-full voice-wave" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-1 bg-white rounded-full voice-wave" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-1 bg-white rounded-full voice-wave" style={{ animationDelay: '0.3s' }}></div>
                    <div className="w-1 bg-white rounded-full voice-wave" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-sm font-medium">
                    {recordingCanceled ? '向上滑动取消' : '正在录音...'}
                  </span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  <span className="text-sm font-medium">
                    {voicePermissionGranted === null ? '检查权限中...' :
                     voicePermissionGranted === false ? '点击申请权限' : '按住说话'}
                  </span>
                </div>
              )}
            </Button>

            <Button
              variant="bordered"
              size="sm"
              className="w-12 h-12 rounded-xl flat-button flex-shrink-0"
              onPress={() => {
                if (isProcessingVoice) {
                  forceStopASR();
                } else {
                  setInputMode('text');
                }
              }}
            >
              {isProcessingVoice ? (
                <span className="text-xs font-medium text-gray-600">取消</span>
              ) : (
                <img
                  src="/icon/keyboard.svg"
                  alt="键盘输入"
                  className="w-4 h-4"
                  style={{ filter: 'brightness(0) saturate(100%) invert(42%) sepia(8%) saturate(1084%) hue-rotate(169deg) brightness(95%) contrast(89%)' }}
                />
              )}
            </Button>
          </div>
        ) : (
          // 文本输入模式 - 优化布局设计
          <div className="flex items-end w-full relative">
            <div className="flex-1 relative">
              {/* 语音按钮 Tag 样式 - 固定在左上角，不跟随高度变化 */}
              <button
                onClick={() => setInputMode('voice')}
                className="absolute -top-2 left-2 z-10 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg hover:bg-green-600 transition-colors duration-200 flex items-center space-x-1"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                <span>语音</span>
              </button>

              {/* 文本输入框容器 */}
              <div className="relative bg-white rounded-2xl border border-gray-200 focus-within:border-green-500 focus-within:ring-2 focus-within:ring-green-100 transition-all duration-200 shadow-sm">
                <textarea
                  placeholder="输入消息..."
                  value={textInput}
                  onChange={handleTextInputChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendTextMessage();
                    }
                  }}
                  className="w-full pl-4 pr-14 py-3 text-base bg-transparent border-none resize-none focus:outline-none rounded-2xl"
                  style={{
                    fontSize: '16px',
                    height: `${textareaHeight}px`,
                    minHeight: '48px',
                    maxHeight: '120px',
                    overflowY: textareaHeight >= 120 ? 'auto' : 'hidden',
                    lineHeight: '24px',
                    paddingTop: '16px', // 固定值，避免动态计算导致的重新渲染
                    paddingBottom: '12px'
                  }}
                />

                {/* 发送按钮 - 内嵌在输入框右侧 */}
                <div className="absolute right-2 bottom-2">
                  <Button
                    variant="solid"
                    size="sm"
                    className={`w-10 h-10 text-sm flat-button text-white rounded-xl flex-shrink-0 min-w-0 transition-all duration-200 ${
                      textInput.trim() && !isAIResponding ? 'send-button-enabled' : 'send-button-disabled'
                    }`}
                    isDisabled={!textInput.trim() || isAIResponding}
                    onPress={sendTextMessage}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>

      {/* 侧边栏 */}
      {isSidebarOpen && (
        <div className="fixed inset-0 z-50 flex">
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={() => setIsSidebarOpen(false)}
          />

          {/* 侧边栏内容 - 白色背景 */}
          <div className="relative w-80 h-full shadow-2xl flex flex-col bg-white border-r border-gray-200">
            {/* 侧边栏头部 */}
            <div className="flex items-center justify-between p-4 border-b border-gray-50 bg-white">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                聊天历史
              </h2>
              <button
                onClick={() => setIsSidebarOpen(false)}
                className="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center transition-all duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 聊天历史列表 - 白色背景，支持下拉刷新 */}
            <div
              ref={sidebarScrollRef}
              className="flex-1 overflow-y-auto bg-white relative"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              style={{
                transform: pullRefreshState.isPulling ? `translate3d(0, ${pullRefreshState.pullDistance}px, 0)` : 'translate3d(0, 0, 0)',
                transition: pullRefreshState.isPulling ? 'none' : 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                willChange: pullRefreshState.isPulling ? 'transform' : 'auto'
              }}
            >
              {/* 下拉刷新指示器 */}
              <div
                className="absolute top-0 left-0 right-0 flex items-center justify-center bg-blue-50 border-b border-blue-100"
                style={{
                  height: `${Math.max(0, pullRefreshState.pullDistance)}px`,
                  opacity: pullRefreshState.isPulling ? Math.min(1, pullRefreshState.pullDistance / 40) : 0,
                  transform: `translate3d(0, -${Math.max(0, pullRefreshState.pullDistance)}px, 0)`,
                  transition: pullRefreshState.isPulling ? 'none' : 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  willChange: pullRefreshState.isPulling ? 'transform, opacity, height' : 'auto'
                }}
              >
                <div className="flex items-center space-x-2 text-blue-600">
                  {pullRefreshState.isRefreshing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="text-sm font-medium">正在刷新...</span>
                    </>
                  ) : pullRefreshState.canRefresh ? (
                    <>
                      <svg className="w-4 h-4 transform rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      <span className="text-sm font-medium">松开刷新</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      <span className="text-sm font-medium">下拉刷新</span>
                    </>
                  )}
                </div>
              </div>

              <div className="p-3">
                {/* 今天 */}
                {conversationHistory.today.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-xs font-medium text-blue-600 uppercase tracking-wider mb-3 px-2 flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                      今天
                    </h3>
                    <div className="space-y-2">
                      {conversationHistory.today.map((conv) => (
                        <div
                          key={conv.id}
                          className="group relative p-3 rounded-xl hover:bg-gray-50 transition-all duration-200 bg-slate-50"
                        >
                          <button
                            className="w-full text-left"
                            onClick={async () => {
                              setCurrentConversationId(conv.id);
                              setStorageConversationId(conv.id);
                              await loadConversation(conv.id);
                              setIsSidebarOpen(false);
                            }}
                          >
                            <p className="text-sm font-medium text-gray-800 truncate pr-8">{conv.title}</p>
                            <p className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {new Date(conv.updated_at).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                            </p>
                          </button>
                          {/* 删除按钮 - 直接显示 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversationHandler(conv.id);
                            }}
                            className="absolute top-2 right-2 p-1 hover:bg-red-100 rounded-full transition-colors duration-200"
                            title="删除会话"
                          >
                            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 昨天 */}
                {conversationHistory.yesterday.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-xs font-medium text-purple-600 uppercase tracking-wider mb-3 px-2 flex items-center">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                      昨天
                    </h3>
                    <div className="space-y-2">
                      {conversationHistory.yesterday.map((conv) => (
                        <div
                          key={conv.id}
                          className="group relative p-3 rounded-xl hover:bg-gray-50 transition-all duration-200"
                        >
                          <button
                            className="w-full text-left"
                            onClick={async () => {
                              setCurrentConversationId(conv.id);
                              setStorageConversationId(conv.id);
                              await loadConversation(conv.id);
                              setIsSidebarOpen(false);
                            }}
                          >
                            <p className="text-sm font-medium text-gray-800 truncate pr-8">{conv.title}</p>
                            <p className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              昨天 {new Date(conv.updated_at).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                            </p>
                          </button>
                          {/* 删除按钮 - 直接显示 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversationHandler(conv.id);
                            }}
                            className="absolute top-2 right-2 p-1 hover:bg-red-100 rounded-full transition-colors duration-200"
                            title="删除会话"
                          >
                            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 本周 */}
                {conversationHistory.thisWeek.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-xs font-medium text-green-600 uppercase tracking-wider mb-3 px-2 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      本周
                    </h3>
                    <div className="space-y-2">
                      {conversationHistory.thisWeek.map((conv) => (
                        <div
                          key={conv.id}
                          className="group relative p-3 rounded-xl hover:bg-gray-50 transition-all duration-200"
                        >
                          <button
                            className="w-full text-left"
                            onClick={async () => {
                              setCurrentConversationId(conv.id);
                              setStorageConversationId(conv.id);
                              await loadConversation(conv.id);
                              setIsSidebarOpen(false);
                            }}
                          >
                            <p className="text-sm font-medium text-gray-800 truncate pr-8">{conv.title}</p>
                            <p className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {Math.ceil((Date.now() - conv.updated_at) / (1000 * 60 * 60 * 24))}天前
                            </p>
                          </button>
                          {/* 删除按钮 - 直接显示 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversationHandler(conv.id);
                            }}
                            className="absolute top-2 right-2 p-1 hover:bg-red-100 rounded-full transition-colors duration-200"
                            title="删除会话"
                          >
                            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 更早 */}
                {conversationHistory.older.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wider mb-3 px-2 flex items-center">
                      <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                      更早
                    </h3>
                    <div className="space-y-2">
                      {conversationHistory.older.slice(0, 10).map((conv) => (
                        <div
                          key={conv.id}
                          className="group relative p-3 rounded-xl hover:bg-gray-50 transition-all duration-200"
                        >
                          <button
                            className="w-full text-left"
                            onClick={async () => {
                              setCurrentConversationId(conv.id);
                              setStorageConversationId(conv.id);
                              await loadConversation(conv.id);
                              setIsSidebarOpen(false);
                            }}
                          >
                            <p className="text-sm font-medium text-gray-800 truncate pr-8">{conv.title}</p>
                            <p className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              {new Date(conv.updated_at).toLocaleDateString('zh-CN')}
                            </p>
                          </button>
                          {/* 删除按钮 - 直接显示 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversationHandler(conv.id);
                            }}
                            className="absolute top-2 right-2 p-1 hover:bg-red-100 rounded-full transition-colors duration-200"
                            title="删除会话"
                          >
                            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 如果没有历史记录 */}
                {conversationHistory.today.length === 0 &&
                 conversationHistory.yesterday.length === 0 &&
                 conversationHistory.thisWeek.length === 0 &&
                 conversationHistory.older.length === 0 && (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-2">
                      <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-500">还没有聊天记录</p>
                    <p className="text-xs text-gray-400 mt-1">开始您的第一次对话吧！</p>
                  </div>
                )}
              </div>
            </div>

            {/* 用户信息区域 - 调低高度，整个区域可点击 */}
            <button
              className="w-full p-3 bg-gradient-to-r from-blue-500/5 to-purple-500/5 backdrop-blur-sm hover:from-blue-500/10 hover:to-purple-500/10 transition-all duration-200"
              onClick={onSettingsClick}
            >
              <div className="flex items-center space-x-2 p-2">
                {/* 左侧头像 */}
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 shadow-lg overflow-hidden">
                  {userAvatar ? (
                    <img
                      src={userAvatar}
                      alt="用户头像"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  )}
                </div>

                {/* 中间用户信息 */}
                <div className="flex-1 min-w-0">
                  {/* 用户昵称 - 只在有数据时显示 */}
                  {(userProfile?.username || userNickname) && (
                    <p className="text-xs font-semibold text-gray-800 truncate flex items-center">
                      {userProfile?.username || userNickname}
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full ml-1.5 animate-pulse"></div>
                    </p>
                  )}

                  {/* 用户电话 - 只在有数据时显示 */}
                  {(userProfile?.phone || userPhone) && (
                    <p className="text-xs text-gray-600 flex items-center">
                      <svg className="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      {userProfile?.phone || userPhone}
                    </p>
                  )}

                  {/* 用户邮箱 - 只在有数据时显示 */}
                  {userProfile?.email && (
                    <p className="text-xs text-gray-400 truncate flex items-center">
                      <svg className="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                      {userProfile.email}
                    </p>
                  )}

                  {/* 如果没有任何用户信息，显示一个简单的在线状态 */}
                  {!(userProfile?.username || userNickname) && !(userProfile?.phone || userPhone) && !userProfile?.email && (
                    <p className="text-xs text-gray-600 flex items-center">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse"></div>
                      在线
                    </p>
                  )}
                </div>

                {/* 右侧三个点 */}
                <div className="w-6 h-6 rounded-full hover:bg-white/30 flex items-center justify-center transition-all duration-200 backdrop-blur-sm border border-white/10 hover:border-white/20">
                  <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </div>
              </div>
            </button>
          </div>
        </div>
      )}



      {/* 图片灯箱 */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={lightboxSlides}
        index={lightboxIndex}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
        carousel={{
          finite: lightboxSlides.length <= 1,
        }}
        render={{
          buttonPrev: lightboxSlides.length <= 1 ? () => null : undefined,
          buttonNext: lightboxSlides.length <= 1 ? () => null : undefined,
        }}
        styles={{
          container: { backgroundColor: "rgba(0, 0, 0, .9)" },
        }}
      />

      {/* 删除确认对话框 */}
      {deleteConfirmDialog.isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={!deleteConfirmDialog.isDeleting ? cancelDeleteConversation : undefined}
          />

          {/* 对话框内容 */}
          <div className="relative bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
            <div className="text-center">
              {/* 图标 */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>

              {/* 标题和描述 */}
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                确认删除对话
              </h3>
              <p className="text-sm text-gray-500 mb-6">
                此操作不可撤销，确定要删除这个对话吗？
              </p>

              {/* 按钮 */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-xl transition-colors disabled:opacity-50"
                  onClick={cancelDeleteConversation}
                  disabled={deleteConfirmDialog.isDeleting}
                >
                  取消
                </button>
                <button
                  type="button"
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-xl transition-colors disabled:opacity-50 flex items-center justify-center"
                  onClick={confirmDeleteConversation}
                  disabled={deleteConfirmDialog.isDeleting}
                >
                  {deleteConfirmDialog.isDeleting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                      删除中...
                    </>
                  ) : (
                    '确认删除'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatPage;
