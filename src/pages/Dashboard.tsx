/**
 * 主页面/仪表板
 * 用户登录成功后显示的页面
 */

import React from 'react';
import { Button } from '@heroui/button';
import { getToken, decodeToken } from '../utils/jwtUtils';
import { logout } from '../utils/dataCleanup';

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const handleLogout = async () => {
    try {
      await logout(); // 使用新的logout函数，先调用API再清空所有localStorage数据
      onLogout();
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使出错也要调用onLogout，确保UI状态正确
      onLogout();
    }
  };

  // 获取用户信息
  const token = getToken();
  const userInfo = token ? decodeToken(token) : null;

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-md mx-auto px-6 py-8">
        {/* 头部 */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 border-2 flex items-center justify-center mx-auto mb-4" style={{ borderColor: '#00A76F', backgroundColor: '#00A76F10' }}>
            <svg
              className="w-10 h-10"
              fill="none"
              stroke="#00A76F"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            登录成功
          </h1>
          <p className="text-gray-600">
            {userInfo?.sub ? `账号: ${userInfo.sub}` : '欢迎使用'}
          </p>
        </div>

        {/* 功能列表 */}
        <div className="space-y-4 mb-12">
          <div className="border-b border-gray-200 pb-4">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-blue-50 border border-blue-200 flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">数据统计</h3>
                  <p className="text-gray-600 text-sm">查看您的数据概览</p>
                </div>
              </div>
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          </div>

          <div className="border-b border-gray-200 pb-4">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-purple-50 border border-purple-200 flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">设置</h3>
                  <p className="text-gray-600 text-sm">个人设置和偏好</p>
                </div>
              </div>
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* 令牌信息（开发调试用） */}
        {userInfo && (
          <div className="bg-gray-50 border border-gray-200 p-6 mb-8">
            <h3 className="font-semibold text-gray-900 mb-4">令牌信息</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">用户ID:</span>
                <span className="text-gray-900 font-medium">{userInfo.sub}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">签发时间:</span>
                <span className="text-gray-900 font-medium">
                  {new Date(userInfo.iat * 1000).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">过期时间:</span>
                <span className="text-gray-900 font-medium">
                  {new Date(userInfo.exp * 1000).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 退出登录按钮 */}
        <Button
          variant="bordered"
          size="lg"
          className="w-full h-14 text-base font-semibold btn-primary-outline flat-button"
          onPress={handleLogout}
        >
          退出登录
        </Button>
      </div>
    </div>
  );
};

export default Dashboard;
