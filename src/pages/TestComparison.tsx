import React, { useState } from 'react';
import CarComparison from '../components/CarComparison';

const TestComparison: React.FC = () => {
  const [showComparison, setShowComparison] = useState(false);

  // 示例数据
  const testData = [
    {
      "id": **********,
      "vehicle_name": "奥迪 Q3 2013款 2.0TFSI 双离合 35TFSI 舒适型",
      "price": 130000,
      "first_photo_url": "group1/M00/43/E4/wKghIF6dXnuAGZnTAALyd9MrzbM522.jpg",
      "pic_url": "http://images.autostreets.com/group1/M00/73/5D/wKghH16dXnuAQK1uAAKNnvvVodg432.jpg",
      "brand": "奥迪",
      "brand_series": "Q3",
      "model_year": "2013",
      "display_mileage": 89200.0,
      "register_license_years": "201306",
      "using_model": "非营运",
      "current_city": "贵阳市",
      "transmission_type": "自动",
      "swept_volume": "2.0",
      "oil_type": "汽油",
      "body_color": "棕色",
      "produced_years": "201303",
      "level": "紧凑型车",
      "interior_color": "黑色",
      "power": 125.0,
      "seat_number": 5,
      "star_condition": 0.9,
      "star_electric": 0.9,
      "star_facade": 0.9,
      "star_interior": 0.9,
      "star_skeleton": 0.9,
      "current_province": "贵州省",
      "transmission_desc": "双离合变速器(DCT)",
      "cylinder_volume": "1984",
      "air_type": "涡轮增压",
      "max_horsepower": "170",
      "max_power": "125",
      "max_torque": "280",
      "oil_number": "95#",
      "standard_oil_consumption": "7.9",
      "acceleration_time": "8.5",
      "max_speed": "212",
      "length": "4385",
      "width": "1831",
      "height": "1608",
      "wheel_base": "2603",
      "weight": "1560",
      "is_abs": "有",
      "is_stability_control": "有",
      "is_auto_air_conditioning": "有",
      "is_electric_skylight": "有",
      "is_panorama_skylight": "有",
      "is_leather_seat": "有",
      "is_electric_adjust_driver_seat": "有",
      "is_reversing_radar": "有"
    },
    {
      "id": **********,
      "vehicle_name": "宝马 X1 2016款 sDrive18Li1.5T 手自一体 时尚型",
      "price": 87000,
      "first_photo_url": "group1/M00/2D/CA/wKghIF4NbYKAdtPvAAO931asioY335.jpg",
      "pic_url": "http://images.autostreets.com/group1/M00/2D/CA/wKghIF4NbYKACUCcAAN3QIS_zJ4114.jpg",
      "brand": "宝马",
      "brand_series": "X1",
      "model_year": "2016",
      "display_mileage": 28195.0,
      "register_license_years": "201708",
      "using_model": "非营运",
      "current_city": "天津市",
      "transmission_type": "自动",
      "swept_volume": "1.5",
      "oil_type": "汽油",
      "body_color": "棕色",
      "produced_years": "201704",
      "level": "紧凑型车",
      "interior_color": "黑色",
      "power": 100.0,
      "seat_number": 5,
      "star_condition": 0.9,
      "star_electric": 0.9,
      "star_facade": 0.9,
      "star_interior": 0.9,
      "star_skeleton": 0.9,
      "current_province": "天津市",
      "transmission_desc": "手自一体变速器(AT)",
      "cylinder_volume": "1499",
      "air_type": "涡轮增压",
      "max_horsepower": "136",
      "max_power": "100",
      "max_torque": "220",
      "oil_number": "95#",
      "standard_oil_consumption": "6.1",
      "acceleration_time": "10.5",
      "max_speed": "195",
      "length": "4565",
      "width": "1821",
      "height": "1624",
      "wheel_base": "2780",
      "weight": "1550",
      "is_abs": "有",
      "is_stability_control": "有",
      "is_auto_air_conditioning": "有",
      "is_electric_skylight": "有",
      "is_panorama_skylight": "有",
      "is_leather_seat": "有",
      "is_electric_adjust_driver_seat": "有",
      "is_reversing_radar": "有"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-xl font-bold mb-4">车辆对比测试</h1>
        
        <button
          onClick={() => setShowComparison(true)}
          className="w-full py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          查看车辆对比
        </button>

        <div className="mt-4 text-sm text-gray-600">
          <p>测试数据：</p>
          <ul className="list-disc list-inside mt-2">
            <li>奥迪 Q3 2013款 - 13万元</li>
            <li>宝马 X1 2016款 - 8.7万元</li>
          </ul>
        </div>
      </div>

      {showComparison && (
        <CarComparison
          cars={testData as any}
          onClose={() => setShowComparison(false)}
        />
      )}
    </div>
  );
};

export default TestComparison;
