/**
 * 登录页面
 * 企业级简洁登录界面，支持国际区号选择
 */

import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@heroui/input';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import { Avatar } from '@heroui/avatar';
import {
  validatePhoneNumber,
  validateVerificationCode,
  sendVerificationCodeAPI,
  loginWithCodeAPI,
  getUserProfileAPI
} from '../utils/auth';
import { saveLoginData } from '../utils/jwtUtils';
import { savePhone, saveUserProfile } from '../utils/userInfoUtils';
import { COUNTRY_DATA, getDefaultCountry } from '../utils/countryData';

interface LoginProps {
  onLoginSuccess: () => void;
  onBackClick: () => void;
}

const Login: React.FC<LoginProps> = ({ onLoginSuccess, onBackClick }) => {
  const [phone, setPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [selectedCountry, setSelectedCountry] = useState(getDefaultCountry());
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [codeError, setCodeError] = useState('');

  // 定时器引用，用于清理
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
      }
    };
  }, []);

  // 实时验证手机号
  const validatePhoneInput = (phoneValue: string) => {
    const validation = validatePhoneNumber(phoneValue, selectedCountry.dialCode);
    setPhoneError(validation.isValid ? '' : validation.message ?? '');
    return validation.isValid;
  };

  // 实时验证验证码
  const validateCodeInput = (codeValue: string) => {
    const validation = validateVerificationCode(codeValue);
    setCodeError(validation.isValid ? '' : validation.message ?? '');
    return validation.isValid;
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhoneInput(phone)) {
      return;
    }

    setIsSendingCode(true);
    setError('');

    try {
      // 使用完整的手机号（包含国家代码）调用真实API
      const fullPhoneNumber = `${selectedCountry.dialCode}${phone}`;
      const result = await sendVerificationCodeAPI(fullPhoneNumber);

      if (result.success) {
        // 自动填充验证码
        if (result.code) {
          setVerificationCode(result.code);
          console.log('验证码已自动填充:', result.code);
        }

        // 清理之前的定时器（如果存在）
        if (countdownTimerRef.current) {
          clearInterval(countdownTimerRef.current);
        }

        // 开始倒计时
        setCountdown(60);
        countdownTimerRef.current = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              if (countdownTimerRef.current) {
                clearInterval(countdownTimerRef.current);
                countdownTimerRef.current = null;
              }
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      console.error('发送验证码错误:', err);
      setError('网络错误，请重试');
    } finally {
      setIsSendingCode(false);
    }
  };

  // 处理登录
  const handleLogin = async () => {
    if (!validatePhoneInput(phone) || !validateCodeInput(verificationCode)) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 使用完整的手机号（包含国家代码）调用真实登录API
      const fullPhoneNumber = `${selectedCountry.dialCode}${phone}`;
      const result = await loginWithCodeAPI(fullPhoneNumber, verificationCode);

      if (result.success && result.loginData) {
        // 保存登录数据到localStorage（排除user_info）
        saveLoginData(result.loginData);

        // 保存登录时使用的完整电话号码
        savePhone(fullPhoneNumber);

        console.log('登录成功，数据已保存');

        // 异步获取用户资料，不阻塞登录流程
        getUserProfileAPI()
          .then(profileResult => {
            if (profileResult.success && profileResult.userProfile) {
              saveUserProfile(profileResult.userProfile);
              console.log('用户资料获取成功并已缓存');
            } else {
              console.warn('获取用户资料失败:', profileResult.message);
            }
          })
          .catch(error => {
            console.warn('获取用户资料时发生错误:', error);
          });

        onLoginSuccess();
      } else {
        setError(result.message || '登录失败');
      }
    } catch (err) {
      console.error('登录错误:', err);
      setError('网络错误，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航栏 */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-100">
        <button
          onClick={onBackClick}
          className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors"
        >
          <svg 
            className="w-6 h-6 text-gray-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 19l-7-7 7-7" 
            />
          </svg>
        </button>
        <h1 className="text-lg font-semibold text-gray-900">
          手机号登录
        </h1>
        <div className="w-10"></div>
      </div>

      {/* 主要内容区域 - 居上对齐 */}
      <div className="px-6 py-8">
        <div className="w-full max-w-sm mx-auto">
          {/* 说明文字 */}
          <div className="text-center mb-8">
            <p className="text-gray-600 text-base">
              请输入手机号和验证码完成登录
            </p>
          </div>

          {/* 表单区域 */}
          <div className="space-y-6">
            {/* 手机号输入区域 */}
            <div className="space-y-2">
              <label htmlFor="phone-input" className="text-sm font-medium text-gray-700">手机号</label>
              <div className="flex gap-3">
                {/* 区号选择 - 优化宽度和自适应 */}
                <div className="flex-shrink-0" style={{ minWidth: '120px', maxWidth: '140px' }}>
                  <Select
                    selectedKeys={[selectedCountry.code]}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0] as string;
                      const country = COUNTRY_DATA.find(c => c.code === selectedKey);
                      if (country) {
                        setSelectedCountry(country);
                        // 使用新选择的国家进行验证，而不是依赖状态更新
                        if (phone) {
                          const validation = validatePhoneNumber(phone, country.dialCode);
                          setPhoneError(validation.isValid ? '' : validation.message ?? '');
                        }
                      }
                    }}
                    classNames={{
                      trigger: "h-12 flat-input min-w-full",
                      value: "text-sm font-medium",
                      popoverContent: "min-w-[280px] max-w-[320px]",
                      listbox: "max-h-[300px]"
                    }}
                    renderValue={(items) => {
                      return items.map((item) => {
                        const country = COUNTRY_DATA.find(c => c.code === item.key);
                        return (
                          <div key={item.key} className="flex items-center gap-2 w-full">
                            <Avatar
                              src={country?.flagUrl}
                              className="w-5 h-4 flex-shrink-0"
                              radius="none"
                            />
                            <span className="text-sm font-medium truncate">{country?.dialCode}</span>
                          </div>
                        );
                      });
                    }}
                    placeholder="选择区号"
                  >
                    {COUNTRY_DATA.map((country) => (
                      <SelectItem key={country.code}>
                        <div className="flex items-center gap-3 py-1">
                          <Avatar
                            src={country.flagUrl}
                            className="w-6 h-4 flex-shrink-0"
                            radius="none"
                          />
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="text-sm font-medium truncate">{country.name}</span>
                            <span className="text-xs text-gray-500">{country.dialCode}</span>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                {/* 手机号输入 */}
                <div className="flex-1">
                  <Input
                    id="phone-input"
                    type="tel"
                    placeholder="请输入手机号"
                    value={phone}
                    onValueChange={(value) => {
                      setPhone(value);
                      validatePhoneInput(value);
                    }}
                    classNames={{
                      input: "text-base",
                      inputWrapper: `h-12 flat-input ${phoneError ? 'is-invalid' : ''}`
                    }}
                    isInvalid={!!phoneError}
                    errorMessage={phoneError}
                  />
                </div>
              </div>
            </div>

            {/* 验证码输入区域 */}
            <div className="space-y-2">
              <label htmlFor="code-input" className="text-sm font-medium text-gray-700">验证码</label>
              <div className="flex gap-3">
                <div className="flex-1">
                  <Input
                    id="code-input"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={verificationCode}
                    onValueChange={(value) => {
                      setVerificationCode(value);
                      validateCodeInput(value);
                    }}
                    maxLength={6}
                    classNames={{
                      input: "text-base tracking-wider",
                      inputWrapper: `h-12 flat-input ${codeError ? 'is-invalid' : ''}`
                    }}
                    isInvalid={!!codeError}
                    errorMessage={codeError}
                  />
                </div>
                <Button
                  variant="solid"
                  size="lg"
                  className="h-12 px-4 text-sm flat-button text-white"
                  style={{
                    backgroundColor: (!phone || !!phoneError || countdown > 0) ? '#9ca3af' : '#00A76F'
                  }}
                  isLoading={isSendingCode}
                  isDisabled={!phone || !!phoneError || countdown > 0}
                  onPress={handleSendCode}
                >
                  {countdown > 0 ? `${countdown}s` : '发送验证码'}
                </Button>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="text-red-500 text-sm text-center bg-red-50 p-3 rounded-lg">
                {error}
              </div>
            )}

            {/* 登录按钮 */}
            <Button
              variant="solid"
              size="lg"
              className="w-full h-14 text-base font-semibold text-white mt-8 btn-primary flat-button"
              style={{ backgroundColor: '#00A76F' }}
              isLoading={isLoading}
              isDisabled={!phone || !verificationCode || !!phoneError || !!codeError}
              onPress={handleLogin}
            >
              {isLoading ? '登录中...' : '登录'}
            </Button>
          </div>

          {/* 底部说明 */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500 leading-relaxed">
              登录即表示同意服务条款和隐私政策
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
