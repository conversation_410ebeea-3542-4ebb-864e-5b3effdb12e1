/**
 * 车辆推荐记录页面
 * 按日期分组显示所有车辆推荐记录
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getGroupedCarRecommendations, type CarRecommendationRecord } from '../utils/conversationStorage';

interface CarRecommendationHistoryPageProps {
  onBack: () => void;
  onCarDetailClick?: (carData: any) => void;
  fromSettings?: boolean; // 🔥 新增：标识是否来自设置页面
}

// 车辆卡片组件
const CarRecommendationCard: React.FC<{ car: any; onCarDetailClick?: (carData: any) => void }> = ({
  car,
  onCarDetailClick
}) => {
  if (!car) return null;

  // 生成图片URL
  const imageUrl = car.pic_url ?? (car.first_photo_url ? `http://images.autostreets.com/${car.first_photo_url}` : null);

  const handleCardClick = () => {
    if (onCarDetailClick) {
      onCarDetailClick(car);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCardClick();
    }
  };

  return (
    <button
      className="w-full bg-white rounded-lg p-2 border border-gray-200 hover:bg-gray-50 transition-colors text-left"
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
      type="button"
    >
      <div className="flex space-x-3">
        {/* 车辆图片 */}
        <div className="flex-shrink-0">
          <div className="w-28 h-20 bg-gray-100 rounded-lg overflow-hidden">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt={car.vehicle_name ?? '车辆图片'}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                暂无图片
              </div>
            )}
          </div>
        </div>

        {/* 车辆信息 */}
        <div className="flex-1 min-w-0 flex flex-col justify-center">
          {/* 车辆名称 */}
          <h3 className="text-xs font-semibold text-gray-800 leading-tight line-clamp-2 mb-1.5">
            {car.vehicle_name ?? '未知车型'}
          </h3>

          {/* 车辆详细信息 - 3列布局 */}
          <div className="grid grid-cols-3 gap-1.5 text-xs">
            <div className="text-center">
              <div className="text-[#00A76F] font-bold text-xs">
                {car.price ? `${(car.price / 10000).toFixed(1)}万元` : '价格面议'}
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">价格</div>
            </div>
            <div className="text-center">
              <div className="text-gray-800 font-medium text-xs">
                {car.model_year ?? '未知'}年
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">上牌时间</div>
            </div>
            <div className="text-center">
              <div className="text-gray-800 font-medium text-xs">
                {car.display_mileage ? `${(car.display_mileage / 10000).toFixed(1)}万公里` : '未知'}
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">行驶里程</div>
            </div>
          </div>
        </div>
      </div>
    </button>
  );
};

const CarRecommendationHistoryPage: React.FC<CarRecommendationHistoryPageProps> = ({
  onBack,
  onCarDetailClick,
  fromSettings = false // 🔥 新增：接收fromSettings参数
}) => {
  const [groupedRecommendations, setGroupedRecommendations] = useState<Record<string, CarRecommendationRecord[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialRefreshing, setIsInitialRefreshing] = useState(false); // 🔥 新增：初始刷新状态

  // 下拉刷新相关状态
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [startY, setStartY] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 加载推荐记录
  const loadRecommendations = useCallback(async (forceRefresh: boolean = false, isInitialFromSettings: boolean = false) => {
    try {
      if (isInitialFromSettings) {
        // 🔥 来自设置页面的初始刷新 - 先加载缓存数据
        setIsInitialRefreshing(true);
        console.log('🔄 从设置页面进入，先显示缓存数据，然后获取最新数据...');

        // 先加载缓存数据，让用户立即看到内容
        const cachedGrouped = await getGroupedCarRecommendations(false, 50);
        setGroupedRecommendations(cachedGrouped);

        // 然后在后台获取最新数据
        const freshGrouped = await getGroupedCarRecommendations(true, 50);
        setGroupedRecommendations(freshGrouped);
        console.log('✅ 最新推荐记录获取成功');
      } else if (forceRefresh) {
        setIsRefreshing(true);
        const grouped = await getGroupedCarRecommendations(forceRefresh, 50);
        setGroupedRecommendations(grouped);
        console.log('✅ 推荐记录刷新成功');
      } else {
        setIsLoading(true);
        const grouped = await getGroupedCarRecommendations(forceRefresh, 50);
        setGroupedRecommendations(grouped);
        console.log('✅ 推荐记录加载成功');
      }
    } catch (error) {
      console.error('❌ 加载推荐记录失败:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsInitialRefreshing(false); // 🔥 重置初始刷新状态
    }
  }, []);

  // 初始加载
  useEffect(() => {
    // 🔥 如果来自设置页面，则强制刷新数据
    if (fromSettings) {
      loadRecommendations(false, true); // 第二个参数表示是来自设置页面的初始刷新
    } else {
      loadRecommendations(false);
    }
  }, [loadRecommendations, fromSettings]);

  // 下拉刷新处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (scrollContainerRef.current && scrollContainerRef.current.scrollTop === 0) {
      setStartY(e.touches[0].clientY);
      setIsPulling(true);
    }
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPulling || !scrollContainerRef.current) return;

    const currentY = e.touches[0].clientY;
    const distance = Math.max(0, currentY - startY);

    if (distance > 0 && scrollContainerRef.current.scrollTop === 0) {
      e.preventDefault();
      setPullDistance(Math.min(distance * 0.5, 80)); // 限制最大下拉距离
    }
  }, [isPulling, startY]);

  const handleTouchEnd = useCallback(() => {
    if (isPulling) {
      setIsPulling(false);

      if (pullDistance > 50) {
        // 触发刷新
        loadRecommendations(true);
      }

      setPullDistance(0);
    }
  }, [isPulling, pullDistance, loadRecommendations]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="flex items-center px-4 py-3">
          <button
            onClick={onBack}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800">车辆推荐记录</h1>
          {/* 刷新按钮 */}
          <button
            onClick={() => loadRecommendations(true)}
            disabled={isRefreshing || isInitialRefreshing}
            className="ml-auto p-2 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <svg
              className={`w-5 h-5 text-gray-600 ${(isRefreshing || isInitialRefreshing) ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* 🔥 新增：来自设置页面的数据刷新提示 */}
        {isInitialRefreshing && (
          <div className="bg-blue-50 border-b border-blue-100">
            <div className="flex items-center justify-center py-3 px-4">
              <svg className="w-4 h-4 text-blue-500 animate-spin mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span className="text-blue-600 text-sm font-medium">获取最新数据中...</span>
            </div>
          </div>
        )}
      </div>

      {/* 下拉刷新指示器 */}
      {pullDistance > 0 && (
        <div
          className="flex items-center justify-center py-2 bg-blue-50 transition-all duration-200"
          style={{
            transform: `translateY(${pullDistance}px)`,
            opacity: pullDistance / 50
          }}
        >
          <svg
            className={`w-5 h-5 text-blue-500 mr-2 ${pullDistance > 50 ? 'animate-spin' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span className="text-sm text-blue-600">
            {pullDistance > 50 ? '释放刷新' : '下拉刷新'}
          </span>
        </div>
      )}

      {/* 内容区域 */}
      <div
        ref={scrollContainerRef}
        className="p-4 overflow-y-auto"
        style={{
          height: isInitialRefreshing
            ? 'calc(100vh - 60px - 48px)' // 🔥 当显示刷新提示时，减去提示条的高度
            : 'calc(100vh - 60px)'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {isLoading ? (
          // 加载状态
          <div className="flex items-center justify-center py-20">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : Object.keys(groupedRecommendations).length === 0 ? (
          // 空状态
          <div className="flex flex-col items-center justify-center py-20">
            <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500 text-center">
              暂无车辆推荐记录<br />
              <span className="text-sm">开始与AI对话获取车辆推荐吧</span>
            </p>
          </div>
        ) : (
          // 推荐记录列表
          <div className="space-y-6">
            {Object.entries(groupedRecommendations).map(([dateKey, records]) => (
              <div key={dateKey}>
                {/* 日期标题 */}
                <div className="text-center mb-4">
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {dateKey}
                  </span>
                </div>

                {/* 该日期下的推荐记录 */}
                <div className="space-y-3">
                  {records.map((record) => (
                    <div key={record.id}>
                      {/* 安全检查：确保recommendations是数组且不为空 */}
                      {Array.isArray(record.recommendations) && record.recommendations.length > 0 ? (
                        record.recommendations.map((car, carIndex) => (
                          <div key={`${record.id}-${carIndex}`} className="mb-3">
                            <CarRecommendationCard car={car} onCarDetailClick={onCarDetailClick} />
                          </div>
                        ))
                      ) : (
                        <div key={record.id} className="mb-3 p-4 bg-gray-50 rounded-lg text-center text-gray-500 text-sm">
                          该记录暂无推荐数据
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部安全区域 */}
      <div className="h-20"></div>
    </div>
  );
};

export default CarRecommendationHistoryPage;
