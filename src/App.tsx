/**
 * 主应用组件
 * 根据设备类型和认证状态显示对应页面
 */

import { useState, useEffect } from 'react';
import { AuthStatus, checkAuthStatus, refreshUserProfile } from './utils/auth';
import PCNotSupported from './pages/PCNotSupported';
import Home from './pages/Home';
import Login from './pages/Login';
import ChatPage from './pages/ChatPage';
import TestComparison from './pages/TestComparison';
import CarDetailPage from './pages/CarDetailPage';
import SettingsPage from './pages/SettingsPage';
import CarRecommendationHistoryPage from './pages/CarRecommendationHistoryPage';
import CarComparison from './components/CarComparison';
import SpineTest from './pages/SpineTest';
import ErrorBoundary from './components/ErrorBoundary';


// 页面类型枚举
enum PageType {
  HOME = 'home',
  PHONE_LOGIN = 'phone_login',
  CAR_LOGIN = 'car_login',
  CAR_DETAIL = 'car_detail',
  SETTINGS = 'settings',
  CAR_RECOMMENDATION_HISTORY = 'car_recommendation_history',
  CAR_COMPARISON = 'car_comparison'
}

function App() {
  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.LOADING);
  const [currentPage, setCurrentPage] = useState<PageType>(PageType.HOME);
  const [selectedCarData, setSelectedCarData] = useState<any>(null);
  const [carDetailSourcePage, setCarDetailSourcePage] = useState<PageType>(PageType.HOME);
  const [comparisonData, setComparisonData] = useState<any[]>([]);
  const [chatScrollPosition, setChatScrollPosition] = useState<number | null>(null);
  const [autoLoadConversationId, setAutoLoadConversationId] = useState<string | null>(null); // 🔥 新增：自动加载的对话ID

  // 检查认证状态
  const checkAuth = () => {
    const status = checkAuthStatus();
    setAuthStatus(status);
  };

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  // 检查是否是测试页面
  if (window.location.pathname === '/test-comparison') {
    return <TestComparison />;
  }

  // 检查是否是 Spine 测试页面
  if (window.location.pathname === '/spine-test') {
    return <SpineTest />;
  }



  // 登录成功回调
  const handleLoginSuccess = async () => {
    setAuthStatus(AuthStatus.AUTHENTICATED);

    // 检查并刷新用户资料（如果需要）
    refreshUserProfile(false)
      .then(result => {
        if (result.success) {
          console.log('用户资料检查完成');
        } else {
          console.warn('用户资料检查失败:', result.message);
        }
      })
      .catch(error => {
        console.warn('用户资料检查时发生错误:', error);
      });

    // 🔥 登录成功后自动加载最近的对话记录
    try {
      console.log('🔄 登录成功，开始获取对话历史...');

      // 动态导入以避免循环依赖
      const { getGroupedConversations, startNewConversation, setCurrentConversationId } = await import('./utils/conversationStorage');

      // 获取对话历史
      const conversationHistory = await getGroupedConversations(true); // 强制刷新获取最新数据

      // 查找最近的对话记录（按优先级：今天 > 昨天 > 本周 > 更早）
      let latestConversation = null;

      if (conversationHistory.today.length > 0) {
        latestConversation = conversationHistory.today[0]; // 今天的第一条（最新）
      } else if (conversationHistory.yesterday.length > 0) {
        latestConversation = conversationHistory.yesterday[0]; // 昨天的第一条
      } else if (conversationHistory.thisWeek.length > 0) {
        latestConversation = conversationHistory.thisWeek[0]; // 本周的第一条
      } else if (conversationHistory.older.length > 0) {
        latestConversation = conversationHistory.older[0]; // 更早的第一条
      }

      if (latestConversation) {
        // 有对话记录，设置为当前对话
        console.log('✅ 找到最近的对话记录，自动加载:', {
          id: latestConversation.id,
          title: latestConversation.title,
          updated_at: new Date(latestConversation.updated_at).toLocaleString()
        });
        setCurrentConversationId(latestConversation.id);
        setAutoLoadConversationId(latestConversation.id);
      } else {
        // 没有对话记录，创建新对话
        console.log('📝 没有找到对话记录，创建新对话...');
        const newConversationId = await startNewConversation();
        console.log('✅ 新对话创建成功:', newConversationId);
        setAutoLoadConversationId(newConversationId);
      }
    } catch (error) {
      console.error('❌ 登录后加载对话记录失败:', error);
      // 即使失败也不影响登录流程，用户可以手动创建对话
    }
  };

  // 退出登录回调
  const handleLogout = () => {
    setAuthStatus(AuthStatus.NEED_LOGIN);
    setCurrentPage(PageType.HOME);
  };

  // 页面导航处理函数
  const handlePhoneLoginClick = () => {
    setCurrentPage(PageType.PHONE_LOGIN);
  };

  const handleBackToHome = () => {
    setCurrentPage(PageType.HOME);
  };

  // 保存聊天页面滚动位置的函数（暂时保留供未来使用）
  // const saveChatScrollPosition = (position: number) => {
  //   setChatScrollPosition(position);
  // };

  // 车辆详情页面导航
  const handleCarDetailClick = (carData: any, sourcePage: PageType = PageType.HOME, scrollPosition?: number) => {
    console.log('🚗 进入车辆详情页，来源页面:', sourcePage);
    // 如果来源是聊天页面且提供了滚动位置，则保存
    if (sourcePage === PageType.HOME && scrollPosition !== undefined) {
      setChatScrollPosition(scrollPosition);
    }
    setSelectedCarData(carData);
    setCarDetailSourcePage(sourcePage);
    setCurrentPage(PageType.CAR_DETAIL);
  };

  const handleBackFromCarDetail = () => {
    console.log('🔙 从车辆详情页返回，目标页面:', carDetailSourcePage);
    setSelectedCarData(null);
    setCurrentPage(carDetailSourcePage); // 返回到来源页面
    // 注意：不在这里清除 chatScrollPosition，让 ChatPage 组件自己处理
  };

  // 设置页面导航
  const handleSettingsClick = () => {
    setCurrentPage(PageType.SETTINGS);
  };

  const handleBackFromSettings = () => {
    setCurrentPage(PageType.HOME); // 重置页面状态，让认证状态下显示聊天页面
  };

  // 车辆推荐记录页面导航
  const handleCarRecommendationHistoryClick = () => {
    setCurrentPage(PageType.CAR_RECOMMENDATION_HISTORY);
  };

  const handleBackFromCarRecommendationHistory = () => {
    setCurrentPage(PageType.SETTINGS); // 返回设置页面
  };

  // 车辆对比页面导航
  const handleCarComparisonClick = (cars: any[], scrollPosition?: number) => {
    console.log('🔄 对比按钮被点击，车辆数据:', cars);
    // 保存聊天页面的滚动位置
    if (scrollPosition !== undefined) {
      setChatScrollPosition(scrollPosition);
    }
    setComparisonData(cars);
    setCurrentPage(PageType.CAR_COMPARISON);
  };

  const handleBackFromCarComparison = () => {
    setComparisonData([]);
    setCurrentPage(PageType.HOME); // 返回聊天页面
    // 注意：不在这里清除 chatScrollPosition，让 ChatPage 组件自己处理
  };

  // 根据认证状态渲染对应页面
  switch (authStatus) {
    case AuthStatus.LOADING:
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      );

    case AuthStatus.PC_NOT_SUPPORTED:
      return <PCNotSupported />;

    case AuthStatus.NEED_LOGIN:
      // 根据当前页面状态显示对应的登录页面
      switch (currentPage) {
        case PageType.HOME:
          return (
            <ErrorBoundary>
              <Home
                onPhoneLoginClick={handlePhoneLoginClick}
              />
            </ErrorBoundary>
          );
        case PageType.PHONE_LOGIN:
          return (
            <ErrorBoundary>
              <Login
                onLoginSuccess={handleLoginSuccess}
                onBackClick={handleBackToHome}
              />
            </ErrorBoundary>
          );
        default:
          return (
            <ErrorBoundary>
              <Home
                onPhoneLoginClick={handlePhoneLoginClick}
              />
            </ErrorBoundary>
          );
      }

    case AuthStatus.AUTHENTICATED:
      // 如果当前页面是车辆详情页面，显示车辆详情
      if (currentPage === PageType.CAR_DETAIL && selectedCarData) {
        return (
          <ErrorBoundary>
            <CarDetailPage
              carData={selectedCarData}
              onBack={handleBackFromCarDetail}
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是设置页面，显示设置页面
      if (currentPage === PageType.SETTINGS) {
        return (
          <ErrorBoundary>
            <SettingsPage
              onBack={handleBackFromSettings}
              onLogout={handleLogout}
              onCarRecommendationHistoryClick={handleCarRecommendationHistoryClick}
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是车辆推荐记录页面，显示车辆推荐记录页面
      if (currentPage === PageType.CAR_RECOMMENDATION_HISTORY) {
        return (
          <ErrorBoundary>
            <CarRecommendationHistoryPage
              onBack={handleBackFromCarRecommendationHistory}
              onCarDetailClick={(carData) => handleCarDetailClick(carData, PageType.CAR_RECOMMENDATION_HISTORY)}
              fromSettings={true} // 🔥 新增：标识来自设置页面，触发数据刷新
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是车辆对比页面，显示车辆对比页面
      if (currentPage === PageType.CAR_COMPARISON) {
        console.log('📱 渲染对比页面，车辆数据:', comparisonData);
        return (
          <ErrorBoundary>
            <CarComparison
              cars={comparisonData}
              onClose={handleBackFromCarComparison}
              onCarDetailClick={(carData) => handleCarDetailClick(carData, PageType.CAR_COMPARISON)}
            />
          </ErrorBoundary>
        );
      }
      // 否则显示聊天页面
      return (
        <ErrorBoundary>
          <ChatPage
            onLogout={handleLogout}
            onCarDetailClick={(carData, scrollPosition) => handleCarDetailClick(carData, PageType.HOME, scrollPosition)}
            onSettingsClick={handleSettingsClick}
            onCarComparisonClick={(cars, scrollPosition) => handleCarComparisonClick(cars, scrollPosition)}
            restoreScrollPosition={chatScrollPosition}
            onScrollPositionRestored={() => setChatScrollPosition(null)}
            autoLoadConversationId={autoLoadConversationId} // 🔥 新增：传递自动加载的对话ID
            onAutoLoadCompleted={() => setAutoLoadConversationId(null)} // 🔥 新增：自动加载完成后清除ID
          />
        </ErrorBoundary>
      );

    default:
      return (
        <ErrorBoundary>
          <Home
            onPhoneLoginClick={handlePhoneLoginClick}
          />
        </ErrorBoundary>
      );
  }
}

export default App;
