/**
 * 头像修改抽屉组件
 * 从下往上滑出的抽屉，包含拍照和相册选择功能
 */

import React, { useRef, useState } from 'react';
import { processAndSaveAvatar, validateImageFile, saveAvatarUrl } from '../utils/avatarUtils';
import { uploadAvatarAPI } from '../utils/auth';

interface AvatarDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onAvatarChange: (base64Image: string) => void;
}

const AvatarDrawer: React.FC<AvatarDrawerProps> = ({ isOpen, onClose, onAvatarChange }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 文件输入引用
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const albumInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = async (file: File) => {
    setError(null);
    setIsProcessing(true);

    try {
      // 验证文件
      const validation = validateImageFile(file);
      if (!validation.valid) {
        setError(validation.error || '文件验证失败');
        return;
      }

      // 先处理并保存本地头像（作为备份）
      const base64Image = await processAndSaveAvatar(file);

      // 尝试上传到服务器
      const uploadResult = await uploadAvatarAPI(file);

      if (uploadResult.success && uploadResult.avatarUrl) {
        // 上传成功，保存服务器URL
        saveAvatarUrl(uploadResult.avatarUrl);
        console.log('✅ 头像上传成功:', uploadResult.avatarUrl);

        // 通知父组件头像已更改（传递服务器URL）
        onAvatarChange(`https://api.st.vup.tools/api/v1/user/avatar/${uploadResult.avatarUrl}`);
      } else {
        // 上传失败，使用本地base64
        console.warn('⚠️ 头像上传失败，使用本地存储:', uploadResult.message);
        onAvatarChange(base64Image);
      }

      // 关闭抽屉
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '处理图片失败';
      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理相机拍照
  const handleCameraCapture = () => {
    if (cameraInputRef.current) {
      cameraInputRef.current.click();
    }
  };

  // 处理相册选择
  const handleAlbumSelect = () => {
    if (albumInputRef.current) {
      albumInputRef.current.click();
    }
  };

  // 文件输入变化处理
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="avatar-drawer-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 - 点击关闭 */}
      <div
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`}
        onClick={onClose}
      />

      {/* 抽屉容器 - 底部对齐 */}
      <div className="absolute bottom-0 left-0 right-0 flex justify-center">

        {/* 抽屉内容 */}
        <div
          className={`
            w-full max-w-md bg-white rounded-t-3xl shadow-2xl transform transition-transform duration-300 ease-out
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          `}
          onClick={(e) => e.stopPropagation()}
        >
        {/* 拖拽指示器 */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* 标题 */}
        <div className="px-6 py-4 border-b border-gray-100">
          <h2 id="avatar-drawer-title" className="text-lg font-semibold text-gray-800 text-center">头像修改</h2>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* 加载状态 */}
        {isProcessing && (
          <div className="mx-6 mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <p className="text-sm text-blue-600">正在处理图片...</p>
            </div>
          </div>
        )}

        {/* 选项列表 */}
        <div className="px-6 py-4 space-y-3">
          {/* 从相册中选一张 */}
          <button
            onClick={handleAlbumSelect}
            disabled={isProcessing}
            className={`
              w-full py-4 px-6 text-left rounded-xl border border-gray-200 transition-all duration-200
              ${isProcessing 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white hover:bg-gray-50 hover:border-gray-300 active:bg-gray-100'
              }
            `}
          >
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800">从相册中选一张</p>
                <p className="text-sm text-gray-500">选择已有的照片作为头像</p>
              </div>
            </div>
          </button>

          {/* 拍一张照片 */}
          <button
            onClick={handleCameraCapture}
            disabled={isProcessing}
            className={`
              w-full py-4 px-6 text-left rounded-xl border border-gray-200 transition-all duration-200
              ${isProcessing 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white hover:bg-gray-50 hover:border-gray-300 active:bg-gray-100'
              }
            `}
          >
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-800">拍一张照片</p>
                <p className="text-sm text-gray-500">使用相机拍摄新的头像</p>
              </div>
            </div>
          </button>
        </div>

        {/* 底部安全区域 */}
        <div className="h-6 safe-area-inset-bottom"></div>

        {/* 隐藏的文件输入 */}
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleInputChange}
          className="hidden"
        />
        <input
          ref={albumInputRef}
          type="file"
          accept="image/*"
          onChange={handleInputChange}
          className="hidden"
        />
        </div>
      </div>
    </div>
  );
};

export default AvatarDrawer;
