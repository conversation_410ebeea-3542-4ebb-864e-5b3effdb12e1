/**
 * 城市选择抽屉组件
 * 支持省市二级联动和IP定位功能
 */

import React, { useState, useEffect } from 'react';
import { Province, City } from '../data/chinaAreaData';
import { getCityByLocation, formatCityDisplay, CityMatchResult } from '../utils/locationUtils';
import { getProvinces, getCities } from '../utils/areaDataUtils';

interface CityPickerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onCitySelect: (province: Province, city: City) => void;
  currentCity?: string;
}

const CityPickerDrawer: React.FC<CityPickerDrawerProps> = ({
  isOpen,
  onClose,
  onCitySelect,
  currentCity
}) => {
  const [selectedProvince, setSelectedProvince] = useState<Province | null>(null);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [isLocating, setIsLocating] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [isLoadingProvinces, setIsLoadingProvinces] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);

  // 加载省份数据
  const loadProvinces = async () => {
    setIsLoadingProvinces(true);
    setLoadError(null);

    try {
      const provincesData = await getProvinces();
      setProvinces(provincesData);

      // 默认选择第一个省份
      if (provincesData.length > 0) {
        const defaultProvince = provincesData[0];
        setSelectedProvince(defaultProvince);
        // 加载第一个省份的城市数据
        await loadCitiesForProvince(defaultProvince.code);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载省份数据失败';
      setLoadError(errorMessage);
    } finally {
      setIsLoadingProvinces(false);
    }
  };

  // 加载指定省份的城市数据
  const loadCitiesForProvince = async (provinceCode: string) => {
    setIsLoadingCities(true);

    try {
      const citiesData = await getCities(provinceCode);
      setCities(citiesData);

      // 默认选择第一个城市（如果没有其他选中的城市）
      if (citiesData.length > 0) {
        setSelectedCity(citiesData[0]);
      }
    } catch (error) {
      console.error('加载城市数据失败:', error);
      setCities([]);
      setSelectedCity(null);
    } finally {
      setIsLoadingCities(false);
    }
  };

  // 匹配当前城市
  const matchCurrentCity = async () => {
    if (!currentCity || provinces.length === 0) return;

    try {
      for (const province of provinces) {
        const provinceCities = await getCities(province.code);
        const city = provinceCities.find(c => c.name === currentCity);
        if (city) {
          setSelectedProvince(province);
          setSelectedCity(city);
          setCities(provinceCities);
          console.log(`✅ 匹配到当前城市: ${province.name} - ${city.name}`);
          return;
        }
      }
      console.log(`⚠️ 未找到匹配的城市: ${currentCity}`);
    } catch (error) {
      console.error('匹配当前城市失败:', error);
    }
  };

  // 初始化数据加载
  useEffect(() => {
    if (isOpen) {
      loadProvinces();
    }
  }, [isOpen]);

  // 当省份数据加载完成且有当前城市时，尝试匹配
  useEffect(() => {
    if (provinces.length > 0 && currentCity) {
      matchCurrentCity();
    }
  }, [provinces, currentCity]);

  // 处理省份选择
  const handleProvinceSelect = async (province: Province) => {
    setSelectedProvince(province);
    setSelectedCity(null); // 清空当前选中的城市

    // 加载该省份的城市数据
    await loadCitiesForProvince(province.code);
  };

  // 处理城市选择
  const handleCitySelect = (city: City) => {
    setSelectedCity(city);
  };

  // 处理定位
  const handleLocation = async () => {
    setIsLocating(true);
    setLocationError(null);

    try {
      const result: CityMatchResult = await getCityByLocation();
      setSelectedProvince(result.province);
      setSelectedCity(result.city);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '定位失败';
      setLocationError(errorMessage);
    } finally {
      setIsLocating(false);
    }
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (selectedProvince && selectedCity) {
      onCitySelect(selectedProvince, selectedCity);
      onClose();
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="city-picker-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`} 
        onClick={onClose}
      />
      
      {/* 抽屉容器 */}
      <div className="absolute bottom-0 left-0 right-0 flex justify-center">
        {/* 抽屉内容 */}
        <div
          className={`
            w-full max-w-md bg-white rounded-t-3xl shadow-2xl transform transition-transform duration-300 ease-out flex flex-col
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          `}
          style={{ maxHeight: '80vh' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 拖拽指示器 */}
          <div className="flex justify-center pt-3 pb-2 flex-shrink-0">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* 标题 */}
          <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
            <h2 id="city-picker-title" className="text-lg font-semibold text-gray-800 text-center">
              意向看车城市修改
            </h2>
          </div>

          {/* 定位按钮 */}
          <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
            <button
              onClick={handleLocation}
              disabled={isLocating || isLoadingProvinces}
              className={`
                w-full py-3 px-4 rounded-xl border transition-all duration-200 flex items-center justify-center space-x-2
                ${isLocating || isLoadingProvinces
                  ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-white border-[#00A76F] text-[#00A76F] hover:bg-[#00A76F]/5 active:bg-[#00A76F]/10'
                }
              `}
            >
              <div className="w-5 h-5 flex items-center justify-center">
                {isLocating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#00A76F]"></div>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                )}
              </div>
              <span className="font-medium">
                {isLocating ? '定位中...' : '定位到当前城市'}
              </span>
            </button>

            {/* 错误提示 */}
            {(locationError || loadError) && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-xs text-red-600 text-center">{locationError || loadError}</p>
              </div>
            )}
          </div>

          {/* 省市选择区域 */}
          <div className="flex flex-1 min-h-0">
            {/* 省份列表 */}
            <div className="w-1/2 border-r border-gray-100 flex flex-col">
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-100 flex-shrink-0">
                <h3 className="text-sm font-medium text-gray-600 text-center">省份</h3>
              </div>
              <div className="overflow-y-auto flex-1">
                {isLoadingProvinces ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00A76F]"></div>
                    <span className="ml-2 text-sm text-gray-500">加载中...</span>
                  </div>
                ) : (
                  provinces.map((province) => (
                    <button
                      key={province.code}
                      onClick={() => handleProvinceSelect(province)}
                      className={`
                        w-full px-4 py-3 text-left text-sm transition-colors duration-200
                        ${selectedProvince?.code === province.code
                          ? 'bg-[#00A76F]/10 text-[#00A76F] font-medium'
                          : 'text-gray-700 hover:bg-gray-50'
                        }
                      `}
                    >
                      {province.name}
                    </button>
                  ))
                )}
              </div>
            </div>

            {/* 城市列表 */}
            <div className="w-1/2 flex flex-col">
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-100 flex-shrink-0">
                <h3 className="text-sm font-medium text-gray-600 text-center">城市</h3>
              </div>
              <div className="overflow-y-auto flex-1">
                {isLoadingCities ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00A76F]"></div>
                    <span className="ml-2 text-sm text-gray-500">加载中...</span>
                  </div>
                ) : (
                  cities.map((city) => (
                    <button
                      key={city.code}
                      onClick={() => handleCitySelect(city)}
                      className={`
                        w-full px-4 py-3 text-left text-sm transition-colors duration-200
                        ${selectedCity?.code === city.code
                          ? 'bg-[#00A76F]/10 text-[#00A76F] font-medium'
                          : 'text-gray-700 hover:bg-gray-50'
                        }
                      `}
                    >
                      {city.name}
                    </button>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* 确认按钮 */}
          <div className="px-6 py-4 border-t border-gray-100 flex-shrink-0">
            <button
              onClick={handleConfirm}
              disabled={!selectedProvince || !selectedCity}
              className={`
                w-full py-3 px-4 rounded-xl font-medium transition-all duration-200
                ${selectedProvince && selectedCity
                  ? 'bg-[#00A76F] text-white hover:bg-[#008f5f] active:bg-[#007a52] shadow-sm'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
            >
              确认选择
              {selectedProvince && selectedCity && (
                <span className="ml-2 text-sm opacity-90">
                  {formatCityDisplay(selectedProvince, selectedCity)}
                </span>
              )}
            </button>
          </div>

          {/* 底部安全区域 */}
          <div className="h-6 safe-area-inset-bottom flex-shrink-0"></div>
        </div>
      </div>
    </div>
  );
};

export default CityPickerDrawer;
