/**
 * 圆形国旗SVG组件
 * 支持中文、日文、英文三种语言的国旗显示
 */

import React from 'react';

interface FlagIconProps {
  size?: number;
  className?: string;
}

/**
 * 中国国旗 - 圆形
 */
export const ChinaFlag: React.FC<FlagIconProps> = ({ size = 32, className = '' }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" className={className}>
    <defs>
      <clipPath id="china-circle">
        <circle cx="16" cy="16" r="16" />
      </clipPath>
    </defs>
    
    {/* 红色背景 */}
    <circle cx="16" cy="16" r="16" fill="#DE2910" />
    
    {/* 大五角星 */}
    <g clipPath="url(#china-circle)">
      <polygon
        points="8,6 9.5,10.5 14,10.5 10.5,13 12,17.5 8,15 4,17.5 5.5,13 2,10.5 6.5,10.5"
        fill="#FFDE00"
        transform="translate(2, 2) scale(0.8)"
      />
      
      {/* 四个小五角星 */}
      <polygon
        points="16,8 16.8,10.2 19,10.2 17.1,11.6 17.9,13.8 16,12.4 14.1,13.8 14.9,11.6 13,10.2 15.2,10.2"
        fill="#FFDE00"
        transform="scale(0.6)"
      />
      <polygon
        points="16,12 16.8,14.2 19,14.2 17.1,15.6 17.9,17.8 16,16.4 14.1,17.8 14.9,15.6 13,14.2 15.2,14.2"
        fill="#FFDE00"
        transform="scale(0.6)"
      />
      <polygon
        points="14,16 14.8,18.2 17,18.2 15.1,19.6 15.9,21.8 14,20.4 12.1,21.8 12.9,19.6 11,18.2 13.2,18.2"
        fill="#FFDE00"
        transform="scale(0.6)"
      />
      <polygon
        points="12,12 12.8,14.2 15,14.2 13.1,15.6 13.9,17.8 12,16.4 10.1,17.8 10.9,15.6 9,14.2 11.2,14.2"
        fill="#FFDE00"
        transform="scale(0.6)"
      />
    </g>
  </svg>
);

/**
 * 日本国旗 - 圆形
 */
export const JapanFlag: React.FC<FlagIconProps> = ({ size = 32, className = '' }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" className={className}>
    <defs>
      <clipPath id="japan-circle">
        <circle cx="16" cy="16" r="16" />
      </clipPath>
    </defs>
    
    {/* 白色背景 */}
    <circle cx="16" cy="16" r="16" fill="#FFFFFF" stroke="#E5E7EB" strokeWidth="1" />
    
    {/* 红色圆圈 */}
    <g clipPath="url(#japan-circle)">
      <circle cx="16" cy="16" r="6" fill="#BC002D" />
    </g>
  </svg>
);

/**
 * 美国国旗 - 圆形
 */
export const USAFlag: React.FC<FlagIconProps> = ({ size = 32, className = '' }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" className={className}>
    <defs>
      <clipPath id="usa-circle">
        <circle cx="16" cy="16" r="16" />
      </clipPath>
    </defs>
    
    {/* 白色背景 */}
    <circle cx="16" cy="16" r="16" fill="#FFFFFF" />
    
    <g clipPath="url(#usa-circle)">
      {/* 红白条纹 */}
      <rect x="0" y="0" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="2.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="5" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="7.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="10" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="12.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="15" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="17.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="20" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="22.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="25" width="32" height="2.5" fill="#B22234" />
      <rect x="0" y="27.5" width="32" height="2.5" fill="#FFFFFF" />
      <rect x="0" y="30" width="32" height="2" fill="#B22234" />
      
      {/* 蓝色星区 */}
      <rect x="0" y="0" width="12.8" height="17.5" fill="#3C3B6E" />
      
      {/* 简化的星星 */}
      <g fill="#FFFFFF">
        <circle cx="2" cy="2" r="0.5" />
        <circle cx="4.5" cy="2" r="0.5" />
        <circle cx="7" cy="2" r="0.5" />
        <circle cx="9.5" cy="2" r="0.5" />
        <circle cx="12" cy="2" r="0.5" />
        
        <circle cx="3.25" cy="4" r="0.5" />
        <circle cx="5.75" cy="4" r="0.5" />
        <circle cx="8.25" cy="4" r="0.5" />
        <circle cx="10.75" cy="4" r="0.5" />
        
        <circle cx="2" cy="6" r="0.5" />
        <circle cx="4.5" cy="6" r="0.5" />
        <circle cx="7" cy="6" r="0.5" />
        <circle cx="9.5" cy="6" r="0.5" />
        <circle cx="12" cy="6" r="0.5" />
        
        <circle cx="3.25" cy="8" r="0.5" />
        <circle cx="5.75" cy="8" r="0.5" />
        <circle cx="8.25" cy="8" r="0.5" />
        <circle cx="10.75" cy="8" r="0.5" />
        
        <circle cx="2" cy="10" r="0.5" />
        <circle cx="4.5" cy="10" r="0.5" />
        <circle cx="7" cy="10" r="0.5" />
        <circle cx="9.5" cy="10" r="0.5" />
        <circle cx="12" cy="10" r="0.5" />
        
        <circle cx="3.25" cy="12" r="0.5" />
        <circle cx="5.75" cy="12" r="0.5" />
        <circle cx="8.25" cy="12" r="0.5" />
        <circle cx="10.75" cy="12" r="0.5" />
        
        <circle cx="2" cy="14" r="0.5" />
        <circle cx="4.5" cy="14" r="0.5" />
        <circle cx="7" cy="14" r="0.5" />
        <circle cx="9.5" cy="14" r="0.5" />
        <circle cx="12" cy="14" r="0.5" />
        
        <circle cx="3.25" cy="16" r="0.5" />
        <circle cx="5.75" cy="16" r="0.5" />
        <circle cx="8.25" cy="16" r="0.5" />
        <circle cx="10.75" cy="16" r="0.5" />
      </g>
    </g>
  </svg>
);

/**
 * 根据语言代码获取对应的国旗组件
 */
export const getFlagComponent = (languageCode: string): React.ComponentType<FlagIconProps> => {
  switch (languageCode) {
    case 'zh-CN':
      return ChinaFlag;
    case 'ja-JP':
      return JapanFlag;
    case 'en-US':
      return USAFlag;
    default:
      return ChinaFlag;
  }
};
