import React, { useEffect, useRef } from 'react';

interface WaveAnimationProps {
  width?: number;
  height?: number;
  lineCount?: number;
}

interface WaveConfig {
  amplitude: number;
  frequency: number;
  speed: number;
  color: string;
  strokeWidth: number;
  offset: number;
  angle: number;
  groupIndex: number;
  phaseOffset: number;
  verticalShift: number;
  twistFactor: number;
}

const WaveAnimation: React.FC<WaveAnimationProps> = ({
  width = 1000,
  height = 200,
  lineCount: _lineCount = 12
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const animationRef = useRef<number>();

  // 生成优雅有序的交织波浪线配置
  const generateWaveGroups = (): WaveConfig[] => {
    const waveConfigs: WaveConfig[] = [];
    const linesPerGroup = 8; // 每组8条线，增大间距

    // 两组规整有序的配置 - 间距更大，更规整
    const groupSettings = [
      {
        baseAmplitude: 12,
        baseFrequency: 0.004,
        baseSpeed: 0.001,
        baseAngle: 20,
        phaseOffset: 0,
        verticalOffset: height * 0.25, // 上移，增大组间距离
        color: 'rgba(0, 167, 111, 0.15)' // 主层
      },
      {
        baseAmplitude: 10,
        baseFrequency: 0.004,
        baseSpeed: 0.001, // 统一速度，保持一致
        baseAngle: -20,
        phaseOffset: Math.PI, // 180度相位差
        verticalOffset: height * 0.65, // 下移，增大组间距离
        color: 'rgba(0, 199, 135, 0.12)' // 交织层
      }
    ];

    groupSettings.forEach((group, groupIndex) => {
      for (let i = 0; i < linesPerGroup; i++) {
        const progress = i / (linesPerGroup - 1); // 0 到 1

        // 规整的扭曲因子，有序而不杂乱
        const twistFactor = 0.3 + groupIndex * 0.1;

        // 规整的垂直分布：有序的层次感
        const verticalShift = Math.sin(i * Math.PI / (linesPerGroup - 1)) * height * 0.08;

        waveConfigs.push({
          amplitude: group.baseAmplitude * (0.8 + progress * 0.4), // 适度的振幅变化
          frequency: group.baseFrequency, // 统一频率，不递增
          speed: group.baseSpeed, // 统一速度，完全一致
          color: group.color,
          strokeWidth: 0.4 + progress * 0.3, // 0.4-0.7 更细的线宽
          offset: group.verticalOffset + i * (height * 0.03), // 增大线条间距
          angle: group.baseAngle + i * 4 - (linesPerGroup - 1) * 2, // 增大角度间距
          groupIndex: groupIndex,
          phaseOffset: group.phaseOffset + i * 0.3, // 增大相位间距
          verticalShift: verticalShift,
          twistFactor: twistFactor
        });
      }
    });

    return waveConfigs;
  };

  const waves = generateWaveGroups();

  // 生成科幻交织的倒S形波浪线条路径
  const generateTwistedSWavePath = (
    waveWidth: number,
    amplitude: number,
    frequency: number,
    phase: number,
    offset: number,
    angle: number = 0,
    phaseOffset: number = 0,
    twistFactor: number = 0
  ): string => {
    const points: string[] = [];
    const step = waveWidth / 250; // 250个点，确保超级平滑

    for (let x = 0; x <= waveWidth; x += step) {
      const progress = x / waveWidth; // 0 到 1 的进度

      // 主S波形：使用tanh函数创建大弧度S形
      const sWave = Math.tanh((progress - 0.5) * 6) * 1.2; // 更大弧度的S形

      // 简化而优雅的波浪效果组合
      const primaryWave = Math.sin(x * frequency + phase + phaseOffset);
      const secondaryWave = Math.sin(x * frequency * 1.5 + phase * 0.8 + phaseOffset) * 0.3;

      // 规整的扭曲效果，有序而立体
      const twistEffect = Math.sin(progress * Math.PI * 2 + phase * 0.3) * twistFactor;

      // 规整的流动变化
      const flowVariation = Math.sin(progress * Math.PI + phase * 0.4) * 0.15;

      // 规整的角度变化，保持三维感但不杂乱
      const dynamicAngle = angle + Math.sin(progress * Math.PI * 2 + phase * 0.1) * 12;
      const angleRad = (dynamicAngle * Math.PI) / 180;

      // 组合效果
      const combinedWave = primaryWave + secondaryWave + flowVariation;
      const rotatedAmplitude = amplitude * (Math.cos(angleRad) + twistEffect * 0.5);

      // 垂直位置计算：S形 + 波浪 + 扭曲
      const y = (sWave * amplitude + combinedWave * rotatedAmplitude) + offset;

      points.push(`${x},${y}`);
    }

    return `M ${points.join(' L ')}`;
  };

  useEffect(() => {
    if (!svgRef.current) return;

    const svg = svgRef.current;
    let startTime = Date.now();

    // 创建路径元素
    const paths = waves.map((wave, _index) => {
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('fill', 'none');
      path.setAttribute('stroke', wave.color);
      path.setAttribute('stroke-width', wave.strokeWidth.toString());
      path.setAttribute('stroke-linecap', 'round');
      path.setAttribute('stroke-linejoin', 'round');
      svg.appendChild(path);
      return path;
    });

    const animate = () => {
      const elapsed = Date.now() - startTime;

      paths.forEach((path, index) => {
        const wave = waves[index];
        const phase = elapsed * wave.speed;

        const pathData = generateTwistedSWavePath(
          width,
          wave.amplitude,
          wave.frequency,
          phase,
          wave.offset,
          wave.angle,
          wave.phaseOffset,
          wave.twistFactor
        );

        path.setAttribute('d', pathData);
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      // 清理路径元素
      paths.forEach(path => {
        if (svg.contains(path)) {
          svg.removeChild(path);
        }
      });
    };
  }, [width, height]);

  return (
    <svg
      ref={svgRef}
      width="100%"
      height="100%"
      viewBox={`0 0 ${width} ${height}`}
      preserveAspectRatio="none"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: 'none'
      }}
    />
  );
};

export default WaveAnimation;
