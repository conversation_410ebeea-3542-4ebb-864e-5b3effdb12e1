/**
 * 通用输入抽屉组件
 * 支持文本输入、电话输入，集成移动端输入法适配
 */

import React, { useState, useRef, useEffect } from 'react';
import { useKeyboardAdjustment } from '../hooks/useKeyboardAdjustment';

interface InputDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  value: string;
  placeholder: string;
  onSave: (value: string) => void;
  inputType?: 'text' | 'tel';
  maxLength?: number;
  validator?: (value: string) => { valid: boolean; error?: string };
}

const InputDrawer: React.FC<InputDrawerProps> = ({
  isOpen,
  onClose,
  title,
  value,
  placeholder,
  onSave,
  inputType = 'text',
  maxLength = 50,
  validator
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  
  // 输入法适配
  const { adjustedStyle, isKeyboardOpen } = useKeyboardAdjustment(isOpen);

  // 当抽屉打开时，重置输入值并聚焦
  useEffect(() => {
    if (isOpen) {
      setInputValue(value);
      setError(null);
      // 延迟聚焦，确保抽屉动画完成
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [isOpen, value]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setError(null);
  };

  // 处理保存
  const handleSave = async () => {
    const trimmedValue = inputValue.trim();
    
    // 验证输入
    if (validator) {
      const validation = validator(trimmedValue);
      if (!validation.valid) {
        setError(validation.error || '输入无效');
        return;
      }
    }

    setIsSaving(true);
    setError(null);

    try {
      await onSave(trimmedValue);
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '保存失败';
      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="input-drawer-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`} 
        onClick={onClose}
      />
      
      {/* 抽屉容器 - 应用输入法适配 */}
      <div 
        className="absolute bottom-0 left-0 right-0 flex justify-center"
        style={adjustedStyle}
      >
        {/* 抽屉内容 */}
        <div 
          className={`
            w-full max-w-md bg-white rounded-t-3xl shadow-2xl transform transition-transform duration-300 ease-out
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          `}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 拖拽指示器 */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* 标题 */}
          <div className="px-6 py-4 border-b border-gray-100">
            <h2 id="input-drawer-title" className="text-lg font-semibold text-gray-800 text-center">
              {title}
            </h2>
          </div>

          {/* 输入区域 */}
          <div className="px-6 py-6">
            {/* 输入框 */}
            <div className="mb-4">
              <input
                ref={inputRef}
                type={inputType}
                value={inputValue}
                onChange={handleInputChange}
                placeholder={placeholder}
                maxLength={maxLength}
                className={`
                  w-full px-4 py-3 border rounded-xl transition-colors duration-200 mobile-input-optimized
                  ${error
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                    : 'border-gray-300 focus:border-[#00A76F] focus:ring-[#00A76F]/20'
                  }
                  focus:outline-none focus:ring-2 bg-white
                `}
                disabled={isSaving}
              />
              
              {/* 字符计数 */}
              <div className="flex justify-between items-center mt-2">
                <div className="text-xs text-gray-500">
                  {maxLength && `${inputValue.length}/${maxLength}`}
                </div>
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* 输入法提示 */}
            {isKeyboardOpen && (
              <div className="mb-4 p-2 bg-blue-50 border border-blue-200 rounded-lg keyboard-hint">
                <p className="text-xs text-blue-600 text-center">
                  📱 输入法已适配，确保输入框可见
                </p>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                disabled={isSaving}
                className={`
                  flex-1 py-3 px-4 rounded-xl border border-gray-300 text-gray-700 font-medium
                  transition-colors duration-200
                  ${isSaving 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white hover:bg-gray-50 active:bg-gray-100'
                  }
                `}
              >
                取消
              </button>
              
              <button
                onClick={handleSave}
                disabled={isSaving || !inputValue.trim()}
                className={`
                  flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200
                  ${isSaving || !inputValue.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#00A76F] text-white hover:bg-[#008f5f] active:bg-[#007a52] shadow-sm'
                  }
                `}
              >
                {isSaving ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>保存中...</span>
                  </div>
                ) : (
                  '保存'
                )}
              </button>
            </div>
          </div>

          {/* 底部安全区域 */}
          <div className="h-6 safe-area-inset-bottom"></div>
        </div>
      </div>
    </div>
  );
};

export default InputDrawer;
