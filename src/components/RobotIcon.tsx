/**
 * 机器人头像SVG组件
 * 用于显示不同颜色的机器人图标
 */

import React from 'react';

export interface RobotIconProps {
  color: string;
  size?: number;
}

/**
 * 机器人头像SVG组件
 */
export const RobotIcon: React.FC<RobotIconProps> = ({ color, size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    {/* 机器人头部 */}
    <rect x="6" y="6" width="12" height="10" rx="2" fill={color} />
    
    {/* 机器人眼睛 */}
    <circle cx="9" cy="9" r="1" fill="white" />
    <circle cx="15" cy="9" r="1" fill="white" />
    
    {/* 机器人嘴巴 */}
    <rect x="10" y="12" width="4" height="1" rx="0.5" fill="white" />
    
    {/* 机器人天线 */}
    <line x1="12" y1="6" x2="12" y2="4" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <circle cx="12" cy="3" r="1" fill={color} />
    
    {/* 机器人身体 */}
    <rect x="8" y="16" width="8" height="6" rx="1" fill={color} opacity="0.8" />
    
    {/* 机器人手臂 */}
    <rect x="5" y="17" width="2" height="4" rx="1" fill={color} opacity="0.6" />
    <rect x="17" y="17" width="2" height="4" rx="1" fill={color} opacity="0.6" />
  </svg>
);

export default RobotIcon;
