import React, { useState } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';

interface CarData {
  // 基本信息
  id: number;
  accessory_remark: string;
  annex_key: number;
  baseinfo_remark: string;
  body_color: string;
  body_type: string;
  brand: string;
  brand_series: string;
  business_insurance_date: string | null;
  certificate_change_record: string | null;
  code: string;
  current_city: string;
  current_province: string;
  display_mileage: number;
  engine_code: string;
  engine_remark: string | null;
  first_photo_url: string;
  interior_color: string;
  is_accident: string | null;
  is_business_insurance: boolean;
  is_compulsory_insurance: boolean;
  is_flamed: string | null;
  is_flooded: string | null;
  is_vehicle_card: boolean;
  level_id: string;
  license_code: string;
  machine_oil_remark: string | null;
  manufacturer: string;
  model_year: string;
  next_annual_examination: string;
  next_compulsory_insurance: string;
  oil_type: string;
  owner_current: string;
  password_bar: boolean;
  power: number;
  price: number;
  procedure_information_remark: string;
  produced_years: string;
  register_city: string;
  register_license_years: string;
  register_province: string;
  sale_time: string;
  seat_number: number;
  star_condition: number;
  star_electric: number;
  star_facade: number;
  star_interior: number;
  star_skeleton: number;
  swept_volume: string;
  swept_volume_standard: string;
  swept_volume_type: string;
  transfer_number: number;
  transfer_remark: string | null;
  transmission_remark: string | null;
  transmission_type: string;
  using_model: string;
  vehicle_name: string;
  seller_address: string;
  seller_phone: string;
  seller_store: string;
  sale_available: boolean;
  brand_id: string;
  brand_series_id: string;
  vehicle_model: string;
  selled_name: string;
  category: string;
  level: string;
  suggestion_price: string;
  market_year: string;
  market_month: string;
  produced_year: string;
  stop_year: string;
  producing_status: string;
  sale_status: string;
  country: string;
  produceing_type: string;

  // 发动机参数
  cylinder_volume: string;
  air_type: string;
  oil_number: string;
  max_horsepower: string;
  max_power: string;
  max_power_rpm: string;
  max_torque: string;
  max_torque_rpm: string;
  cylinder_arrange: string;
  quantity_cylinder: string;
  quantity_cylinder_door: string;
  compression_ratio: string;
  oil_feeding_type: string;
  standard_oil_consumption: string;
  downtown_oil_consumption: string;
  suburb_oil_consumption: string;
  acceleration_time: string;
  max_speed: string;

  // 变速箱
  transmission_desc: string;
  gear_number: string;

  // 底盘转向
  front_brake_type: string;
  end_brake_type: string;
  front_suspension: string;
  end_suspension: string;
  steering_type: string;
  steering_power_type: string;
  min_clearance: string;
  turning_radius: string;
  engine_position: string;
  drive_model: string;
  drive_type: string;

  // 车身尺寸
  length: string;
  width: string;
  height: string;
  wheel_base: string;
  front_wheel_span: string;
  end_wheel_span: string;
  weight: string;
  max_load: string;
  tank_capacity: string;
  trunk_capacity: string;
  door_quantity: string;
  seat_quantity: string;

  // 轮胎轮毂
  front_wheel: string;
  end_wheel: string;
  front_hub: string;
  end_hub: string;
  hub_material: string;
  spare_wheel: string;

  // 安全配置
  is_driver_airbag: string;
  is_codriver_airbag: string;
  is_front_side_airbag: string;
  is_end_side_airbag: string;
  is_front_head_airbag: string;
  is_end_head_airbag: string;
  is_knee_airbag: string;
  is_tire_pressure_monitor: string;
  is_zero_pressure_running: string;
  is_remind_safety_belt: string;
  is_child_seat_interface: string;
  is_latch_seat_interface: string;
  is_anti_theft_for_engine: string;
  is_central_lock: string;
  is_remote_key: string;
  is_keyless_start_system: string;
  is_abs: string;
  is_brake_force_distribution: string;
  is_bas: string;
  is_traction_control_system: string;
  is_stability_control: string;
  is_automatic_parking: string;
  is_hill_decent_control: string;
  is_variable_suspension: string;
  is_air_suspension: string;
  is_variable_steering_ratio: string;
  is_doubling_assit: string;
  is_active_brake: string;
  is_active_steering_system: string;

  // 操控配置
  is_leather_steering_wheel: string;
  is_vertical_adjust_steering_wheel: string;
  is_horizontal_adjust_steering_wheel: string;
  is_electric_adjust_steering_wheel: string;
  is_multi_function_steering_wheel: string;
  is_steering_wheel_shift: string;

  // 座椅配置
  is_leather_seat: string;
  is_sport_seat: string;
  is_adjust_seat_height: string;
  is_adjust_lumbar_support: string;
  is_adjust_shoulder_support: string;
  is_electric_adjust_driver_seat: string;
  is_electric_adjust_codriver_seat: string;
  is_adjust_second_backrest_angle: string;
  is_adjust_second_seat_move: string;
  is_electric_adjust_end_seat: string;
  is_electric_memory_seat: string;
  is_heat_front_seat: string;
  is_heat_end_seat: string;
  is_ventilation_seat: string;
  is_massage_seat: string;
  is_all_down_end_seat: string;
  is_ratio_down_end_seat: string;
  is_third_seat: string;
  is_front_central_armrest: string;
  is_end_central_armrest: string;
  is_end_cup_holder: string;
  is_interior_atmosphere_light: string;
  is_end_windshield_sunshade: string;
  is_end_side_windows_sunshade: string;
  is_sun_visor_mirror: string;
  is_electric_trunk: string;

  // 外观配置
  is_sport_appearance: string;
  is_electric_dool: string;
  is_electric_skylight: string;
  is_panorama_skylight: string;
  is_hid: string;
  is_led: string;
  is_daytime_driving_light: string;
  is_auto_headlight: string;
  is_front_turning_light: string;
  is_front_fog_light: string;
  is_adjust_headlight_height: string;
  is_clear_headlight: string;
  is_electric_front_window: string;
  is_electric_end_window: string;
  is_prevent_clamp_hand_window: string;
  is_heat_glass: string;
  is_electric_adjust_rearview_mirror: string;
  is_heat_rearview_mirror: string;
  is_prevent_dazzle_rearview_mirror: string;
  is_auto_fold_rearview_mirror: string;
  is_memory_rearview_mirror: string;
  is_rear_wiper: string;
  is_induction_rear_wiper: string;

  // 辅助配置
  is_ccs: string;
  is_parking_assist: string;
  is_reverse_video: string;
  is_ecu_display: string;
  is_hud_display: string;
  is_gps: string;
  is_location_interaction_service: string;
  is_control_large_screen: string;
  is_human_computer: string;
  is_built_in_hard_disk: string;
  is_bluetooth_car_phone: string;
  is_car_tv: string;
  is_end_lcd: string;
  is_aux: string;
  is_mp3: string;
  is_simple_cd: string;
  is_multi_cd: string;
  is_virtual_multi_cd: string;
  is_simple_dvd: string;
  is_multi_dvd: string;
  speaker_number: string;

  // 空调配置
  is_air_conditioning: string;
  is_auto_air_conditioning: string;
  is_individual_air_conditioning: string;
  is_end_seat_air_outlet: string;
  is_temp_zone_control: string;
  is_air_filtration: string;
  is_car_fridge: string;

  // 高科技配置
  is_pav: string;
  is_nvs: string;
  is_console_screen_display: string;
  is_acc: string;
  is_panorama_camera: string;
  is_reversing_radar: string;

  // 其他信息
  biggest_mileage: string;
  is_foreign: string;
  is_car_telematics: string;
  feature_items: string;
  uv_sid: string;
  qczj_id: string;

  // 新能源相关
  charging_time: string;
  motor_yype: string;
  total_power: string;
  total_torque: string;
  front_max_power: string;
  front_max_torque: string;
  rear_max_power: string;
  rear_max_torque: string;
  integrated_power: string;
  integrated_torque: string;
  motor_num: string;
  battery_type: string;
  battery_energy: string;
  power_consumption_per: string;
  battery_warranty: string;
  fast_charge: string;
  qqddj: string;
  hqddj: string;
  qqddjsl: string;
  hqddjsl: string;
  nedc: string;
  wltp: string;
  cltc: string;
  epa: string;
  battery_prehot: string;
  battery_factory: string;
  fast_charge_time: string;
  slow_charge_time: string;
  dcnlmd: string;
  dxpp: string;
  dclqfs: string;
  dcxs: string;
  dwfd: string;

  // 时间戳
  create_time: string | null;
  modify_time: string;
  sync_time: string;
  vehicle_id: number;

  // 检测信息
  category_name: string;
  damage_color: string;
  damage_name: string | null;
  item_name: string;
  level_name: string | null;
  url: string | null;
  pic_name: string;
  pic_type: number;
  pic_url: string;
}

interface CarComparisonProps {
  cars: CarData[];
  onClose: () => void;
  onCarDetailClick?: (car: CarData) => void;
}

const CarComparison: React.FC<CarComparisonProps> = ({ cars, onClose, onCarDetailClick }) => {
  // 灯箱状态管理
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  if (!cars || cars.length !== 2) {
    return null;
  }

  const [car1, car2] = cars;

  // 准备灯箱图片数据
  const lightboxSlides = [
    ...(car1.pic_url ? [{
      src: car1.pic_url,
      alt: `${car1.vehicle_name} - 车辆图片`,
      title: `${car1.brand} ${car1.brand_series} ${car1.model_year}款`
    }] : []),
    ...(car2.pic_url ? [{
      src: car2.pic_url,
      alt: `${car2.vehicle_name} - 车辆图片`,
      title: `${car2.brand} ${car2.brand_series} ${car2.model_year}款`
    }] : [])
  ];

  // 打开灯箱
  const openLightbox = (carIndex: number) => {
    if (lightboxSlides.length > 0) {
      setLightboxIndex(carIndex);
      setLightboxOpen(true);
    }
  };

  // 渲染星星评级



  const formatMileage = (mileage: number) => {
    return `${(mileage / 10000).toFixed(2)}万公里`;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const totalStars = rating; // 直接使用评分值，满级为5
    const fullStars = Math.floor(totalStars);
    const hasHalfStar = (totalStars % 1) >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        // 完整的星星
        stars.push(
          <span key={i} style={{ color: '#01A66E' }} className="text-lg">★</span>
        );
      } else if (i === fullStars && hasHalfStar) {
        // 半颗星星 - 使用特殊的半星符号
        stars.push(
          <span key={i} style={{ color: '#01A66E' }} className="text-lg relative">
            <span className="absolute">☆</span>
            <span className="absolute overflow-hidden w-1/2">★</span>
          </span>
        );
      } else {
        // 空星星
        stars.push(
          <span key={i} className="text-gray-300 text-lg">☆</span>
        );
      }
    }

    // 只返回星星，不显示数值
    return (
      <div className="flex items-center justify-center">
        <div className="flex">{stars}</div>
      </div>
    );
  };

  // 基本信息对比 - 只显示指定字段
  const basicComparisonItems: Array<{
    label: string;
    key: string;
    formatter?: (value: any) => string;
    isStarRating?: boolean;
  }> = [
    { label: '在售价格', key: 'price', formatter: (value: number) => value ? `${(value / 10000).toFixed(1)}万元` : '-' },
    { label: '首次上牌', key: 'register_license_years', formatter: (value: string) => value ? `${value.slice(0,4)}年${value.slice(4,6)}月` : '-' },
    { label: '表显里程', key: 'display_mileage', formatter: formatMileage },
    { label: '使用性质', key: 'using_model' },
    { label: '车辆所在地', key: 'current_city' },
    { label: '变速箱', key: 'transmission_type' },
    { label: '排气量', key: 'swept_volume', formatter: (value: string) => value ? `${value}L` : '-' },
    { label: '燃油类型', key: 'oil_type' },
    { label: '车辆颜色', key: 'body_color' },
    { label: '出厂日期', key: 'produced_years', formatter: (value: string) => value ? `${value.slice(0,4)}年${value.slice(4,6)}月` : '-' },
    { label: '发动机号', key: 'engine_code' },
    { label: '环保标准', key: 'swept_volume_standard' },
    { label: '最大功率', key: 'max_power', formatter: (value: string) => value ? `${value}kW` : '-' },
    { label: '内饰颜色', key: 'interior_color' },
    { label: '座位数', key: 'seat_number', formatter: (value: number) => value ? `${value}座` : '-' },
    { label: '骨架', key: 'star_skeleton', isStarRating: true },
    { label: '外观', key: 'star_facade', isStarRating: true },
    { label: '内饰', key: 'star_interior', isStarRating: true },
    { label: '工况', key: 'star_condition', isStarRating: true },
  ];







  return (
    <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="flex items-center px-4 py-3">
          <button
            onClick={onClose}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800 truncate">
            车辆对比详情
          </h1>
        </div>
      </div>

      {/* 车辆图片和基本信息 */}
      <div className="px-4 py-6">
        <div className="grid grid-cols-2 gap-4 mb-6">
          {/* 车辆1 */}
          <div className="text-center">
            <div
              className="w-full h-32 rounded-lg mb-3 bg-gray-100 flex items-center justify-center relative cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => car1.pic_url && openLightbox(0)}
            >
              {car1.pic_url ? (
                <img
                  src={car1.pic_url}
                  alt={car1.vehicle_name}
                  className="w-full h-32 object-cover rounded-lg"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                />
              ) : null}
              <div className="text-gray-500 text-sm" style={{ display: car1.pic_url ? 'none' : 'flex' }}>
                图片不存在
              </div>
              {/* 放大镜图标 */}
              {car1.pic_url && (
                <div className="absolute top-2 right-2 bg-black/50 rounded-full p-1">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              )}
            </div>
            <h3 className="text-sm font-medium text-gray-900">
              {car1.vehicle_name || `${car1.brand} ${car1.brand_series} ${car1.model_year}款`}
            </h3>
          </div>

          {/* 车辆2 */}
          <div className="text-center">
            <div
              className="w-full h-32 rounded-lg mb-3 bg-gray-100 flex items-center justify-center relative cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => car2.pic_url && openLightbox(1)}
            >
              {car2.pic_url ? (
                <img
                  src={car2.pic_url}
                  alt={car2.vehicle_name}
                  className="w-full h-32 object-cover rounded-lg"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                />
              ) : null}
              <div className="text-gray-500 text-sm" style={{ display: car2.pic_url ? 'none' : 'flex' }}>
                图片不存在
              </div>
              {/* 放大镜图标 */}
              {car2.pic_url && (
                <div className="absolute top-2 right-2 bg-black/50 rounded-full p-1">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              )}
            </div>
            <h3 className="text-sm font-medium text-gray-900">
              {car2.vehicle_name || `${car2.brand} ${car2.brand_series} ${car2.model_year}款`}
            </h3>
          </div>
        </div>

        {/* 基本信息对比 */}
        <div className="mb-6">
          <div className="space-y-0">
            {basicComparisonItems.map((item, index) => (
              <div key={item.key} className={`grid grid-cols-3 py-3 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
                <div className="text-sm text-gray-800 px-4 flex items-center justify-center font-medium">
                  {item.isStarRating ?
                    renderStars((car1 as any)[item.key] || 0) :
                    (item.formatter ? item.formatter((car1 as any)[item.key]) : (car1 as any)[item.key] || '-')
                  }
                </div>
                <div className="text-xs text-gray-500 px-2 flex items-center justify-center">
                  {item.label}
                </div>
                <div className="text-sm text-gray-800 px-4 flex items-center justify-center font-medium">
                  {item.isStarRating ?
                    renderStars((car2 as any)[item.key] || 0) :
                    (item.formatter ? item.formatter((car2 as any)[item.key]) : (car2 as any)[item.key] || '-')
                  }
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部留白，为固定按钮预留空间 */}
        <div className="h-20"></div>
      </div>

      {/* 固定在底部的查看更多详情按钮 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 safe-area-inset-bottom">
        <div className="grid grid-cols-2 gap-4">
          <button
            onClick={() => onCarDetailClick && onCarDetailClick(car1)}
            className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg text-sm font-medium transition-all duration-200"
          >
            查看车辆详情
          </button>
          <button
            onClick={() => onCarDetailClick && onCarDetailClick(car2)}
            className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg text-sm font-medium transition-all duration-200"
          >
            查看车辆详情
          </button>
        </div>
      </div>

      {/* 图片灯箱 */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={lightboxSlides}
        index={lightboxIndex}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
        carousel={{
          finite: lightboxSlides.length <= 1,
        }}
        render={{
          buttonPrev: lightboxSlides.length <= 1 ? () => null : undefined,
          buttonNext: lightboxSlides.length <= 1 ? () => null : undefined,
        }}
        styles={{
          container: { backgroundColor: "rgba(0, 0, 0, .9)" },
        }}
      />
    </div>
  );
};

export default CarComparison;
