/**
 * 确认模态框组件
 * 用于显示确认对话框，支持自定义标题、内容和按钮
 */

import React from 'react';

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonColor?: string;
  isLoading?: boolean;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  content,
  confirmText = '确认',
  cancelText = '取消',
  confirmButtonColor = '#00A76F',
  isLoading = false
}) => {
  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isLoading) {
      onClose();
    } else if (e.key === 'Enter' && !isLoading) {
      e.preventDefault();
      onConfirm();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="confirm-modal-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`} 
        onClick={!isLoading ? onClose : undefined}
      />
      
      {/* 模态框容器 */}
      <div className="absolute inset-0 flex items-center justify-center p-4">
        {/* 模态框内容 */}
        <div 
          className={`
            w-full max-w-sm bg-white rounded-2xl shadow-2xl transform transition-all duration-300 ease-out
            ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
          `}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 标题 */}
          <div className="px-6 py-5 border-b border-gray-100">
            <h2 id="confirm-modal-title" className="text-lg font-semibold text-gray-800 text-center">
              {title}
            </h2>
          </div>

          {/* 内容 */}
          <div className="px-6 py-6">
            <p className="text-gray-600 text-center leading-relaxed">
              {content}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="px-6 py-4 border-t border-gray-100">
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                disabled={isLoading}
                className={`
                  flex-1 py-3 px-4 rounded-xl border border-gray-300 text-gray-700 font-medium
                  transition-colors duration-200
                  ${isLoading 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white hover:bg-gray-50 active:bg-gray-100'
                  }
                `}
              >
                {cancelText}
              </button>
              
              <button
                onClick={onConfirm}
                disabled={isLoading}
                className={`
                  flex-1 py-3 px-4 rounded-xl font-medium text-white
                  transition-all duration-200 relative overflow-hidden
                  ${isLoading 
                    ? 'cursor-not-allowed opacity-70' 
                    : 'hover:opacity-90 active:scale-95'
                  }
                `}
                style={{ 
                  backgroundColor: confirmButtonColor,
                  boxShadow: isLoading ? 'none' : '0 2px 8px rgba(0, 167, 111, 0.3)'
                }}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    处理中...
                  </div>
                ) : (
                  confirmText
                )}
              </button>
            </div>
          </div>

          {/* 底部安全区域 */}
          <div className="h-2"></div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmModal;
