/**
 * React错误边界组件
 * 捕获子组件中的JavaScript错误，防止整个应用白屏
 */

import { Component, ErrorInfo, ReactNode } from 'react';
import { isIOS } from '../utils/deviceDetection';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary捕获到错误:', error);
    console.error('错误详情:', errorInfo);

    // 更新state以包含错误信息
    this.setState({
      error,
      errorInfo
    });

    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo);

    // iOS特定的错误处理
    if (isIOS()) {
      console.warn('iOS设备检测到渲染错误，可能是内存不足或复杂DOM操作导致');
      
      // 尝试清理一些缓存来释放内存
      try {
        // 清理图片缓存
        const imageKeys = Object.keys(localStorage).filter(key => key.startsWith('img_cache_'));
        if (imageKeys.length > 50) {
          imageKeys.slice(0, 20).forEach(key => localStorage.removeItem(key));
          console.log('已清理部分图片缓存以释放内存');
        }
      } catch (cleanupError) {
        console.warn('清理缓存时出错:', cleanupError);
      }
    }
  }

  handleRetry = () => {
    // 重置错误状态
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg 
                  className="w-8 h-8 text-red-600" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
              </div>
              <h1 className="text-xl font-bold text-gray-900 mb-2">
                页面出现错误
              </h1>
              <p className="text-gray-600 mb-4">
                {isIOS() 
                  ? '检测到您使用的是iOS设备，可能是内存不足导致的渲染问题。' 
                  : '页面渲染时出现了意外错误。'
                }
              </p>
              
              {/* 开发环境下显示错误详情 */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-left bg-gray-100 p-3 rounded-lg mb-4 text-xs">
                  <summary className="cursor-pointer font-medium text-gray-700 mb-2">
                    错误详情 (开发模式)
                  </summary>
                  <div className="space-y-2">
                    <div>
                      <strong>错误:</strong> {this.state.error.message}
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>组件栈:</strong>
                        <pre className="whitespace-pre-wrap text-xs mt-1">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>

            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                重新加载
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors"
              >
                刷新页面
              </button>
            </div>

            {isIOS() && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">
                  <strong>iOS用户提示:</strong> 如果问题持续出现，请尝试关闭其他Safari标签页或重启Safari浏览器。
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
