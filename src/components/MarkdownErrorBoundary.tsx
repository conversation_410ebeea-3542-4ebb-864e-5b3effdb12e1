/**
 * Markdown渲染专用错误边界
 * 专门处理MarkdownRenderer可能出现的渲染错误
 */

import { Component, ErrorInfo, ReactNode } from 'react';
import { isIOS } from '../utils/deviceDetection';

interface Props {
  children: ReactNode;
  content: string; // 原始markdown内容，用于降级显示
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class MarkdownErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('MarkdownRenderer渲染错误:', error);
    console.error('错误详情:', errorInfo);

    // iOS特定处理
    if (isIOS()) {
      console.warn('iOS设备Markdown渲染失败，可能是内容过于复杂或内存不足');
    }
  }

  render() {
    if (this.state.hasError) {
      // 降级到纯文本显示，移除错误提示框
      return (
        <div className="text-sm leading-relaxed whitespace-pre-wrap break-words">
          {this.props.content}
        </div>
      );
    }

    return this.props.children;
  }
}

export default MarkdownErrorBoundary;
