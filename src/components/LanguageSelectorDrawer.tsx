/**
 * 语言选择抽屉组件
 * 支持选择中文、日文、英文三种语言
 */

import React, { useState, useEffect } from 'react';
import { supportedLanguages, Language } from '../data/languages';

interface LanguageSelectorDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onLanguageSelect: (language: Language) => void;
  currentLanguageCode?: string;
}

const LanguageSelectorDrawer: React.FC<LanguageSelectorDrawerProps> = ({
  isOpen,
  onClose,
  onLanguageSelect,
  currentLanguageCode = 'zh-CN'
}) => {
  const [selectedLanguage, setSelectedLanguage] = useState<Language | null>(null);

  // 初始化选中状态
  useEffect(() => {
    if (isOpen) {
      const currentLanguage = supportedLanguages.find(lang => lang.code === currentLanguageCode) || supportedLanguages[0];
      setSelectedLanguage(currentLanguage);
    }
  }, [isOpen, currentLanguageCode]);

  // 处理语言选择
  const handleLanguageSelect = (language: Language) => {
    setSelectedLanguage(language);
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (selectedLanguage) {
      onLanguageSelect(selectedLanguage);
      onClose();
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="language-selector-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`} 
        onClick={onClose}
      />
      
      {/* 抽屉容器 */}
      <div className="absolute bottom-0 left-0 right-0 flex justify-center">
        {/* 抽屉内容 */}
        <div 
          className={`
            w-full max-w-md bg-white rounded-t-3xl shadow-2xl transform transition-transform duration-300 ease-out flex flex-col
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          `}
          style={{ maxHeight: '60vh' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 拖拽指示器 */}
          <div className="flex justify-center pt-3 pb-2 flex-shrink-0">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* 标题 */}
          <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
            <h2 id="language-selector-title" className="text-lg font-semibold text-gray-800 text-center">
              语言修改
            </h2>
          </div>

          {/* 语言列表 */}
          <div className="px-6 py-4 flex-1 min-h-0 overflow-y-auto">
            <div className="space-y-3">
              {supportedLanguages.map((language) => {
                // 除了中文以外的语言都设为禁用状态
                const isDisabled = language.code !== 'zh-CN';

                return (
                  <button
                    key={language.code}
                    onClick={() => !isDisabled && handleLanguageSelect(language)}
                    disabled={isDisabled}
                    className={`
                      w-full p-4 rounded-xl border transition-all duration-200 text-center
                      ${isDisabled
                        ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed opacity-60'
                        : selectedLanguage?.code === language.code
                          ? 'border-[#00A76F] bg-[#00A76F]/5 text-[#00A76F]'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                      }
                    `}
                  >
                    <span className="text-base font-medium">{language.name}</span>
                    {isDisabled && (
                      <div className="text-xs text-gray-400 mt-1">暂不支持</div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* 确认按钮 */}
          <div className="px-6 py-4 border-t border-gray-100 flex-shrink-0">
            <button
              onClick={handleConfirm}
              disabled={!selectedLanguage}
              className={`
                w-full py-3 px-4 rounded-xl font-medium transition-all duration-200
                ${selectedLanguage
                  ? 'bg-[#00A76F] text-white hover:bg-[#008f5f] active:bg-[#007a52] shadow-sm'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
            >
              确认选择
              {selectedLanguage && (
                <span className="ml-2 text-sm opacity-90">
                  {selectedLanguage.name}
                </span>
              )}
            </button>
          </div>

          {/* 底部安全区域 */}
          <div className="h-6 safe-area-inset-bottom flex-shrink-0"></div>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelectorDrawer;
