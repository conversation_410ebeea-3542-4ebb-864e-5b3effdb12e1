/**
 * 机器人头像选择抽屉组件
 * 支持选择不同的智能精灵形象
 */

import React, { useState, useEffect } from 'react';
import { robotAvatars, RobotAvatar } from '../data/robotAvatars';
import SpineRobot from './SpineRobot';

interface RobotSelectorDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onRobotSelect: (robot: RobotAvatar) => void;
  currentRobotId?: string;
}

const RobotSelectorDrawer: React.FC<RobotSelectorDrawerProps> = ({
  isOpen,
  onClose,
  onRobotSelect,
  currentRobotId = 'blue'
}) => {
  const [selectedRobot, setSelectedRobot] = useState<RobotAvatar | null>(null);

  // 初始化选中状态
  useEffect(() => {
    if (isOpen) {
      const currentRobot = robotAvatars.find(robot => robot.id === currentRobotId) || robotAvatars[0];
      setSelectedRobot(currentRobot);
    }
  }, [isOpen, currentRobotId]);

  // 处理机器人选择
  const handleRobotSelect = (robot: RobotAvatar) => {
    setSelectedRobot(robot);
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (selectedRobot) {
      onRobotSelect(selectedRobot);
      onClose();
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="robot-selector-title"
      tabIndex={-1}
    >
      {/* 背景遮罩 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
        }`} 
        onClick={onClose}
      />
      
      {/* 抽屉容器 */}
      <div className="absolute bottom-0 left-0 right-0 flex justify-center">
        {/* 抽屉内容 */}
        <div 
          className={`
            w-full max-w-md bg-white rounded-t-3xl shadow-2xl transform transition-transform duration-300 ease-out flex flex-col
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
          `}
          style={{ maxHeight: '70vh' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 拖拽指示器 */}
          <div className="flex justify-center pt-3 pb-2 flex-shrink-0">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* 标题 */}
          <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
            <h2 id="robot-selector-title" className="text-lg font-semibold text-gray-800 text-center">
              形象修改
            </h2>
          </div>

          {/* 机器人选择列表 */}
          <div className="px-6 py-4 flex-1 min-h-0 overflow-y-auto">
            <div className="space-y-3">
              {robotAvatars.map((robot) => {
                // 机器人B（green）设为禁用状态
                const isDisabled = robot.id === 'green';
                // 根据机器人ID确定动画类型
                const animationType = robot.id === 'blue' ? 'think' : 'listen';

                return (
                  <button
                    key={robot.id}
                    onClick={() => !isDisabled && handleRobotSelect(robot)}
                    disabled={isDisabled}
                    className={`
                      w-full p-4 rounded-xl border transition-all duration-200 flex items-center justify-center
                      ${isDisabled
                        ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed opacity-60'
                        : selectedRobot?.id === robot.id
                          ? 'border-[#00A76F] bg-[#00A76F]/5 text-[#00A76F]'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                      }
                    `}
                  >
                    {/* 机器人Spine动画 - 左侧显示，添加圆角 */}
                    <div className="w-12 h-12 rounded-lg flex-shrink-0 mr-4 overflow-hidden bg-gray-50 flex items-center justify-center">
                      <div style={{
                        transform: 'scale(0.22) translateY(-45%)',
                        transformOrigin: 'center'
                      }}>
                        <SpineRobot
                          animation={animationType}
                          className=""
                          loop={true}
                          timeScale={1.0}
                          onError={(error) => {
                            console.error('Robot selector spine animation error:', error);
                          }}
                        />
                      </div>
                    </div>

                    {/* 文字内容 - 居中显示 */}
                    <div className="flex flex-col items-center justify-center">
                      <span className="text-base font-medium">{robot.name}</span>
                      {isDisabled && (
                        <div className="text-xs text-gray-400 mt-1">暂不可用</div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* 确认按钮 */}
          <div className="px-6 py-4 border-t border-gray-100 flex-shrink-0">
            <button
              onClick={handleConfirm}
              disabled={!selectedRobot}
              className={`
                w-full py-3 px-4 rounded-xl font-medium transition-all duration-200
                ${selectedRobot
                  ? 'bg-[#00A76F] text-white hover:bg-[#008f5f] active:bg-[#007a52] shadow-sm'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
            >
              确认选择
              {selectedRobot && (
                <span className="ml-2 text-sm opacity-90">
                  {selectedRobot.name}
                </span>
              )}
            </button>
          </div>

          {/* 底部安全区域 */}
          <div className="h-6 safe-area-inset-bottom flex-shrink-0"></div>
        </div>
      </div>
    </div>
  );
};

export default RobotSelectorDrawer;
