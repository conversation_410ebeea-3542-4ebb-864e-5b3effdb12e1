/**
 * Spine 动画机器人组件
 * 使用 @esotericsoftware/spine-player 渲染 Spine 动画
 * 重构版本：只支持 public/spine/jqr 目录下的动画
 * 支持动画：think, listen, listen2, talk
 */

import React, { useRef, useEffect, useState } from 'react';

// 导入 Spine Player 和样式
// @ts-ignore - Spine Player 可能没有完整的 TypeScript 定义
import { SpinePlayer } from '@esotericsoftware/spine-player';

// 缓存相关类型定义
interface CachedSpineResources {
  jsonData: string; // JSON 数据
  atlasData: string; // Atlas 数据
  imageData: { [key: string]: string }; // 图片数据 (base64)
  loadTime: number; // 加载时间戳
  isReady: boolean; // 是否准备就绪
}

interface SpineRobotProps {
  /** 容器的 CSS 类名 */
  className?: string;
  /** 动画名称：think, listen, listen2, talk */
  animation?: 'think' | 'listen' | 'listen2' | 'talk';
  /** 是否循环播放，默认为 true */
  loop?: boolean;
  /** 动画播放速度，默认为 1.0 */
  timeScale?: number;
  /** 错误回调函数 */
  onError?: (error: Error) => void;
  /** 加载完成回调函数 */
  onLoad?: () => void;
}

// 全局资源缓存 - 只缓存 jqr 动画资源
const spineResourceCache = new Map<string, CachedSpineResources>();

// 活跃的 SpinePlayer 实例管理
const activeSpinePlayers = new Map<string, any>();

// Spine 资源路径配置 - 固定使用 jqr 目录
const SPINE_CONFIG = {
  basePath: '/spine/jqr',
  jsonFileName: 'ROBOT.json',
  atlasFileName: 'ROBOT.txt'
};

// 缓存管理工具函数
const getCachedResources = (): CachedSpineResources | null => {
  const cached = spineResourceCache.get('jqr');
  if (cached && cached.isReady) {
    console.log('🎯 SpineRobot: 从缓存获取 jqr 资源');
    return cached;
  }
  return null;
};

const setCachedResources = (resources: CachedSpineResources): void => {
  spineResourceCache.set('jqr', resources);
  console.log('💾 SpineRobot: 缓存 jqr 资源');
};

const clearResourceCache = (): void => {
  console.log('🗑️ SpineRobot: 清理资源缓存');
  spineResourceCache.clear();

  // 清理活跃的 player 实例
  activeSpinePlayers.forEach((player, key) => {
    if (player && typeof player.dispose === 'function') {
      try {
        player.dispose();
      } catch (error) {
        console.warn(`清理活跃 player 失败 ${key}:`, error);
      }
    }
  });
  activeSpinePlayers.clear();
};

// 预加载 jqr 资源函数
const preloadSpineResources = async (): Promise<CachedSpineResources | null> => {
  const cached = getCachedResources();
  if (cached) {
    return cached;
  }

  try {
    const jsonUrl = `${SPINE_CONFIG.basePath}/${SPINE_CONFIG.jsonFileName}`;
    const atlasUrl = `${SPINE_CONFIG.basePath}/${SPINE_CONFIG.atlasFileName}`;

    console.log('📥 SpineRobot: 预加载 jqr 资源');

    // 预加载 JSON 和 Atlas 文件
    const [jsonResponse, atlasResponse] = await Promise.all([
      fetch(jsonUrl),
      fetch(atlasUrl)
    ]);

    if (!jsonResponse.ok || !atlasResponse.ok) {
      throw new Error('Failed to preload jqr resources');
    }

    const jsonData = await jsonResponse.text();
    const atlasData = await atlasResponse.text();

    // 解析 atlas 文件以获取图片文件名
    const imageData: { [key: string]: string } = {};
    const atlasLines = atlasData.split('\n');

    for (let i = 0; i < atlasLines.length; i++) {
      const line = atlasLines[i].trim();
      if (line.endsWith('.png')) {
        const imageName = line;
        const imageUrl = `${SPINE_CONFIG.basePath}/${imageName}`;

        try {
          const imageResponse = await fetch(imageUrl);
          if (imageResponse.ok) {
            const imageBlob = await imageResponse.blob();
            const imageBase64 = await new Promise<string>((resolve) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.readAsDataURL(imageBlob);
            });
            imageData[imageName] = imageBase64;
            console.log(`📷 SpineRobot: 缓存图片 ${imageName}`);
          }
        } catch (imageError) {
          console.warn(`加载图片失败 ${imageName}:`, imageError);
        }
      }
    }

    const resources: CachedSpineResources = {
      jsonData,
      atlasData,
      imageData,
      loadTime: Date.now(),
      isReady: true
    };

    setCachedResources(resources);
    return resources;
  } catch (error) {
    console.error('预加载 jqr 资源失败:', error);
    return null;
  }
};

/**
 * Spine 动画机器人组件
 */
const SpineRobot: React.FC<SpineRobotProps> = ({
  className = '',
  animation = 'think',
  loop = true,
  timeScale = 1.0,
  onError,
  onLoad
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const spinePlayerRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Spine 加载 useEffect
  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;

    const initSpinePlayer = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // 创建唯一的容器 ID
        const containerId = `spine-robot-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        container.id = containerId;

        // 安全地清空容器
        const safeClearContainer = (containerElement: HTMLElement) => {
          try {
            containerElement.innerHTML = '';
          } catch (error) {
            console.warn('清空容器失败:', error);
          }
        };

        safeClearContainer(container);

        // 尝试从缓存获取资源
        let cachedResources = getCachedResources();

        // 如果没有缓存，则预加载资源
        if (!cachedResources) {
          console.log('📥 SpineRobot: 首次加载，预加载 jqr 资源');
          cachedResources = await preloadSpineResources();
        } else {
          console.log('🚀 SpineRobot: 使用缓存的 jqr 资源');
        }

        // 如果资源加载失败，回退到传统方式
        if (!cachedResources) {
          console.log('⚠️ SpineRobot: 资源预加载失败，回退到传统加载方式');
          await createSpinePlayerTraditional(containerId);
          return;
        }

        // 使用缓存的资源创建 SpinePlayer
        await createSpinePlayerFromCache(containerId, cachedResources);

      } catch (error) {
        console.error('Failed to initialize Spine Player:', error);
        setHasError(true);
        setIsLoading(false);
        onError?.(error as Error);
      }
    };

    // 使用缓存资源创建 SpinePlayer
    const createSpinePlayerFromCache = async (containerId: string, resources: CachedSpineResources) => {
      try {
        console.log('🎯 SpineRobot: 使用缓存资源创建播放器');

        // 构建 rawDataURIs 对象，使用缓存的数据
        const rawDataURIs: { [key: string]: string } = {};

        // 添加 JSON 数据
        rawDataURIs[SPINE_CONFIG.jsonFileName] = `data:application/json;charset=utf-8,${encodeURIComponent(resources.jsonData)}`;

        // 添加 Atlas 数据
        rawDataURIs[SPINE_CONFIG.atlasFileName] = `data:text/plain;charset=utf-8,${encodeURIComponent(resources.atlasData)}`;

        // 添加图片数据
        Object.keys(resources.imageData).forEach(imageName => {
          rawDataURIs[imageName] = resources.imageData[imageName];
        });

        // 配置 Spine Player
        const spineConfig: any = {
          skeleton: SPINE_CONFIG.jsonFileName,
          atlas: SPINE_CONFIG.atlasFileName,
          rawDataURIs: rawDataURIs,
          animation: animation,
          backgroundColor: '#00000000', // 透明背景
          alpha: true,
          showControls: false,
          showLoading: false,
          preserveDrawingBuffer: false,
          premultipliedAlpha: false,
          viewport: {
            x: -1000,
            y: -800,
            width: 2000,
            height: 3200,
            padLeft: 200,
            padRight: 200,
            padTop: 300,
            padBottom: 200
          }
        };

        // 添加成功和错误回调
        spineConfig.success = (player: any) => {
          console.log('💫 SpineRobot: 缓存资源播放器创建成功');
          spinePlayerRef.current = player;

          // 将 player 添加到活跃实例管理
          activeSpinePlayers.set(containerId, player);

          // 设置播放速度和动画
          try {
            if (player.animationState) {
              player.animationState.timeScale = timeScale;

              // 验证动画是否存在
              const skeletonData = player.skeleton?.data;
              if (skeletonData) {
                const animationData = skeletonData.findAnimation(animation);
                if (!animationData) {
                  console.warn(`Animation "${animation}" not found, using fallback "think"`);
                  const fallbackAnimation = skeletonData.findAnimation('think');
                  if (fallbackAnimation) {
                    player.animationState.setAnimation(0, 'think', true);
                  }
                }
              }
            }
          } catch (validationError) {
            console.warn('Animation validation failed:', validationError);
          }

          setIsLoading(false);
          onLoad?.();
        };

        spineConfig.error = (_player: any, reason: string) => {
          console.error('Spine animation failed to load:', reason);
          const error = new Error(`Spine animation failed to load: ${reason}`);
          setHasError(true);
          setIsLoading(false);
          onError?.(error);
        };

        // 创建 Spine Player 实例
        new SpinePlayer(containerId, spineConfig);

      } catch (error) {
        console.error('使用缓存资源创建播放器失败:', error);
        // 回退到传统方式
        await createSpinePlayerTraditional(containerId);
      }
    };

    // 传统方式创建 SpinePlayer（回退方案）
    const createSpinePlayerTraditional = async (containerId: string) => {
      try {
        console.log('📥 SpineRobot: 传统方式加载 jqr 资源');

        // 配置 Spine Player
        const spineConfig: any = {
          jsonUrl: `${SPINE_CONFIG.basePath}/${SPINE_CONFIG.jsonFileName}`,
          atlasUrl: `${SPINE_CONFIG.basePath}/${SPINE_CONFIG.atlasFileName}`,
          animation: animation,
          backgroundColor: '#00000000', // 透明背景
          alpha: true,
          showControls: false,
          showLoading: false,
          preserveDrawingBuffer: false,
          premultipliedAlpha: false,
          viewport: {
            x: -1000,
            y: -800,
            width: 2000,
            height: 3200,
            padLeft: 200,
            padRight: 200,
            padTop: 300,
            padBottom: 200
          }
        };

        // 添加成功和错误回调
        spineConfig.success = (player: any) => {
          console.log('💫 SpineRobot: 传统方式加载成功');
          spinePlayerRef.current = player;

          // 将 player 添加到活跃实例管理
          activeSpinePlayers.set(containerId, player);

          // 设置播放速度和动画
          try {
            if (player.animationState) {
              player.animationState.timeScale = timeScale;

              // 验证动画是否存在
              const skeletonData = player.skeleton?.data;
              if (skeletonData) {
                const animationData = skeletonData.findAnimation(animation);
                if (!animationData) {
                  console.warn(`Animation "${animation}" not found, using fallback "think"`);
                  const fallbackAnimation = skeletonData.findAnimation('think');
                  if (fallbackAnimation) {
                    player.animationState.setAnimation(0, 'think', true);
                  }
                }
              }
            }
          } catch (validationError) {
            console.warn('Animation validation failed:', validationError);
          }

          setIsLoading(false);
          onLoad?.();
        };

        spineConfig.error = (_player: any, reason: string) => {
          console.error('Spine animation failed to load:', reason);
          const error = new Error(`Spine animation failed to load: ${reason}`);
          setHasError(true);
          setIsLoading(false);
          onError?.(error);
        };

        // 创建 Spine Player 实例
        new SpinePlayer(containerId, spineConfig);

      } catch (error) {
        console.error('传统方式创建播放器失败:', error);
        setHasError(true);
        setIsLoading(false);
        onError?.(error as Error);
      }
    };

    // 延迟初始化，确保 DOM 已准备好
    const timeoutId = setTimeout(initSpinePlayer, 100);

    return () => {
      clearTimeout(timeoutId);

      // 清理当前组件的 player 实例
      const currentContainerId = container.id;
      if (currentContainerId && activeSpinePlayers.has(currentContainerId)) {
        const player = activeSpinePlayers.get(currentContainerId);
        if (player && typeof player.dispose === 'function') {
          try {
            player.dispose();
          } catch (error) {
            console.warn('清理 player 实例失败:', error);
          }
        }
        activeSpinePlayers.delete(currentContainerId);
      }

      // 清理组件引用
      spinePlayerRef.current = null;

      // 安全地清空容器
      if (container?.parentNode) {
        try {
          container.innerHTML = '';
          container.removeAttribute('id');
        } catch (error) {
          console.warn('清理容器失败:', error);
        }
      }
    };
  }, []); // 只在组件挂载时初始化一次

  // 动画切换 useEffect
  useEffect(() => {
    // 检查当前是否有可用的 spine player 实例
    const currentPlayer = spinePlayerRef.current;
    if (!currentPlayer?.animationState) {
      console.log(`🔄 SpineRobot: 等待资源加载完成后设置动画 ${animation}`);
      return;
    }

    try {
      // 验证动画是否存在
      const skeletonData = currentPlayer.skeleton?.data;
      if (skeletonData) {
        const animationData = skeletonData.findAnimation(animation);
        if (animationData) {
          currentPlayer.animationState.setAnimation(0, animation, loop);
          currentPlayer.animationState.timeScale = timeScale;
          console.log(`🎬 SpineRobot: 成功切换动画 ${animation}`);
        } else {
          console.warn(`Animation "${animation}" not found, falling back to "think"`);
          // 回退到 think 动画
          const fallbackAnimation = skeletonData.findAnimation('think');
          if (fallbackAnimation) {
            currentPlayer.animationState.setAnimation(0, 'think', loop);
            currentPlayer.animationState.timeScale = timeScale;
            console.log('🔄 SpineRobot: 回退到 think 动画');
          } else {
            console.error('Even fallback animation "think" not found');
          }
        }
      } else {
        // 如果没有 skeleton 数据，直接尝试设置动画
        currentPlayer.animationState.setAnimation(0, animation, loop);
        currentPlayer.animationState.timeScale = timeScale;
        console.log(`🎬 SpineRobot: 直接设置动画 ${animation}`);
      }
    } catch (error) {
      console.warn(`Failed to update animation "${animation}":`, error);
      // 尝试回退到 think 动画
      try {
        currentPlayer.animationState.setAnimation(0, 'think', loop);
        currentPlayer.animationState.timeScale = timeScale;
        console.log('🔄 SpineRobot: 成功回退到 think 动画');
      } catch (fallbackError) {
        console.error('Failed to set fallback animation:', fallbackError);
        const errorMessage = error instanceof Error ? error.message : String(error);
        const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
        onError?.(new Error(`Animation error: ${errorMessage}, fallback failed: ${fallbackErrorMessage}`));
      }
    }
  }, [animation, loop, timeScale, onError]);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      // 组件卸载时，不清理全局资源缓存，只清理当前组件的引用和实例
      const container = containerRef.current;
      const currentContainerId = container?.id;
      
      if (currentContainerId && activeSpinePlayers.has(currentContainerId)) {
        const player = activeSpinePlayers.get(currentContainerId);
        if (player && typeof player.dispose === 'function') {
          try {
            player.dispose();
          } catch (error) {
            console.warn('组件卸载时清理 player 实例失败:', error);
          }
        }
        activeSpinePlayers.delete(currentContainerId);
      }

      // 安全地清理容器 - 使用改进的清理方法
      if (container?.parentNode) {
        try {
          // 使用innerHTML清空，避免removeChild竞态条件
          container.innerHTML = '';
          container.removeAttribute('id');
        } catch (error) {
          console.warn('组件卸载时清理容器失败:', error);
        }
      }

      spinePlayerRef.current = null;
      console.log('🧹 SpineRobot: 组件卸载，清理引用和实例');
    };
  }, []);

  return (
    <div className={`spine-robot-container ${className}`}>
      {/* Spine 动画容器 */}
      <div
        ref={containerRef}
        className="spine-robot-canvas"
        style={{
          width: '350px',
          height: '350px',
          position: 'relative',
          zIndex: 35,
        }}
      />

      {/* 加载指示器 */}
      {isLoading && !hasError && (
        <div className="spine-robot-loading">
          <div className="spine-loading-spinner" />
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <div className="spine-robot-error" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#666',
          fontSize: '14px',
          width: '350px',
          height: '350px'
        }}>
          动画加载失败
        </div>
      )}
    </div>
  );
};

// 导出清理函数，供外部调用
export const clearSpineCache = clearResourceCache;

export default SpineRobot;
