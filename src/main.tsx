import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";

import App from "./App.tsx";
import { Provider } from "./provider.tsx";
import ErrorBoundary from "./components/ErrorBoundary.tsx";
import { initializeAudioContext } from "./utils/ttsService";
import "@/styles/globals.css";

// 初始化AudioContext以支持iOS自动播放
window.addEventListener('load', () => {
  initializeAudioContext();
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <BrowserRouter>
        <Provider>
          <App />
        </Provider>
      </BrowserRouter>
    </ErrorBoundary>
  </React.StrictMode>,
);
