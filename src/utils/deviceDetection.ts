/**
 * 设备检测工具函数
 * 用于判断当前设备是否为移动设备
 */

/**
 * 检测是否为移动设备
 * 使用多重检测策略确保准确性
 */
export const isMobileDevice = (): boolean => {
  // 检查 User Agent
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'mobile', 'android', 'iphone', 'ipad', 'tablet',
    'blackberry', 'windows phone', 'opera mini'
  ];
  
  const isMobileUA = mobileKeywords.some(keyword => 
    userAgent.includes(keyword)
  );
  
  // 检查屏幕宽度（移动端通常小于768px）
  const screenWidth = window.innerWidth;
  const isSmallScreen = screenWidth <= 768;
  
  // 检查触摸支持
  const hasTouchSupport = 'ontouchstart' in window || 
                         navigator.maxTouchPoints > 0;
  
  // 综合判断：满足任一条件即认为是移动设备
  return isMobileUA || isSmallScreen || hasTouchSupport;
};

/**
 * 获取设备类型描述
 */
export const getDeviceType = (): 'mobile' | 'desktop' => {
  return isMobileDevice() ? 'mobile' : 'desktop';
};

/**
 * 检测是否为iOS设备
 */
export const isIOS = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
};

/**
 * 检测是否为Android设备
 */
export const isAndroid = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /android/.test(userAgent);
};
