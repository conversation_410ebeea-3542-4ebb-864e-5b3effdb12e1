/**
 * 认证相关工具函数
 * 整合设备检测和JWT验证逻辑
 */

import { isMobileDevice } from './deviceDetection';
import { isCurrentTokenValid, clearToken, getToken } from './jwtUtils';
import { buildAuthApiUrl, buildChatApiUrl, AUTH_ENDPOINTS, CHAT_ENDPOINTS } from './apiConfig';

/**
 * 认证状态枚举
 */
export enum AuthStatus {
  LOADING = 'loading',
  PC_NOT_SUPPORTED = 'pc_not_supported',
  NEED_LOGIN = 'need_login',
  AUTHENTICATED = 'authenticated'
}

/**
 * 检查用户认证状态
 * 综合考虑设备类型和JWT状态
 */
export const checkAuthStatus = (): AuthStatus => {
  try {
    // 首先检查是否为移动设备
    if (!isMobileDevice()) {
      return AuthStatus.PC_NOT_SUPPORTED;
    }
    
    // 检查JWT令牌状态
    if (!isCurrentTokenValid()) {
      // 清除无效令牌
      clearToken();
      return AuthStatus.NEED_LOGIN;
    }
    
    return AuthStatus.AUTHENTICATED;
  } catch (error) {
    console.error('检查认证状态失败:', error);
    return AuthStatus.NEED_LOGIN;
  }
};

/**
 * 手机号验证（根据不同国家区号）
 */
export const validatePhoneNumber = (phone: string, dialCode: string): { isValid: boolean; message?: string } => {
  if (!phone.trim()) {
    return { isValid: false, message: '请输入手机号' };
  }

  // 根据不同区号进行验证
  switch (dialCode) {
    case '+86': // 中国大陆
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国大陆手机号' };
      }
      break;
    case '+852': // 香港
      if (!/^[5-9]\d{7}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国香港手机号' };
      }
      break;
    case '+886': // 台湾
      if (!/^9\d{8}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国台湾手机号' };
      }
      break;
    case '+1': // 美国/加拿大
      if (!/^\d{10}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的美国/加拿大手机号' };
      }
      break;
    default: // 其他国家，基本长度验证
      if (!/^\d{7,15}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的手机号' };
      }
  }

  return { isValid: true };
};

/**
 * 验证码验证（严格6位数字）
 */
export const validateVerificationCode = (code: string): { isValid: boolean; message?: string } => {
  if (!code.trim()) {
    return { isValid: false, message: '请输入验证码' };
  }

  if (!/^\d{6}$/.test(code)) {
    return { isValid: false, message: '验证码必须为6位数字' };
  }

  return { isValid: true };
};

/**
 * 格式化手机号显示
 * 例：13812345678 -> 138 1234 5678
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`;
  }
  return phone;
};

/**
 * 模拟登录API调用
 * 实际项目中应该调用真实的后端API
 */
export const mockLogin = async (phone: string, code: string, dialCode: string): Promise<{
  success: boolean;
  token?: string;
  message?: string;
}> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 验证手机号
  const phoneValidation = validatePhoneNumber(phone, dialCode);
  if (!phoneValidation.isValid) {
    return {
      success: false,
      message: phoneValidation.message
    };
  }

  // 验证验证码
  const codeValidation = validateVerificationCode(code);
  if (!codeValidation.isValid) {
    return {
      success: false,
      message: codeValidation.message
    };
  }

  // 模拟成功登录，返回JWT令牌
  // 实际项目中这个令牌应该由后端生成
  const mockToken = generateMockJWT(`${dialCode}${phone}`);

  return {
    success: true,
    token: mockToken,
    message: '登录成功'
  };
};

/**
 * 生成模拟JWT令牌（仅用于演示）
 * 实际项目中令牌应该由后端生成
 */
const generateMockJWT = (phone: string): string => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: phone,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时后过期
  }));
  const signature = 'mock_signature';
  
  return `${header}.${payload}.${signature}`;
};

/**
 * 发送验证码API响应接口
 */
interface SendCodeResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    expires_in: number;
    code: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 用户资料接口
 */
export interface UserProfile {
  id: number;
  uuid: string;
  phone: string;
  username: string | null;
  avatar_url: string | null;
  province_code: string | null;
  province_name: string | null;
  city_code: string | null;
  city_name: string | null;
  is_active: boolean;
  created_at: string;
  last_login: string;
}

/**
 * 用户资料API响应接口
 */
export interface UserProfileResponse {
  success: boolean;
  message: string;
  data: UserProfile;
  timestamp: string;
  request_id: string;
}

/**
 * 登录API响应接口
 */
interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    device_id: string;
    user_info: {
      id: number;
      uuid: string;
      phone: string;
      username: string;
      avatar_url: string;
    };
  };
  timestamp: string;
  request_id: string;
}

/**
 * 真实发送验证码API
 */
export const sendVerificationCodeAPI = async (phone: string): Promise<{
  success: boolean;
  message?: string;
  code?: string;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.SEND_CODE), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone,
        purpose: 'login'
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: SendCodeResponse = await response.json();

    if (data.success && data.data.success) {
      return {
        success: true,
        message: data.data.message,
        code: data.data.code // 返回验证码用于自动填充
      };
    } else {
      return {
        success: false,
        message: data.message || '发送验证码失败'
      };
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 真实登录API
 */
export const loginWithCodeAPI = async (phone: string, code: string): Promise<{
  success: boolean;
  message?: string;
  loginData?: any;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.LOGIN), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone,
        code: code
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: LoginResponse = await response.json();

    if (data.success && data.data.success) {
      // 排除user_info，只返回其他数据用于存储
      const { user_info, ...loginDataWithoutUserInfo } = data.data;

      return {
        success: true,
        message: data.data.message,
        loginData: loginDataWithoutUserInfo
      };
    } else {
      return {
        success: false,
        message: data.message || '登录失败'
      };
    }
  } catch (error) {
    console.error('登录失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 模拟发送验证码API（保留用于兼容性）
 */
export const mockSendVerificationCode = async (phone: string, dialCode: string): Promise<{
  success: boolean;
  message?: string;
}> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  const phoneValidation = validatePhoneNumber(phone, dialCode);
  if (!phoneValidation.isValid) {
    return {
      success: false,
      message: phoneValidation.message
    };
  }

  return {
    success: true,
    message: '验证码已发送'
  };
};

/**
 * 创建会话API响应接口
 */
interface CreateConversationResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    conversation_id: string;
    user_id: number;
    title: string;
    is_deleted: boolean;
    created_at: string;
    updated_at: string | null;
    deleted_at: string | null;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 创建新会话API
 * @param title 会话标题，默认为"新的会话"
 * @returns Promise<{success: boolean, conversationId?: string, message?: string}>
 */
export const createConversationAPI = async (title: string = '新的会话'): Promise<{
  success: boolean;
  conversationId?: string;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.CREATE_CONVERSATION), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ title }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: CreateConversationResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        conversationId: data.data.conversation_id,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '创建会话失败'
      };
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 获取会话列表API响应接口
 */
interface GetConversationsResponse {
  success: boolean;
  message: string;
  data: Array<{
    id: number;
    conversation_id: string;
    title: string;
    created_at: string; // ISO字符串
    updated_at: string | null; // ISO字符串或null
  }>;
  timestamp: string;
  request_id: string;
}

/**
 * 会话数据接口（API格式）
 */
export interface APIConversation {
  id: number;
  conversation_id: string;
  title: string;
  created_at: string;
  updated_at: string | null;
}

/**
 * 获取会话列表API
 * @returns Promise<{success: boolean, conversations?: APIConversation[], message?: string}>
 */
export const getConversationsAPI = async (): Promise<{
  success: boolean;
  conversations?: APIConversation[];
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.CREATE_CONVERSATION), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: GetConversationsResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        conversations: data.data,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '获取会话列表失败'
      };
    }
  } catch (error) {
    console.error('获取会话列表失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 获取用户资料API
 * @returns Promise<{success: boolean, userProfile?: UserProfile, message?: string}>
 */
export const getUserProfileAPI = async (): Promise<{
  success: boolean;
  userProfile?: UserProfile;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.USER_PROFILE), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: UserProfileResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        userProfile: data.data,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '获取用户资料失败'
      };
    }
  } catch (error) {
    console.error('获取用户资料失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

// 防止重复请求的标志
let isRefreshingUserProfile = false;
let refreshPromise: Promise<{success: boolean; userProfile?: UserProfile; message?: string}> | null = null;

/**
 * 刷新用户资料
 * @param forceRefresh 是否强制刷新，忽略缓存
 * @returns Promise<{success: boolean, userProfile?: UserProfile, message?: string}>
 */
export const refreshUserProfile = async (forceRefresh: boolean = false): Promise<{
  success: boolean;
  userProfile?: UserProfile;
  message?: string;
}> => {
  try {
    // 导入用户信息工具函数（避免循环依赖）
    const { getUserProfile, saveUserProfile, isUserProfileCached } = await import('./userInfoUtils');

    // 如果不强制刷新且有缓存，返回缓存数据
    if (!forceRefresh && isUserProfileCached()) {
      const cachedProfile = getUserProfile();
      if (cachedProfile) {
        console.log('📋 使用缓存的用户资料');
        return {
          success: true,
          userProfile: cachedProfile,
          message: '从缓存获取用户资料'
        };
      }
    }

    // 如果正在刷新中，返回同一个Promise
    if (isRefreshingUserProfile && refreshPromise) {
      console.log('⏳ 用户资料正在刷新中，等待结果...');
      return refreshPromise;
    }

    // 开始刷新
    isRefreshingUserProfile = true;
    refreshPromise = (async () => {
      try {
        console.log('🔄 开始从服务器获取用户资料');
        const result = await getUserProfileAPI();

        if (result.success && result.userProfile) {
          // 保存到缓存
          saveUserProfile(result.userProfile);
          console.log('✅ 用户资料已更新并缓存');
        }

        return result;
      } finally {
        // 重置标志
        isRefreshingUserProfile = false;
        refreshPromise = null;
      }
    })();

    return refreshPromise;
  } catch (error) {
    console.error('❌ 刷新用户资料失败:', error);
    // 重置标志
    isRefreshingUserProfile = false;
    refreshPromise = null;
    return {
      success: false,
      message: '刷新用户资料失败'
    };
  }
};

/**
 * 头像上传API响应接口
 */
export interface AvatarUploadResponse {
  success: boolean;
  message: string;
  data: {
    avatar_url: string;
    filename: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 昵称更新API响应接口
 */
export interface UpdateUsernameResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    username: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 省份信息接口
 */
export interface ProvinceInfo {
  id: number;
  uuid: string;
  code: string;
  name: string;
  is_active: boolean;
  created_at: string;
}

/**
 * 城市信息接口
 */
export interface CityInfo {
  id: number;
  uuid: string;
  code: string;
  name: string;
  province_code: string;
  is_active: boolean;
  created_at: string;
}

/**
 * 获取省份列表API响应接口
 */
export interface ProvincesResponse {
  success: boolean;
  message: string;
  data: ProvinceInfo[];
  timestamp: string;
  request_id: string;
}

/**
 * 获取城市列表API响应接口
 */
export interface CitiesResponse {
  success: boolean;
  message: string;
  data: CityInfo[];
  timestamp: string;
  request_id: string;
}

/**
 * 更新地址API响应接口
 */
export interface UpdateAddressResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    province_code: string;
    city_code: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 上传头像API
 * @param file 图片文件
 * @returns Promise<{success: boolean, avatarUrl?: string, filename?: string, message?: string}>
 */
export const uploadAvatarAPI = async (file: File): Promise<{
  success: boolean;
  avatarUrl?: string;
  filename?: string;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.AVATAR_UPLOAD), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AvatarUploadResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        avatarUrl: data.data.avatar_url,
        filename: data.data.filename,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '头像上传失败'
      };
    }
  } catch (error) {
    console.error('头像上传失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 更新用户昵称API
 * @param username 新的用户昵称
 * @returns Promise<{success: boolean, username?: string, message?: string}>
 */
export const updateUsernameAPI = async (username: string): Promise<{
  success: boolean;
  username?: string;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.UPDATE_USERNAME), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        username: username
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: UpdateUsernameResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        username: data.data.username,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '昵称更新失败'
      };
    }
  } catch (error) {
    console.error('昵称更新失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 获取省份列表API
 * @returns Promise<{success: boolean, provinces?: ProvinceInfo[], message?: string}>
 */
export const getProvincesAPI = async (): Promise<{
  success: boolean;
  provinces?: ProvinceInfo[];
  message?: string;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.PROVINCES), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ProvincesResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        provinces: data.data,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '获取省份列表失败'
      };
    }
  } catch (error) {
    console.error('获取省份列表失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 获取城市列表API
 * @param provinceCode 省份代码
 * @returns Promise<{success: boolean, cities?: CityInfo[], message?: string}>
 */
export const getCitiesAPI = async (provinceCode: string): Promise<{
  success: boolean;
  cities?: CityInfo[];
  message?: string;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(`${AUTH_ENDPOINTS.CITIES}/${provinceCode}`), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: CitiesResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        cities: data.data,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '获取城市列表失败'
      };
    }
  } catch (error) {
    console.error('获取城市列表失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 更新用户地址API
 * @param provinceCode 省份代码
 * @param cityCode 城市代码
 * @returns Promise<{success: boolean, message?: string}>
 */
export const updateAddressAPI = async (provinceCode: string, cityCode: string): Promise<{
  success: boolean;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.UPDATE_ADDRESS), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        province_code: provinceCode,
        city_code: cityCode
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: UpdateAddressResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '地址更新失败'
      };
    }
  } catch (error) {
    console.error('地址更新失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

// 退出登录API响应接口
interface LogoutResponse {
  success: boolean;
  message: string;
  data: {
    logout_time: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 退出登录API
 * @returns Promise<{success: boolean, message?: string, logout_time?: string}>
 */
export const logoutAPI = async (): Promise<{
  success: boolean;
  message?: string;
  logout_time?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      // 如果没有token，认为已经退出登录
      return {
        success: true,
        message: '已退出登录'
      };
    }

    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.LOGOUT), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: '',
    });

    if (response.ok) {
      const data: LogoutResponse = await response.json();

      if (data.success) {
        console.log('✅ 服务器退出登录成功:', {
          message: data.message,
          logout_time: data.data.logout_time,
          request_id: data.request_id
        });

        return {
          success: true,
          message: data.message,
          logout_time: data.data.logout_time
        };
      } else {
        console.warn('⚠️ 服务器返回退出登录失败:', data.message);
        // 即使服务器返回失败，也继续本地清理
        return {
          success: true,
          message: '退出登录成功'
        };
      }
    } else {
      console.warn(`⚠️ 服务器退出登录返回状态: ${response.status}`);
      // 即使服务器出错，也继续本地清理
      return {
        success: true,
        message: '退出登录成功'
      };
    }
  } catch (error) {
    console.warn('⚠️ 退出登录API调用失败，但仍继续本地清理:', error);
    // 即使API调用失败，也返回成功，确保本地数据被清除
    return {
      success: true,
      message: '退出登录成功'
    };
  }
};
