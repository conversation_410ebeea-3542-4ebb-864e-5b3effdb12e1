/**
 * TTS语音合成服务（新API）
 */

// @ts-ignore
import StartAudioContext from 'startaudiocontext';
import { getToken } from './jwtUtils';
import { buildChatApiUrl, CHAT_ENDPOINTS } from './apiConfig';

// 全局AudioContext实例
let globalAudioContext: AudioContext | null = null;

/**
 * 初始化全局AudioContext
 */
export function initializeAudioContext(): void {
  if (!globalAudioContext) {
    try {
      globalAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log('TTS: AudioContext初始化成功');

      // 使用StartAudioContext处理移动端兼容性
      StartAudioContext(globalAudioContext);
      console.log('TTS: StartAudioContext初始化完成');
    } catch (error) {
      console.error('TTS: AudioContext初始化失败:', error);
    }
  }
}

/**
 * 获取全局AudioContext
 */
function getAudioContext(): AudioContext | null {
  if (!globalAudioContext) {
    initializeAudioContext();
  }
  return globalAudioContext;
}

// TTS配置常量 - 使用新的API端点
const TTS_CONFIG = {
  // 使用新的API端点，通过认证API代理
  getApiUrl: () => buildChatApiUrl(CHAT_ENDPOINTS.TTS)
};

// TTS请求接口（新API格式）
interface TTSRequest {
  message: string;
}

// TTS响应接口（新API格式）
interface TTSResponse {
  success: boolean;
  message: string;
  data: {
    data: {
      reqid: string;
      code: number;
      operation: string;
      message: string;
      sequence: number;
      data: string; // base64编码的音频数据
      addition: {
        duration: string;
        first_pkg: string;
      };
    };
  };
  timestamp: string;
  request_id: string;
}



/**
 * 将base64音频数据转换为Blob
 */
function base64ToBlob(base64Data: string, mimeType: string = 'audio/mp3'): Blob {
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * 使用Web Audio API播放音频Blob（iOS自动播放优化）
 */
async function playAudioWithWebAudioAPI(audioBlob: Blob): Promise<void> {
  try {
    const audioContext = getAudioContext();
    if (!audioContext) {
      throw new Error('AudioContext未初始化');
    }

    // 恢复AudioContext（如果被暂停）
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    // 将Blob转换为ArrayBuffer
    const arrayBuffer = await audioBlob.arrayBuffer();

    // 解码音频数据
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    // 创建音频源
    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);

    // 返回Promise等待播放完成
    return new Promise((resolve) => {
      // 设置播放结束回调
      source.onended = () => {
        console.log('TTS: Web Audio API播放完成');
        resolve();
      };

      // Web Audio API的AudioBufferSourceNode没有onerror事件
      // 错误处理在try-catch中进行

      // 开始播放
      source.start(0);
      console.log('TTS: Web Audio API开始播放');
    });

  } catch (error) {
    console.error('TTS: Web Audio API播放失败:', error);
    throw error instanceof Error ? error : new Error('Web Audio API播放失败');
  }
}

/**
 * 播放音频（HTML5 Audio备用方案）
 */
function playAudio(audioBlob: Blob): Promise<void> {
  return new Promise((resolve, reject) => {
    const audioUrl = URL.createObjectURL(audioBlob);
    const audio = new Audio(audioUrl);

    audio.onended = () => {
      URL.revokeObjectURL(audioUrl);
      console.log('TTS: 音频播放自然结束');
      resolve();
    };

    audio.onerror = (error) => {
      URL.revokeObjectURL(audioUrl);
      console.error('TTS: 音频播放出错:', error);
      reject(new Error('音频播放失败'));
    };

    // 尝试播放音频，处理自动播放策略限制
    audio.play().then(() => {
      console.log('TTS: 音频开始播放');
    }).catch((error) => {
      URL.revokeObjectURL(audioUrl);
      console.error('TTS: 音频播放被阻止:', error);

      // 检查是否是自动播放策略限制
      if (error.name === 'NotAllowedError' || error.message.includes('play')) {
        reject(new Error('音频自动播放被浏览器阻止，请点击TTS按钮手动播放'));
      } else {
        reject(new Error(`音频播放失败: ${error.message}`));
      }
    });
  });
}

/**
 * 可控制的音频播放器类
 */
export class ControllableAudio {
  private audio: HTMLAudioElement | null = null;
  private audioUrl: string | null = null;
  private _isDestroyed: boolean = false;
  private _onPlayEnd: (() => void) | null = null;
  private _onPlayError: ((error: Error) => void) | null = null;
  private webAudioSource: AudioBufferSourceNode | null = null;

  constructor(private readonly audioBlob: Blob) {}

  /**
   * 开始播放（优先使用Web Audio API）
   */
  async play(): Promise<void> {
    if (this._isDestroyed) {
      throw new Error('音频对象已被销毁');
    }

    // 如果已经有音频在播放，直接播放
    if (this.audio) {
      return this.audio.play();
    }

    // 优先尝试Web Audio API
    try {
      await this.playWithWebAudioAPI();
    } catch (webAudioError) {
      console.warn('TTS: ControllableAudio Web Audio API播放失败，回退到HTML5 Audio:', webAudioError);
      await this.playWithHTMLAudio();
    }
  }

  /**
   * 使用Web Audio API播放
   */
  private async playWithWebAudioAPI(): Promise<void> {
    const audioContext = getAudioContext();
    if (!audioContext) {
      throw new Error('AudioContext未初始化');
    }

    // 恢复AudioContext（如果被暂停）
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    // 将Blob转换为ArrayBuffer
    const arrayBuffer = await this.audioBlob.arrayBuffer();

    // 解码音频数据
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    // 创建音频源
    this.webAudioSource = audioContext.createBufferSource();
    this.webAudioSource.buffer = audioBuffer;
    this.webAudioSource.connect(audioContext.destination);

    // 返回Promise等待播放完成
    return new Promise((resolve, reject) => {
      if (!this.webAudioSource) {
        reject(new Error('Web Audio源创建失败'));
        return;
      }

      // 设置播放结束回调
      this.webAudioSource.onended = () => {
        console.log('TTS: ControllableAudio Web Audio API播放完成');
        this._onPlayEnd?.();
        this.cleanup();
        resolve();
      };

      // 开始播放
      this.webAudioSource.start(0);
      console.log('TTS: ControllableAudio Web Audio API开始播放');
    });
  }

  /**
   * 使用HTML5 Audio播放（备用方案）
   */
  private playWithHTMLAudio(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.audioUrl = URL.createObjectURL(this.audioBlob);
      this.audio = new Audio(this.audioUrl);

      this.audio.onended = () => {
        console.log('TTS: ControllableAudio HTML5 Audio播放自然结束');
        this._onPlayEnd?.();
        this.cleanup();
        resolve();
      };

      this.audio.onerror = () => {
        console.error('TTS: ControllableAudio HTML5 Audio播放出错');
        const error = new Error('音频播放失败');
        this._onPlayError?.(error);
        this.cleanup();
        reject(error);
      };

      // 监听音频被打断的情况
      this.audio.onpause = () => {
        if (!this._isDestroyed && this.audio && this.audio.currentTime > 0 && !this.audio.ended) {
          console.log('TTS: ControllableAudio HTML5 Audio播放被暂停');
        }
      };

      this.audio.play().then(resolve).catch(reject);
    });
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (this.audio && !this._isDestroyed) {
      console.log('TTS: 暂停音频播放');
      this.audio.pause();
    }
  }

  /**
   * 停止播放并清理资源
   */
  stop(): void {
    if (this._isDestroyed) return;

    console.log('TTS: 强制停止音频播放');

    // 停止Web Audio API播放
    if (this.webAudioSource) {
      try {
        // 先断开连接，再停止，避免异常
        this.webAudioSource.disconnect();
        this.webAudioSource.stop();
      } catch (error) {
        console.warn('TTS: 停止Web Audio源时出错:', error);
        // 即使出错也要清理引用
      }
      this.webAudioSource = null;
    }

    // 停止HTML5 Audio播放
    if (this.audio) {
      try {
        this.audio.pause();
        this.audio.currentTime = 0;
        // 移除事件监听器，防止触发回调
        this.audio.onended = null;
        this.audio.onerror = null;
        this.audio.onpause = null;
      } catch (error) {
        console.warn('TTS: 停止HTML5 Audio时出错:', error);
      }
    }

    // 触发停止事件
    this._onPlayEnd?.();
    this.cleanup();
  }

  /**
   * 立即销毁音频对象（用于强制打断）
   */
  destroy(): void {
    if (this._isDestroyed) return;

    console.log('TTS: 销毁音频对象');
    this._isDestroyed = true;

    // 先停止Web Audio API播放
    if (this.webAudioSource) {
      try {
        this.webAudioSource.disconnect();
        this.webAudioSource.stop();
      } catch (error) {
        console.warn('TTS: 销毁时停止Web Audio源出错:', error);
      }
      this.webAudioSource = null;
    }

    // 再停止HTML5 Audio播放
    if (this.audio) {
      try {
        this.audio.pause();
        this.audio.currentTime = 0;
        // 移除所有事件监听器
        this.audio.onended = null;
        this.audio.onerror = null;
        this.audio.onpause = null;
        this.audio.src = '';
        this.audio.load(); // 强制释放音频资源
      } catch (error) {
        console.warn('TTS: 销毁HTML5 Audio时出错:', error);
      }
    }

    this.cleanup();
  }

  /**
   * 设置播放结束回调
   */
  onPlayEnd(callback: () => void): void {
    this._onPlayEnd = callback;
  }

  /**
   * 设置播放错误回调
   */
  onPlayError(callback: (error: Error) => void): void {
    this._onPlayError = callback;
  }

  /**
   * 获取播放状态
   */
  get isPlaying(): boolean {
    return this.audio ? !this.audio.paused && !this._isDestroyed : false;
  }

  /**
   * 获取是否已销毁
   */
  get isDestroyed(): boolean {
    return this._isDestroyed;
  }

  /**
   * 获取播放进度（0-1）
   */
  get progress(): number {
    if (!this.audio || !this.audio.duration || this._isDestroyed) return 0;
    return this.audio.currentTime / this.audio.duration;
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.audioUrl) {
      URL.revokeObjectURL(this.audioUrl);
      this.audioUrl = null;
    }
    this.audio = null;
  }
}

/**
 * 调用TTS API进行语音合成
 */
export async function synthesizeText(text: string): Promise<void> {
  const timestamp = new Date().toISOString();
  console.log(`TTS: [${timestamp}] synthesizeText 被调用，原始文本:`, text.substring(0, 100) + '...');

  try {
    // 清理文本，移除特殊字符和多余空格
    const cleanText = text
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""''（）【】]/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    if (!cleanText) {
      console.warn('TTS: 文本为空，跳过语音合成');
      return;
    }

    // 如果文本过长，截取前200个字符
    const maxLength = 200;
    const finalText = cleanText.length > maxLength
      ? cleanText.substring(0, maxLength) + '...'
      : cleanText;

    console.log('TTS: 开始合成语音，清理后文本:', finalText);

    // 获取JWT token
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 构建请求数据（新API格式）
    const requestData: TTSRequest = {
      message: finalText
    };

    const apiUrl = TTS_CONFIG.getApiUrl();
    console.log('TTS: 发送请求到:', apiUrl);
    console.log('TTS: 请求数据:', JSON.stringify(requestData, null, 2));

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });

    console.log('TTS: 响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('TTS: API请求失败，响应内容:', errorText);
      throw new Error(`TTS API请求失败: ${response.status} ${response.statusText}`);
    }

    const result: TTSResponse = await response.json();
    console.log('TTS: API响应:', result);

    if (!result.success) {
      console.error('TTS: 合成失败，错误详情:', result);
      throw new Error(`TTS合成失败: ${result.message}`);
    }

    if (!result.data?.data?.data) {
      throw new Error('TTS响应中没有音频数据');
    }

    const ttsData = result.data.data;
    if (ttsData.code !== 3000) {
      console.error('TTS: 合成失败，错误详情:', ttsData);
      throw new Error(`TTS合成失败: ${ttsData.message} (code: ${ttsData.code})`);
    }

    console.log('TTS: 语音合成成功，音频时长:', ttsData.addition?.duration, 'ms');

    // 将base64音频数据转换为Blob并播放
    const audioBlob = base64ToBlob(ttsData.data);

    // 检查音频播放权限
    if (!hasAudioPermission()) {
      console.warn('TTS: 尝试获取音频播放权限...');
      const permissionGranted = await requestAudioPermission();
      if (!permissionGranted) {
        throw new Error('无法获取音频播放权限，请手动点击TTS按钮播放');
      }
    }

    // 优先使用Web Audio API，iOS设备自动播放效果更好
    try {
      await playAudioWithWebAudioAPI(audioBlob);
    } catch (webAudioError) {
      console.warn('TTS: Web Audio API播放失败，回退到HTML5 Audio:', webAudioError);
      await playAudio(audioBlob);
    }

    console.log('TTS: 音频播放完成');
    
  } catch (error) {
    console.error('TTS语音合成失败:', error);
    throw error;
  }
}

/**
 * 合成语音并返回可控制的音频对象
 */
export async function synthesizeTextControllable(text: string): Promise<ControllableAudio> {
  const timestamp = new Date().toISOString();
  console.log(`TTS: [${timestamp}] synthesizeTextControllable 被调用，原始文本:`, text.substring(0, 100) + '...');

  try {
    // 清理文本，移除特殊字符和多余空格
    const cleanText = text
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""''（）【】]/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    if (!cleanText) {
      throw new Error('文本为空，无法进行语音合成');
    }

    // 如果文本过长，截取前200个字符
    const maxLength = 200;
    const finalText = cleanText.length > maxLength
      ? cleanText.substring(0, maxLength) + '...'
      : cleanText;

    console.log('TTS: 开始合成语音，清理后文本:', finalText);

    // 获取JWT token
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 构建请求数据（新API格式）
    const requestData: TTSRequest = {
      message: finalText
    };

    const apiUrl = TTS_CONFIG.getApiUrl();
    console.log('TTS: 发送请求到:', apiUrl);
    console.log('TTS: 请求数据:', JSON.stringify(requestData, null, 2));

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });

    console.log('TTS: 响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('TTS: API请求失败，响应内容:', errorText);
      throw new Error(`TTS API请求失败: ${response.status} ${response.statusText}`);
    }

    const result: TTSResponse = await response.json();
    console.log('TTS: API响应:', result);

    if (!result.success) {
      console.error('TTS: 合成失败，错误详情:', result);
      throw new Error(`TTS合成失败: ${result.message}`);
    }

    if (!result.data?.data?.data) {
      throw new Error('TTS响应中没有音频数据');
    }

    const ttsData = result.data.data;
    if (ttsData.code !== 3000) {
      console.error('TTS: 合成失败，错误详情:', ttsData);
      throw new Error(`TTS合成失败: ${ttsData.message} (code: ${ttsData.code})`);
    }

    console.log('TTS: 语音合成成功，音频时长:', ttsData.addition?.duration, 'ms');

    // 将base64音频数据转换为Blob并返回可控制的音频对象
    const audioBlob = base64ToBlob(ttsData.data);
    return new ControllableAudio(audioBlob);

  } catch (error) {
    console.error('TTS语音合成失败:', error);
    throw error;
  }
}

/**
 * 音频播放权限管理
 */
let audioPermissionGranted = false;

/**
 * 检测是否为iOS设备
 */
function isIOSDevice(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 请求音频播放权限（优先使用Web Audio API）
 */
export async function requestAudioPermission(): Promise<boolean> {
  if (audioPermissionGranted) {
    return true;
  }

  try {
    // 优先使用全局AudioContext
    const audioContext = getAudioContext();
    if (audioContext) {
      // 恢复AudioContext（如果被暂停）
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // 设置为静音
      gainNode.gain.value = 0;
      oscillator.frequency.value = 440;

      // 播放极短时间
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.01);

      // 等待播放完成
      await new Promise(resolve => setTimeout(resolve, 50));

      audioPermissionGranted = true;
      console.log('TTS: Web Audio API权限已获取');
      return true;
    }

    // 备用方案：iOS设备使用HTML5 Audio
    if (isIOSDevice()) {
      return await requestIOSAudioPermission();
    }

    // 其他设备创建临时AudioContext
    const tempAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = tempAudioContext.createOscillator();
    const gainNode = tempAudioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(tempAudioContext.destination);

    // 设置为静音
    gainNode.gain.value = 0;
    oscillator.frequency.value = 440;

    // 播放极短时间
    oscillator.start();
    oscillator.stop(tempAudioContext.currentTime + 0.01);

    // 等待播放完成
    await new Promise(resolve => setTimeout(resolve, 50));

    audioPermissionGranted = true;
    console.log('TTS: 音频播放权限已获取');
    return true;
  } catch (error) {
    console.warn('TTS: 无法获取音频播放权限:', error);
    return false;
  }
}

/**
 * iOS设备专用的音频权限获取方法
 */
async function requestIOSAudioPermission(): Promise<boolean> {
  try {
    console.log('TTS: iOS设备权限获取开始');

    // 创建一个极短的实际音频来测试播放权限
    // 使用data URL创建一个极短的静音音频文件
    const silentAudioData = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

    const audio = new Audio(silentAudioData);
    audio.volume = 0.01; // 设置极低音量
    audio.muted = true; // 静音播放

    // 尝试播放
    await audio.play();

    // 立即暂停
    audio.pause();
    audio.currentTime = 0;

    audioPermissionGranted = true;
    console.log('TTS: iOS音频播放权限已获取');
    return true;
  } catch (error) {
    console.warn('TTS: iOS音频权限获取失败:', error);

    // 如果是NotAllowedError，说明需要用户交互
    if (error instanceof Error && error.name === 'NotAllowedError') {
      console.log('TTS: iOS需要用户交互才能获取音频权限');
      return false;
    }

    return false;
  }
}

/**
 * 强制请求音频权限（需要用户交互触发）
 */
export async function forceRequestAudioPermission(): Promise<boolean> {
  try {
    console.log('TTS: 强制请求音频权限');

    // 首先确保全局AudioContext已初始化
    if (!globalAudioContext) {
      initializeAudioContext();
    }

    if (isIOSDevice()) {
      // iOS设备：优先尝试Web Audio API
      try {
        const audioContext = getAudioContext();
        if (audioContext) {
          // 恢复AudioContext（如果被暂停）
          if (audioContext.state === 'suspended') {
            await audioContext.resume();
          }

          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          gainNode.gain.value = 0.01; // 极低音量
          oscillator.frequency.value = 440;

          oscillator.start();
          oscillator.stop(audioContext.currentTime + 0.01);

          await new Promise(resolve => setTimeout(resolve, 50));

          audioPermissionGranted = true;
          console.log('TTS: iOS Web Audio API权限获取成功');
          return true;
        }
      } catch (webAudioError) {
        console.warn('TTS: iOS Web Audio API权限获取失败，尝试HTML5 Audio:', webAudioError);
      }

      // 备用方案：HTML5 Audio
      const silentAudioData = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

      const audio = new Audio(silentAudioData);
      audio.volume = 0.01;

      await audio.play();
      audio.pause();

      audioPermissionGranted = true;
      console.log('TTS: iOS HTML5 Audio权限获取成功');
      return true;
    } else {
      // 其他设备：使用全局AudioContext或创建新的
      const audioContext = getAudioContext() || new (window.AudioContext || (window as any).webkitAudioContext)();

      // 恢复AudioContext（如果被暂停）
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      gainNode.gain.value = 0.01; // 极低音量
      oscillator.frequency.value = 440;

      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.01);

      await new Promise(resolve => setTimeout(resolve, 50));

      audioPermissionGranted = true;
      console.log('TTS: 强制权限获取成功');
      return true;
    }
  } catch (error) {
    console.error('TTS: 强制权限获取失败:', error);
    return false;
  }
}

/**
 * 检查是否已获取音频播放权限
 */
export function hasAudioPermission(): boolean {
  return audioPermissionGranted;
}

/**
 * 检查TTS功能是否可用
 */
export function isTTSAvailable(): boolean {
  return typeof Audio !== 'undefined' && typeof fetch !== 'undefined';
}
