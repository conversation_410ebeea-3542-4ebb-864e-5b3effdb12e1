/**
 * 车辆图片缓存工具
 * 提供完整的图片缓存管理，包括容量控制、过期清理、预加载等功能
 */

// 缓存配置
const CACHE_CONFIG = {
  PREFIX: 'car_image_cache_',
  EXPIRY: 7 * 24 * 60 * 60 * 1000, // 7天过期
  MAX_SIZE: 50 * 1024 * 1024, // 50MB最大缓存
  CLEANUP_THRESHOLD: 0.8, // 80%时开始清理
  MAX_CONCURRENT_LOADS: 3, // 最大并发加载数
};

// 缓存数据结构
interface CachedImage {
  data: string; // base64 data URL
  timestamp: number;
  url: string;
  size: number; // 数据大小（字节）
  accessCount: number; // 访问次数
  lastAccess: number; // 最后访问时间
}

// 缓存统计信息
interface CacheStats {
  totalSize: number;
  itemCount: number;
  hitRate: number;
  oldestItem: number;
}

// 当前加载中的图片URL集合
const loadingImages = new Set<string>();
const loadingPromises = new Map<string, Promise<string>>();

// 图片加载状态管理
const imageLoadingStates = new Map<string, {
  isLoading: boolean;
  cachedSrc: string | null;
  error: boolean;
}>();

/**
 * 生成缓存键
 */
const getCacheKey = (url: string): string => {
  try {
    return CACHE_CONFIG.PREFIX + btoa(encodeURIComponent(url)).replace(/[+/=]/g, '');
  } catch (error) {
    // 如果btoa失败，使用简单的hash
    return CACHE_CONFIG.PREFIX + url.replace(/[^a-zA-Z0-9]/g, '_');
  }
};

/**
 * 获取图片加载状态
 */
export const getImageLoadingState = (url: string) => {
  if (!url) return { isLoading: false, cachedSrc: null, error: true };

  const state = imageLoadingStates.get(url);
  if (state) return state;

  // 检查缓存
  const cached = getImageFromCache(url);
  if (cached) {
    const newState = { isLoading: false, cachedSrc: cached, error: false };
    imageLoadingStates.set(url, newState);
    return newState;
  }

  return { isLoading: false, cachedSrc: null, error: false };
};

/**
 * 设置图片加载状态
 */
export const setImageLoadingState = (url: string, state: { isLoading: boolean; cachedSrc: string | null; error: boolean }) => {
  if (!url) return;
  imageLoadingStates.set(url, state);
};

/**
 * 获取缓存的图片
 */
export const getImageFromCache = (url: string): string | null => {
  if (!url) return null;
  
  const key = getCacheKey(url);
  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const parsedCache: CachedImage = JSON.parse(cached);
    
    // 检查是否过期
    if (Date.now() - parsedCache.timestamp > CACHE_CONFIG.EXPIRY) {
      localStorage.removeItem(key);
      return null;
    }

    // 更新访问统计
    parsedCache.accessCount++;
    parsedCache.lastAccess = Date.now();
    localStorage.setItem(key, JSON.stringify(parsedCache));

    return parsedCache.data;
  } catch (error) {
    console.warn('读取图片缓存失败:', url, error);
    return null;
  }
};

/**
 * 保存图片到缓存
 */
export const saveImageToCache = (url: string, base64Data: string): void => {
  if (!url || !base64Data) return;

  const key = getCacheKey(url);
  const size = base64Data.length;
  
  const cacheData: CachedImage = {
    data: base64Data,
    timestamp: Date.now(),
    url,
    size,
    accessCount: 1,
    lastAccess: Date.now(),
  };

  try {
    // 检查缓存容量
    const stats = getCacheStats();
    if (stats.totalSize + size > CACHE_CONFIG.MAX_SIZE * CACHE_CONFIG.CLEANUP_THRESHOLD) {
      cleanOldCache();
    }

    localStorage.setItem(key, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('保存图片缓存失败，尝试清理后重试:', url);
    
    // localStorage满了，强制清理
    cleanOldCache(true);
    
    try {
      localStorage.setItem(key, JSON.stringify(cacheData));
    } catch (retryError) {
      console.error('重试保存图片缓存仍然失败:', url, retryError);
    }
  }
};

/**
 * 将网络图片转换为base64
 */
const imageToBase64 = (url: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('无法创建canvas上下文'));
          return;
        }

        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        
        ctx.drawImage(img, 0, 0);
        
        // 转换为base64，使用JPEG格式压缩
        const base64 = canvas.toDataURL('image/jpeg', 0.8);
        resolve(base64);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error(`图片加载失败: ${url}`));
    };

    // 添加超时处理
    const timeout = setTimeout(() => {
      reject(new Error(`图片加载超时: ${url}`));
    }, 10000);

    img.onload = () => {
      clearTimeout(timeout);
      img.onload = null;
    };

    img.src = url;
  });
};

/**
 * 预加载并缓存图片（带状态管理）
 */
export const preloadAndCacheImage = async (url: string): Promise<string> => {
  if (!url) throw new Error('图片URL不能为空');

  // 检查缓存
  const cached = getImageFromCache(url);
  if (cached) {
    setImageLoadingState(url, { isLoading: false, cachedSrc: cached, error: false });
    return cached;
  }

  // 检查是否正在加载
  if (loadingPromises.has(url)) {
    return loadingPromises.get(url)!;
  }

  // 设置加载状态
  setImageLoadingState(url, { isLoading: true, cachedSrc: null, error: false });

  // 检查并发加载限制
  if (loadingImages.size >= CACHE_CONFIG.MAX_CONCURRENT_LOADS) {
    // 等待其他加载完成
    await new Promise(resolve => setTimeout(resolve, 100));
    return preloadAndCacheImage(url);
  }

  const loadPromise = (async () => {
    try {
      loadingImages.add(url);

      const base64Data = await imageToBase64(url);
      saveImageToCache(url, base64Data);

      // 更新加载状态为成功
      setImageLoadingState(url, { isLoading: false, cachedSrc: base64Data, error: false });

      return base64Data;
    } catch (error) {
      // 更新加载状态为失败
      setImageLoadingState(url, { isLoading: false, cachedSrc: null, error: true });
      throw error;
    } finally {
      loadingImages.delete(url);
      loadingPromises.delete(url);
    }
  })();

  loadingPromises.set(url, loadPromise);
  return loadPromise;
};

/**
 * 清理旧缓存
 */
export const cleanOldCache = (forceClean: boolean = false): void => {
  try {
    const allKeys = Object.keys(localStorage);
    const cacheKeys = allKeys.filter(key => key.startsWith(CACHE_CONFIG.PREFIX));
    
    if (cacheKeys.length === 0) return;

    const cacheItems: Array<{ key: string; data: CachedImage }> = [];
    
    // 收集所有缓存项
    for (const key of cacheKeys) {
      try {
        const cached = localStorage.getItem(key);
        if (cached) {
          const parsedCache: CachedImage = JSON.parse(cached);
          
          // 清理过期项
          if (Date.now() - parsedCache.timestamp > CACHE_CONFIG.EXPIRY) {
            localStorage.removeItem(key);
            continue;
          }
          
          cacheItems.push({ key, data: parsedCache });
        }
      } catch (error) {
        // 损坏的缓存项，直接删除
        localStorage.removeItem(key);
      }
    }

    // 如果需要强制清理或超过阈值，按LRU策略清理
    if (forceClean || cacheItems.length > 100) {
      // 按最后访问时间和访问次数排序（LRU + LFU混合策略）
      cacheItems.sort((a, b) => {
        const scoreA = a.data.lastAccess + (a.data.accessCount * 86400000); // 访问次数转换为时间权重
        const scoreB = b.data.lastAccess + (b.data.accessCount * 86400000);
        return scoreA - scoreB;
      });

      // 删除最旧的30%
      const deleteCount = Math.floor(cacheItems.length * 0.3);
      for (let i = 0; i < deleteCount; i++) {
        localStorage.removeItem(cacheItems[i].key);
      }
    }
  } catch (error) {
    console.error('清理缓存失败:', error);
  }
};

/**
 * 获取缓存统计信息
 */
export const getCacheStats = (): CacheStats => {
  const allKeys = Object.keys(localStorage);
  const cacheKeys = allKeys.filter(key => key.startsWith(CACHE_CONFIG.PREFIX));
  
  let totalSize = 0;
  let oldestTimestamp = Date.now();
  let totalHits = 0;
  let totalAccess = 0;

  for (const key of cacheKeys) {
    try {
      const cached = localStorage.getItem(key);
      if (cached) {
        const parsedCache: CachedImage = JSON.parse(cached);
        totalSize += parsedCache.size;
        oldestTimestamp = Math.min(oldestTimestamp, parsedCache.timestamp);
        totalHits += parsedCache.accessCount;
        totalAccess += parsedCache.accessCount;
      }
    } catch (error) {
      // 忽略损坏的缓存项
    }
  }

  return {
    totalSize,
    itemCount: cacheKeys.length,
    hitRate: totalAccess > 0 ? totalHits / totalAccess : 0,
    oldestItem: oldestTimestamp,
  };
};

/**
 * 清空所有图片缓存
 */
export const clearAllImageCache = (): void => {
  try {
    const allKeys = Object.keys(localStorage);
    const cacheKeys = allKeys.filter(key => key.startsWith(CACHE_CONFIG.PREFIX));
    
    for (const key of cacheKeys) {
      localStorage.removeItem(key);
    }
    
    console.log(`已清理 ${cacheKeys.length} 个图片缓存项`);
  } catch (error) {
    console.error('清空图片缓存失败:', error);
  }
};

/**
 * 批量预加载图片
 */
export const batchPreloadImages = async (urls: string[]): Promise<void> => {
  const validUrls = urls.filter(url => url && !getImageFromCache(url));

  if (validUrls.length === 0) return;

  // 分批处理，避免同时加载太多图片
  const batchSize = CACHE_CONFIG.MAX_CONCURRENT_LOADS;

  for (let i = 0; i < validUrls.length; i += batchSize) {
    const batch = validUrls.slice(i, i + batchSize);

    await Promise.allSettled(
      batch.map(url => preloadAndCacheImage(url).catch(error => {
        console.warn('预加载图片失败:', url, error);
      }))
    );
  }
};

/**
 * 清理图片加载状态缓存
 */
export const clearImageLoadingStates = (): void => {
  imageLoadingStates.clear();
};

/**
 * 检查图片是否正在加载
 */
export const isImageLoading = (url: string): boolean => {
  if (!url) return false;
  return loadingImages.has(url) || loadingPromises.has(url);
};
