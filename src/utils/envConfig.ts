/**
 * 环境配置文件
 * 根据运行环境自动切换API地址
 */

// 检测当前环境
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;

// 生产环境API地址配置
export const PRODUCTION_API_CONFIG = {
  // 认证API - 生产环境直接使用原始地址（支持环境变量覆盖）
  AUTH_BASE_URL: import.meta.env.VITE_AUTH_API_BASE_URL || 'https://api.st.vup.tools',

  // SenseTime API - 生产环境直接使用原始地址（支持环境变量覆盖）
  SENSETIME_BASE_URL: import.meta.env.VITE_SENSETIME_API_BASE_URL || 'https://api-gai.metishon.co',

  // TTS API - 生产环境直接使用原始地址（支持环境变量覆盖）
  TTS_BASE_URL: import.meta.env.VITE_TTS_API_BASE_URL || 'https://openspeech.bytedance.com',
};

// 开发环境API地址配置（使用代理）
export const DEVELOPMENT_API_CONFIG = {
  // 认证API - 开发环境使用代理（支持环境变量覆盖）
  AUTH_BASE_URL: import.meta.env.VITE_AUTH_API_BASE_URL || '/auth-api',

  // SenseTime API - 开发环境使用代理（支持环境变量覆盖）
  SENSETIME_BASE_URL: import.meta.env.VITE_SENSETIME_API_BASE_URL || '/api',

  // TTS API - 开发环境使用代理（支持环境变量覆盖）
  TTS_BASE_URL: import.meta.env.VITE_TTS_API_BASE_URL || '/tts-api',
};

/**
 * 获取当前环境的API配置
 */
export const getCurrentApiConfig = () => {
  if (isProduction) {
    console.log('🚀 生产环境：使用直接API地址');
    return PRODUCTION_API_CONFIG;
  } else {
    console.log('🔧 开发环境：使用代理API地址');
    return DEVELOPMENT_API_CONFIG;
  }
};

/**
 * 获取环境信息（用于调试）
 */
export const getEnvironmentInfo = () => {
  return {
    isDevelopment,
    isProduction,
    mode: import.meta.env.MODE,
    baseUrl: import.meta.env.BASE_URL,
    currentConfig: getCurrentApiConfig()
  };
};
