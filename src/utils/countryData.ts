/**
 * 国家区号数据
 * 包含常用国家的区号、名称和标识
 */

export interface CountryData {
  code: string;        // 国家代码
  name: string;        // 国家名称
  dialCode: string;    // 区号
  flag: string;        // 国旗emoji或标识
  flagUrl: string;     // 国旗图片URL
}

export const COUNTRY_DATA: CountryData[] = [
  {
    code: 'CN',
    name: '中国大陆',
    dialCode: '+86',
    flag: '🇨🇳',
    flagUrl: 'https://flagcdn.com/cn.svg'
  },
  {
    code: 'HK',
    name: '中国香港',
    dialCode: '+852',
    flag: '🇭🇰',
    flagUrl: 'https://flagcdn.com/cn.svg'
  },
  {
    code: 'TW',
    name: '中国台湾',
    dialCode: '+886',
    flag: '🇹🇼',
    flagUrl: 'https://flagcdn.com/cn.svg'
  },
  {
    code: 'MO',
    name: '中国澳门',
    dialCode: '+853',
    flag: '🇲🇴',
    flagUrl: 'https://flagcdn.com/mo.svg'
  },
  {
    code: 'US',
    name: '美国',
    dialCode: '+1',
    flag: '🇺🇸',
    flagUrl: 'https://flagcdn.com/us.svg'
  },
  {
    code: 'GB',
    name: '英国',
    dialCode: '+44',
    flag: '🇬🇧',
    flagUrl: 'https://flagcdn.com/gb.svg'
  },
  {
    code: 'JP',
    name: '日本',
    dialCode: '+81',
    flag: '🇯🇵',
    flagUrl: 'https://flagcdn.com/jp.svg'
  },
  {
    code: 'KR',
    name: '韩国',
    dialCode: '+82',
    flag: '🇰🇷',
    flagUrl: 'https://flagcdn.com/kr.svg'
  },
  {
    code: 'SG',
    name: '新加坡',
    dialCode: '+65',
    flag: '🇸🇬',
    flagUrl: 'https://flagcdn.com/sg.svg'
  },
  {
    code: 'MY',
    name: '马来西亚',
    dialCode: '+60',
    flag: '🇲🇾',
    flagUrl: 'https://flagcdn.com/my.svg'
  },
  {
    code: 'TH',
    name: '泰国',
    dialCode: '+66',
    flag: '🇹🇭',
    flagUrl: 'https://flagcdn.com/th.svg'
  },
  {
    code: 'AU',
    name: '澳大利亚',
    dialCode: '+61',
    flag: '🇦🇺',
    flagUrl: 'https://flagcdn.com/au.svg'
  },
  {
    code: 'CA',
    name: '加拿大',
    dialCode: '+1',
    flag: '🇨🇦',
    flagUrl: 'https://flagcdn.com/ca.svg'
  },
  {
    code: 'DE',
    name: '德国',
    dialCode: '+49',
    flag: '🇩🇪',
    flagUrl: 'https://flagcdn.com/de.svg'
  },
  {
    code: 'FR',
    name: '法国',
    dialCode: '+33',
    flag: '🇫🇷',
    flagUrl: 'https://flagcdn.com/fr.svg'
  },
  {
    code: 'IT',
    name: '意大利',
    dialCode: '+39',
    flag: '🇮🇹',
    flagUrl: 'https://flagcdn.com/it.svg'
  },
  {
    code: 'ES',
    name: '西班牙',
    dialCode: '+34',
    flag: '🇪🇸',
    flagUrl: 'https://flagcdn.com/es.svg'
  },
  {
    code: 'RU',
    name: '俄罗斯',
    dialCode: '+7',
    flag: '🇷🇺',
    flagUrl: 'https://flagcdn.com/ru.svg'
  },
  {
    code: 'IN',
    name: '印度',
    dialCode: '+91',
    flag: '🇮🇳',
    flagUrl: 'https://flagcdn.com/in.svg'
  },
  {
    code: 'BR',
    name: '巴西',
    dialCode: '+55',
    flag: '🇧🇷',
    flagUrl: 'https://flagcdn.com/br.svg'
  }
];

/**
 * 根据区号查找国家信息
 */
export const findCountryByDialCode = (dialCode: string): CountryData | undefined => {
  return COUNTRY_DATA.find(country => country.dialCode === dialCode);
};

/**
 * 根据国家代码查找国家信息
 */
export const findCountryByCode = (code: string): CountryData | undefined => {
  return COUNTRY_DATA.find(country => country.code === code);
};

/**
 * 搜索国家（支持名称和区号搜索）
 */
export const searchCountries = (query: string): CountryData[] => {
  if (!query.trim()) {
    return COUNTRY_DATA;
  }
  
  const lowerQuery = query.toLowerCase();
  return COUNTRY_DATA.filter(country => 
    country.name.toLowerCase().includes(lowerQuery) ||
    country.dialCode.includes(query) ||
    country.code.toLowerCase().includes(lowerQuery)
  );
};

/**
 * 获取默认国家（中国）
 */
export const getDefaultCountry = (): CountryData => {
  return COUNTRY_DATA[0]; // 中国 +86
};
