/**
 * 语音录制工具类
 * 处理语音录制、权限管理和音频格式转换
 */

export interface VoiceRecorderOptions {
  maxDuration?: number; // 最大录制时长（毫秒）
  sampleRate?: number;  // 采样率
}

export interface RecordingResult {
  originalBlob: Blob;    // 原始格式（用于调试下载）
  wavBlob: Blob;         // 标准WAV格式（用于ASR）
  duration: number;
}

export class VoiceRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioChunks: Blob[] = [];
  private startTime: number = 0;
  private options: VoiceRecorderOptions;
  private actualMimeType: string = ''; // 保存实际使用的MIME类型

  constructor(options: VoiceRecorderOptions = {}) {
    this.options = {
      maxDuration: 60000, // 默认60秒
      sampleRate: 16000,  // 默认16kHz
      ...options
    };
  }

  /**
   * 检查浏览器是否支持录音功能
   */
  static isSupported(): boolean {
    console.log('VoiceRecorder: 检查浏览器支持情况');

    // 检查基础API支持
    const hasNavigator = typeof navigator !== 'undefined';
    const hasMediaDevices = hasNavigator && !!navigator.mediaDevices;
    const hasGetUserMedia = hasMediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function';
    const hasMediaRecorder = typeof window !== 'undefined' && !!window.MediaRecorder;
    const isSecureContext = typeof window !== 'undefined' && (window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost');

    console.log('VoiceRecorder: 支持检查结果', {
      hasNavigator,
      hasMediaDevices,
      hasGetUserMedia,
      hasMediaRecorder,
      isSecureContext,
      userAgent: navigator?.userAgent || 'unknown'
    });

    if (!hasNavigator) {
      console.warn('VoiceRecorder: navigator 不可用');
      return false;
    }

    if (!hasMediaDevices) {
      console.warn('VoiceRecorder: navigator.mediaDevices 不可用');
      return false;
    }

    if (!hasGetUserMedia) {
      console.warn('VoiceRecorder: navigator.mediaDevices.getUserMedia 不可用');
      return false;
    }

    if (!hasMediaRecorder) {
      console.warn('VoiceRecorder: MediaRecorder 不可用');
      return false;
    }

    // 移除HTTPS检查 - 允许在HTTP环境下尝试
    if (!isSecureContext) {
      console.warn('VoiceRecorder: 非安全上下文 (HTTP)，某些浏览器可能会阻止语音录制');
      // 不返回false，让浏览器自己决定是否允许
    }

    console.log('VoiceRecorder: 浏览器支持录音功能');
    return true;
  }

  /**
   * 检查麦克风权限状态（Safari优化版本）
   */
  async checkPermission(): Promise<'granted' | 'denied' | 'prompt'> {
    try {
      // Safari特殊处理
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      if (!navigator.permissions) {
        console.log('VoiceRecorder: navigator.permissions 不可用，返回 prompt');
        return 'prompt';
      }

      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      console.log('VoiceRecorder: 权限状态', permission.state);

      // Safari中，即使权限已授权，有时也会返回'prompt'
      // 我们需要通过实际尝试获取媒体流来验证权限
      if (isSafari && permission.state === 'prompt') {
        console.log('VoiceRecorder: Safari检测到prompt状态，尝试验证实际权限');
        try {
          // 尝试获取媒体流来验证权限
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: false,
              noiseSuppression: false,
              autoGainControl: false
            }
          });
          // 立即停止流
          stream.getTracks().forEach(track => track.stop());
          console.log('VoiceRecorder: Safari权限验证成功，实际状态为granted');
          return 'granted';
        } catch (error) {
          console.log('VoiceRecorder: Safari权限验证失败:', error);
          if (error instanceof Error && error.name === 'NotAllowedError') {
            return 'denied';
          }
          return 'prompt';
        }
      }

      return permission.state;
    } catch (error) {
      console.warn('VoiceRecorder: 无法检查麦克风权限:', error);
      return 'prompt';
    }
  }

  /**
   * 预先请求麦克风权限（不开始录音）
   */
  async requestPermission(): Promise<boolean> {
    console.log('VoiceRecorder: 请求麦克风权限');

    if (!VoiceRecorder.isSupported()) {
      throw new Error('当前浏览器不支持录音功能');
    }

    try {
      // 尝试获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.options.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      // 立即停止流，我们只是为了获取权限
      stream.getTracks().forEach(track => track.stop());

      console.log('VoiceRecorder: 麦克风权限获取成功');
      return true;
    } catch (error) {
      console.error('VoiceRecorder: 麦克风权限获取失败:', error);

      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          throw new Error('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
        } else if (error.name === 'NotFoundError') {
          throw new Error('未找到麦克风设备');
        } else if (error.name === 'NotSupportedError') {
          throw new Error('浏览器不支持录音功能');
        }
      }

      throw new Error('无法获取麦克风权限');
    }
  }

  /**
   * 请求麦克风权限并初始化录音（移动端优化版本）
   */
  async initialize(): Promise<void> {
    console.log('VoiceRecorder: 开始初始化录音功能');

    if (!VoiceRecorder.isSupported()) {
      throw new Error('当前浏览器不支持录音功能');
    }

    try {
      console.log('VoiceRecorder: 请求麦克风权限...');

      // 移动端优化的音频约束
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

      const audioConstraints = {
        audio: isMobile ? (isIOS ? {
          // iOS Safari 使用最简单的约束
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
        } : {
          // Android 移动端约束
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }) : {
          // 桌面端使用完整约束
          sampleRate: this.options.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      };

      console.log('VoiceRecorder: 音频约束:', audioConstraints);
      console.log('VoiceRecorder: 设备信息:', {
        userAgent: navigator.userAgent,
        isMobile,
        isIOS,
        platform: navigator.platform
      });

      // 请求麦克风权限
      try {
        this.audioStream = await navigator.mediaDevices.getUserMedia(audioConstraints);
        console.log('VoiceRecorder: 麦克风权限获取成功');
      } catch (initialError) {
        console.warn('VoiceRecorder: 初始音频约束失败，尝试基本约束:', initialError);

        // iOS Safari 回退方案：使用最基本的音频约束
        if (isIOS) {
          try {
            const basicConstraints = { audio: true };
            console.log('VoiceRecorder: 尝试基本音频约束:', basicConstraints);
            this.audioStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
            console.log('VoiceRecorder: 基本约束获取成功');
          } catch (fallbackError) {
            console.error('VoiceRecorder: 基本约束也失败:', fallbackError);
            throw fallbackError;
          }
        } else {
          throw initialError;
        }
      }

      // 创建MediaRecorder实例
      const mimeType = this.getSupportedMimeType();
      console.log('VoiceRecorder: 使用MIME类型:', mimeType);

      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: mimeType || undefined // 如果没有支持的类型，使用默认
      });

      // 保存实际使用的MIME类型
      this.actualMimeType = mimeType || 'audio/webm'; // 默认为webm
      console.log('VoiceRecorder: 保存实际MIME类型:', this.actualMimeType);

      this.setupMediaRecorderEvents();
      console.log('VoiceRecorder: 录音器初始化完成');
    } catch (error) {
      console.error('VoiceRecorder: 初始化失败:', error);
      this.cleanup();

      if (error instanceof Error) {
        console.error('VoiceRecorder: 错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });

        if (error.name === 'NotAllowedError') {
          const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          if (isMobile) {
            throw new Error('麦克风权限被拒绝。请在Safari设置中允许此网站访问麦克风，或在iOS设置→Safari→麦克风中允许访问。');
          } else {
            throw new Error('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
          }
        } else if (error.name === 'NotFoundError') {
          throw new Error('未找到麦克风设备');
        } else if (error.name === 'NotSupportedError') {
          throw new Error('浏览器不支持录音功能');
        } else if (error.name === 'NotReadableError') {
          throw new Error('麦克风设备无法使用，可能被其他应用占用');
        } else if (error.name === 'OverconstrainedError') {
          throw new Error('音频设备不支持请求的配置，请尝试使用其他设备');
        }
      }

      throw new Error('初始化录音功能失败');
    }
  }

  /**
   * 获取支持的音频MIME类型
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return ''; // 使用默认类型
  }

  /**
   * 设置MediaRecorder事件监听
   */
  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };

    this.mediaRecorder.onerror = (event) => {
      console.error('录音错误:', event);
      this.cleanup();
    };
  }

  /**
   * 开始录音（移动端优化版本）
   */
  async startRecording(): Promise<void> {
    try {
      console.log('VoiceRecorder: 开始录音，检测环境...');

      // 检测是否为移动设备
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      console.log('VoiceRecorder: 是否为移动设备:', isMobile);

      // 如果录制器不存在，先初始化
      if (!this.mediaRecorder) {
        console.log('VoiceRecorder: 录制器不存在，开始初始化');
        await this.initialize();
      }

      // 检查录制器状态
      if (!this.mediaRecorder) {
        throw new Error('录音器初始化失败，请检查麦克风权限');
      }

      if (this.mediaRecorder.state === 'recording') {
        throw new Error('录音器正在使用中');
      }

      if (this.mediaRecorder.state !== 'inactive') {
        throw new Error(`录音器状态异常: ${this.mediaRecorder.state}`);
      }

      this.audioChunks = [];
      this.startTime = Date.now();

      // 开始录音
      console.log('VoiceRecorder: 开始录制音频');
      this.mediaRecorder.start(100); // 每100ms收集一次数据
      console.log('VoiceRecorder: 录制已开始，状态:', this.mediaRecorder.state);

      // 移除自动停止机制，让用户完全控制录音
      // 注释掉原来的自动停止逻辑，避免意外停止录音
      /*
      if (this.options.maxDuration) {
        setTimeout(() => {
          if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            console.log('VoiceRecorder: 达到最大录制时长，自动停止');
            this.stopRecording();
          }
        }, this.options.maxDuration);
      }
      */
    } catch (error) {
      console.error('VoiceRecorder: 开始录音失败:', error);

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          console.error('VoiceRecorder: 麦克风权限被拒绝');
          throw new Error('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
        } else if (error.name === 'NotFoundError') {
          console.error('VoiceRecorder: 未找到麦克风设备');
          throw new Error('未找到麦克风设备');
        } else if (error.name === 'NotReadableError') {
          console.error('VoiceRecorder: 麦克风设备无法使用');
          throw new Error('麦克风设备无法使用，可能被其他应用占用');
        } else if (error.name === 'AbortError') {
          console.error('VoiceRecorder: 用户取消了权限请求');
          throw new Error('用户取消了权限请求');
        }
      }

      // 清理资源
      this.cleanup();
      throw error;
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<RecordingResult> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
        reject(new Error('当前没有进行录音'));
        return;
      }

      this.mediaRecorder.onstop = async () => {
        try {
          const duration = Date.now() - this.startTime;
          // 使用实际的MIME类型创建原始Blob
          const originalBlob = new Blob(this.audioChunks, { type: this.actualMimeType });

          console.log('VoiceRecorder: 录音完成，开始格式转换', {
            duration: duration + 'ms',
            mimeType: this.actualMimeType,
            originalSize: originalBlob.size + ' bytes'
          });

          // 转换为标准WAV格式，如果失败则使用原始格式作为fallback
          let wavBlob: Blob;
          try {
            wavBlob = await this.convertToWav(originalBlob);
          } catch (convertError) {
            console.warn('VoiceRecorder: WAV转换失败，使用原始格式作为fallback:', convertError);
            // 如果转换失败，创建一个标记为WAV的原始Blob（用于ASR尝试）
            wavBlob = new Blob([originalBlob], { type: 'audio/wav' });
          }

          resolve({
            originalBlob,
            wavBlob,
            duration
          });
        } catch (error) {
          console.error('VoiceRecorder: 录音处理失败:', error);
          reject(error);
        }
      };

      this.mediaRecorder.stop();
    });
  }

  /**
   * 取消录音
   */
  cancelRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }
    this.audioChunks = [];
    this.cleanup();
  }

  /**
   * 获取当前录音状态
   */
  getState(): string {
    return this.mediaRecorder?.state || 'inactive';
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }
    
    if (this.mediaRecorder) {
      this.mediaRecorder = null;
    }
    
    this.audioChunks = [];
  }

  /**
   * 检查是否正在录音
   */
  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording';
  }

  /**
   * 获取录音时长（毫秒）
   */
  getRecordingDuration(): number {
    if (!this.isRecording()) return 0;
    return Date.now() - this.startTime;
  }

  /**
   * 获取实际使用的MIME类型
   */
  getActualMimeType(): string {
    return this.actualMimeType;
  }

  /**
   * 根据MIME类型获取文件扩展名
   */
  getFileExtension(): string {
    if (this.actualMimeType.includes('webm')) {
      return 'webm';
    } else if (this.actualMimeType.includes('mp4')) {
      return 'mp4';
    } else if (this.actualMimeType.includes('wav')) {
      return 'wav';
    } else {
      return 'webm'; // 默认扩展名
    }
  }

  /**
   * 将音频转换为标准WAV格式
   * @param audioBlob 原始音频Blob
   * @returns Promise<Blob> 标准WAV格式的Blob
   */
  private async convertToWav(audioBlob: Blob): Promise<Blob> {
    try {
      console.log('VoiceRecorder: 开始转换音频为WAV格式...', {
        originalSize: audioBlob.size,
        mimeType: audioBlob.type,
        userAgent: navigator.userAgent
      });

      // 检查AudioContext支持
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContextClass) {
        console.error('VoiceRecorder: AudioContext不支持');
        throw new Error('浏览器不支持AudioContext，无法进行音频格式转换');
      }

      // 创建AudioContext
      const audioContext = new AudioContextClass();
      console.log('VoiceRecorder: AudioContext创建成功', {
        sampleRate: audioContext.sampleRate,
        state: audioContext.state
      });

      // 将Blob转换为ArrayBuffer
      const arrayBuffer = await audioBlob.arrayBuffer();
      console.log('VoiceRecorder: ArrayBuffer转换完成，大小:', arrayBuffer.byteLength);

      // 解码音频数据 - 添加更好的错误处理
      let audioBuffer: AudioBuffer;
      try {
        audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      } catch (decodeError) {
        console.error('VoiceRecorder: 音频解码失败，尝试使用回调方式:', decodeError);

        // 尝试使用旧版回调方式（移动端兼容性）
        audioBuffer = await new Promise<AudioBuffer>((resolve, reject) => {
          audioContext.decodeAudioData(
            arrayBuffer,
            (buffer) => resolve(buffer),
            (error) => reject(error)
          );
        });
      }

      console.log('VoiceRecorder: 音频解码成功', {
        sampleRate: audioBuffer.sampleRate,
        channels: audioBuffer.numberOfChannels,
        duration: audioBuffer.duration + 's',
        length: audioBuffer.length
      });

      // 获取音频数据（转换为单声道）
      const channelData = audioBuffer.getChannelData(0);

      // 重采样到16kHz（如果需要）
      const targetSampleRate = 16000;
      const resampledData = this.resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);

      // 转换为16位PCM
      const pcmData = this.floatTo16BitPCM(resampledData);

      // 生成WAV文件
      const wavBlob = this.createWavBlob(pcmData, targetSampleRate);

      // 关闭AudioContext以释放资源
      if (audioContext.state !== 'closed') {
        await audioContext.close();
      }

      console.log('VoiceRecorder: WAV转换完成', {
        originalSize: audioBlob.size,
        wavSize: wavBlob.size,
        sampleRate: targetSampleRate,
        compression: ((audioBlob.size - wavBlob.size) / audioBlob.size * 100).toFixed(1) + '%'
      });

      return wavBlob;
    } catch (error) {
      console.error('VoiceRecorder: WAV转换失败，详细错误信息:', {
        error: error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        originalBlobSize: audioBlob.size,
        originalBlobType: audioBlob.type,
        userAgent: navigator.userAgent
      });

      // 提供更具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('decodeAudioData')) {
          throw new Error('音频解码失败，可能是不支持的音频格式');
        } else if (error.message.includes('AudioContext')) {
          throw new Error('浏览器不支持音频处理功能');
        }
      }

      throw new Error(`音频格式转换失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 重采样音频数据
   */
  private resampleAudio(inputData: Float32Array, inputSampleRate: number, outputSampleRate: number): Float32Array {
    if (inputSampleRate === outputSampleRate) {
      return inputData;
    }

    const ratio = inputSampleRate / outputSampleRate;
    const outputLength = Math.floor(inputData.length / ratio);
    const outputData = new Float32Array(outputLength);

    for (let i = 0; i < outputLength; i++) {
      const inputIndex = i * ratio;
      const inputIndexFloor = Math.floor(inputIndex);
      const inputIndexCeil = Math.min(inputIndexFloor + 1, inputData.length - 1);
      const fraction = inputIndex - inputIndexFloor;

      // 线性插值
      outputData[i] = inputData[inputIndexFloor] * (1 - fraction) + inputData[inputIndexCeil] * fraction;
    }

    return outputData;
  }

  /**
   * 将浮点数音频数据转换为16位PCM
   */
  private floatTo16BitPCM(input: Float32Array): ArrayBuffer {
    const buffer = new ArrayBuffer(input.length * 2);
    const view = new DataView(buffer);

    for (let i = 0; i < input.length; i++) {
      // 限制范围到[-1, 1]
      const sample = Math.max(-1, Math.min(1, input[i]));
      // 转换为16位整数
      const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(i * 2, intSample, true); // little-endian
    }

    return buffer;
  }

  /**
   * 创建WAV格式的Blob
   */
  private createWavBlob(pcmData: ArrayBuffer, sampleRate: number): Blob {
    const dataLength = pcmData.byteLength;
    const buffer = new ArrayBuffer(44 + dataLength);
    const view = new DataView(buffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    // RIFF标识符
    writeString(0, 'RIFF');
    // 文件大小
    view.setUint32(4, 36 + dataLength, true);
    // WAVE标识符
    writeString(8, 'WAVE');
    // fmt子块
    writeString(12, 'fmt ');
    // fmt子块大小
    view.setUint32(16, 16, true);
    // 音频格式（PCM = 1）
    view.setUint16(20, 1, true);
    // 声道数
    view.setUint16(22, 1, true);
    // 采样率
    view.setUint32(24, sampleRate, true);
    // 字节率
    view.setUint32(28, sampleRate * 2, true);
    // 块对齐
    view.setUint16(32, 2, true);
    // 位深度
    view.setUint16(34, 16, true);
    // data子块
    writeString(36, 'data');
    // data子块大小
    view.setUint32(40, dataLength, true);

    // 复制PCM数据
    const pcmView = new Uint8Array(pcmData);
    const wavView = new Uint8Array(buffer);
    wavView.set(pcmView, 44);

    return new Blob([buffer], { type: 'audio/wav' });
  }
}

/**
 * 创建语音录制器实例
 */
export const createVoiceRecorder = (options?: VoiceRecorderOptions): VoiceRecorder => {
  return new VoiceRecorder(options);
};
