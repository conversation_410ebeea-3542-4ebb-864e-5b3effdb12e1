/**
 * 聊天API工具函数
 * 处理与新聊天API的交互，包括流式响应和话题标题获取
 */

import { getToken } from './jwtUtils';
import { buildChatApiUrl, CHAT_ENDPOINTS } from './apiConfig';

// API基础配置 - 使用新的聊天API端点
// 聊天API现在使用与认证API相同的基础URL (http://0.0.0.0:8000)
const LEGACY_API_BASE_URL = '/api'; // 保留用于其他端点的旧API基础URL

// ==================== 聊天历史相关接口 ====================

/**
 * 服务器消息数据结构
 */
export interface ServerMessage {
  id: number;
  message_id: string;
  conversation_id: string;
  user_id: number;
  message_type: 'user' | 'assistant';
  content: string;
  is_complete: boolean;
  created_at: string;
  updated_at: string | null;
}

/**
 * 服务器推荐数据结构
 */
export interface ServerRecommendation {
  id: string;
  conversation_id: string;
  message_id: string;
  task_type: string;
  recommendation_data: any;
  slots_data: Record<string, any>;
  created_at: string;
}

/**
 * 服务器对比数据结构
 */
export interface ServerComparison {
  id: string;
  conversation_id: string;
  message_id: string;
  comparison_data: any[];
  created_at: string;
}

/**
 * 聊天历史项数据结构
 */
export interface ChatHistoryItem {
  message: ServerMessage;
  recommendations: ServerRecommendation[];
  comparisons: ServerComparison[];
}

/**
 * 分页信息
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  current_count: number;
  has_more: boolean;
}

/**
 * 会话摘要信息
 */
export interface ConversationSummary {
  conversation_id: string;
  title: string;
  message_count: number;
  recommendation_count: number;
  comparison_count: number;
  last_message_at: string;
  last_message_type: 'user' | 'assistant';
  created_at: string;
  updated_at: string;
}

/**
 * 聊天历史API响应接口
 */
export interface ChatHistoryResponse {
  success: boolean;
  message: string;
  data: {
    history: ChatHistoryItem[];
    pagination: PaginationInfo;
    summary: ConversationSummary;
  };
  timestamp: string;
  request_id: string;
}

// 聊天响应数据结构
export interface ChatResponse {
  data: {
    user_id: string;
    message_id: string;
    conversation_id: string;
    choices: Array<{
      delta: string;
    }>;
    usage: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
  status: {
    code: number;
    message: string;
  };
}

// 话题标题响应数据结构（新API格式）
export interface TopicSummaryResponse {
  success: boolean;
  message: string;
  data: {
    conversation_id: string;
    title: string;
  };
  timestamp: string;
  request_id: string;
}

// ASR响应数据结构（新API格式）
export interface ASRResponse {
  success: boolean;
  message: string;
  data: {
    data: {
      data: {
        user_id: string;
        request_id: string;
        text: string;
      };
      status: {
        code: number;
        message: string;
      };
    };
  };
  timestamp: string;
  request_id: string;
}

// 话题推荐响应数据结构
export interface RecommendTopicsResponse {
  status: string;
  data: string;
}

// 车辆图片数据结构
export interface VehiclePicture {
  id: number;
  vehicle_id: number;
  pic_name: string;
  pic_type: number;
  pic_url: string;
}

// 车辆图片响应数据结构
export interface VehiclePicturesResponse {
  status: string;
  data: VehiclePicture[];
}

// 车辆损伤数据结构
export interface VehicleDamage {
  id: number;
  vehicle_id: number;
  category_name: string;
  damage_color: string;
  damage_name: string | null;
  item_name: string;
  level_name: string | null;
  url: string | null;
}

// 车辆损伤响应数据结构
export interface VehicleDamageResponse {
  status: string;
  data: VehicleDamage[];
}

// 聊天请求参数（新API格式）
export interface ChatRequest {
  conversation_id: string;
  message: string;
}

/**
 * 发送聊天消息并处理流式响应
 * @param request 聊天请求参数
 * @param onChunk 处理每个响应块的回调函数
 * @param onComplete 完成时的回调函数
 * @param onError 错误处理回调函数
 */
export const sendChatMessage = async (
  request: ChatRequest,
  onChunk: (delta: string) => void,
  onComplete: () => void,
  onError: (error: string) => void
): Promise<void> => {
  try {
    const token = getToken();
    if (!token) {
      onError('未找到认证令牌，请重新登录');
      return;
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.COMPLETIONS), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true });
        
        // 按行分割数据
        const lines = buffer.split('\n');
        
        // 保留最后一行（可能不完整）
        buffer = lines.pop() || '';
        
        // 处理完整的行
        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine) {
            // 跳过 [DONE] 标记
            if (trimmedLine === 'data: [DONE]') {
              continue;
            }

            // 处理以 "data: " 开头的行
            if (trimmedLine.startsWith('data: ')) {
              const jsonStr = trimmedLine.substring(6); // 移除 "data: " 前缀
              try {
                const data: any = JSON.parse(jsonStr);

                // 检查响应状态 - 兼容不同的响应格式
                if (data.status && data.status.code !== undefined) {
                  // 有status字段的响应
                  if (data.status.code === 0 && data.data && data.data.choices && data.data.choices.length > 0) {
                    const delta = data.data.choices[0].delta;
                    if (delta) {
                      onChunk(delta);
                    }
                  } else if (data.status.code !== 0) {
                    onError(`API错误: ${data.status.message}`);
                    return;
                  }
                } else if (data.data && data.data.choices && data.data.choices.length > 0) {
                  // 没有status字段的响应，直接处理data
                  const delta = data.data.choices[0].delta;
                  if (delta) {
                    onChunk(delta);
                  }
                } else {
                  // 其他格式的响应，记录但不处理
                  console.log('收到未知格式的响应:', data);
                }
              } catch (parseError) {
                console.warn('解析响应数据失败:', parseError, '原始数据:', jsonStr.substring(0, 200) + '...');
              }
            }
          }
        }
      }
      
      // 处理缓冲区中剩余的数据
      if (buffer.trim()) {
        const trimmedBuffer = buffer.trim();
        if (trimmedBuffer.startsWith('data: ') && trimmedBuffer !== 'data: [DONE]') {
          const jsonStr = trimmedBuffer.substring(6);
          try {
            const data: any = JSON.parse(jsonStr);
            // 兼容不同的响应格式
            if (data.status && data.status.code === 0 && data.data && data.data.choices && data.data.choices.length > 0) {
              const delta = data.data.choices[0].delta;
              if (delta) {
                onChunk(delta);
              }
            } else if (data.data && data.data.choices && data.data.choices.length > 0) {
              const delta = data.data.choices[0].delta;
              if (delta) {
                onChunk(delta);
              }
            }
          } catch (parseError) {
            console.warn('解析最后的响应数据失败:', parseError);
          }
        }
      }
      
      onComplete();
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error('聊天API调用失败:', error);
    onError(error instanceof Error ? error.message : '网络错误，请重试');
  }
};

/**
 * 获取对话话题标题（新API）
 * @param conversationId 会话ID
 * @param message 用户的第一条消息
 * @returns Promise<string> 话题标题
 */
export const getSummaryTopic = async (conversationId: string, message: string): Promise<string> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.SUMMARY_TOPICS), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        conversation_id: conversationId,
        message: message
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: TopicSummaryResponse = await response.json();

    if (data.success) {
      console.log('✅ 话题标题生成成功:', data.data.title);
      return data.data.title;
    } else {
      throw new Error(data.message || '获取话题标题失败');
    }
  } catch (error) {
    console.error('获取话题标题失败:', error);
    // 返回默认标题
    return '新的对话';
  }
};

/**
 * 生成UUID
 * @returns string UUID
 */
export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * 从JWT token中提取用户ID，如果失败则生成一个
 * @returns string 用户ID
 */
export const getUserId = (): string => {
  try {
    const token = getToken();
    if (token) {
      // 尝试从token中解析用户信息
      const payload = JSON.parse(atob(token.split('.')[1]));
      if (payload.sub) {
        return payload.sub;
      }
      if (payload.iss) {
        return payload.iss;
      }
    }
  } catch (error) {
    console.warn('从token中提取用户ID失败:', error);
  }

  // 如果无法从token中获取，则生成一个固定的用户ID
  let userId = localStorage.getItem('chat_user_id');
  if (!userId) {
    userId = generateUUID();
    localStorage.setItem('chat_user_id', userId);
  }
  return userId;
};

/**
 * 发送语音文件到ASR接口进行语音识别
 * @param audioBlob 音频文件Blob
 * @param abortController 可选的取消控制器
 * @returns Promise<string> 识别出的文本
 */
export const sendVoiceToASR = async (audioBlob: Blob, abortController?: AbortController): Promise<string> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 创建FormData（新API格式，不需要user_id）
    const formData = new FormData();
    formData.append('input_file_type', 'wav');
    formData.append('file', audioBlob, 'recording.wav');

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.ASR), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
      signal: abortController?.signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ASRResponse = await response.json();

    if (data.success && data.data?.data?.status?.code === 200) {
      console.log('✅ 语音识别成功:', data.data.data.data.text);
      return data.data.data.data.text;
    } else {
      const errorMessage = data.data?.data?.status?.message || data.message || '语音识别失败';
      throw new Error(`ASR API错误: ${errorMessage}`);
    }
  } catch (error) {
    console.error('语音识别失败:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('语音识别服务暂时不可用，请稍后重试');
  }
};

/**
 * 获取推荐话题
 * @param message 用户的消息内容
 * @returns Promise<string[]> 推荐话题列表
 */
export const getRecommendTopics = async (message: string): Promise<string[]> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    const response = await fetch(`${LEGACY_API_BASE_URL}${CHAT_ENDPOINTS.RECOMMEND_TOPICS}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ message }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: RecommendTopicsResponse = await response.json();

    if (data.status === 'success') {
      // 解析返回的话题数据，按行分割并去掉序号前缀
      return data.data
        .split('\n')
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .filter(topic => topic.length > 0);
    } else {
      throw new Error('获取推荐话题失败');
    }
  } catch (error) {
    console.error('获取推荐话题失败:', error);
    // 返回空数组，不影响正常聊天流程
    return [];
  }
};

/**
 * 获取车辆图片信息（新API）
 * @param vehicleId 车辆ID（UUID格式）
 * @returns Promise<VehiclePicture[]> 车辆图片列表
 */
export const getVehiclePictures = async (vehicleId: string): Promise<VehiclePicture[]> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    const response = await fetch(buildChatApiUrl(`${CHAT_ENDPOINTS.VEHICLE_PICTURES}/${vehicleId}/pictures`), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: VehiclePicturesResponse = await response.json();

    if (data.status === 'success') {
      console.log('✅ 车辆图片获取成功，数量:', data.data.length);
      return data.data;
    } else {
      throw new Error('获取车辆图片失败');
    }
  } catch (error) {
    console.error('获取车辆图片失败:', error);
    throw error;
  }
};

/**
 * 获取车辆损伤信息（新API）
 * @param vehicleId 车辆ID（UUID格式）
 * @returns Promise<VehicleDamage[]> 车辆损伤信息列表
 */
export const getVehicleDamage = async (vehicleId: string): Promise<VehicleDamage[]> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    const response = await fetch(buildChatApiUrl(`${CHAT_ENDPOINTS.VEHICLE_DAMAGE}/${vehicleId}/damage`), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: VehicleDamageResponse = await response.json();

    if (data.status === 'success') {
      console.log('✅ 车辆损伤信息获取成功，数量:', data.data.length);
      return data.data;
    } else {
      throw new Error('获取车辆损伤信息失败');
    }
  } catch (error) {
    console.error('获取车辆损伤信息失败:', error);
    throw error;
  }
};

/**
 * 获取聊天历史记录
 * @param conversationId 会话ID
 * @param page 页码，默认为1
 * @param limit 每页数量，默认为50
 * @param useCache 是否使用缓存，默认为false
 * @returns Promise<ChatHistoryResponse> 聊天历史响应
 */
export const getChatHistoryAPI = async (
  conversationId: string,
  page: number = 1,
  limit: number = 50,
  useCache: boolean = false
): Promise<ChatHistoryResponse> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 构建查询参数
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      use_cache: useCache.toString()
    });

    const url = `${buildChatApiUrl(CHAT_ENDPOINTS.CHAT_HISTORY)}/${conversationId}?${queryParams}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ChatHistoryResponse = await response.json();

    if (!data.success) {
      throw new Error(data.message || '获取聊天历史失败');
    }

    console.log(`✅ 成功获取会话 ${conversationId} 的历史记录，页码: ${page}，数量: ${data.data.history.length}`);
    return data;

  } catch (error) {
    console.error('获取聊天历史失败:', error);
    throw error instanceof Error ? error : new Error('获取聊天历史失败');
  }
};

/**
 * 历史推车记录API响应接口
 */
export interface RecentRecommendationsResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    conversation_id: string;
    message_id: string;
    task_type: string;
    recommendation_data: any;
    created_at: string;
  }[];
  timestamp: string;
  request_id: string;
}

/**
 * 删除对话响应接口
 */
export interface DeleteConversationResponse {
  success: boolean;
  message: string;
  data: {
    conversation_id: string;
    title: string;
    deleted_at: string;
    message: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 获取历史推车记录
 * @param limit 限制数量，默认为10
 * @returns Promise<RecentRecommendationsResponse> 历史推车记录响应
 */
export const getRecentRecommendationsAPI = async (
  limit: number = 10
): Promise<RecentRecommendationsResponse> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 构建查询参数
    const queryParams = new URLSearchParams({
      limit: limit.toString()
    });

    const url = `${buildChatApiUrl(CHAT_ENDPOINTS.RECENT_RECOMMENDATIONS)}?${queryParams}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: RecentRecommendationsResponse = await response.json();

    if (!data.success) {
      throw new Error(data.message || '获取历史推车记录失败');
    }

    console.log('✅ 历史推车记录获取成功，数量:', data.data.length);
    return data;

  } catch (error) {
    console.error('获取历史推车记录失败:', error);
    throw error instanceof Error ? error : new Error('获取历史推车记录失败');
  }
};

/**
 * 删除对话会话
 * @param conversationId 会话ID
 * @returns Promise<DeleteConversationResponse> 删除对话响应
 */
export const deleteConversationAPI = async (
  conversationId: string
): Promise<DeleteConversationResponse> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.DELETE_CONVERSATION), {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        conversation_id: conversationId
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: DeleteConversationResponse = await response.json();

    if (!data.success) {
      throw new Error(data.message || '删除对话失败');
    }

    console.log(`✅ 成功删除对话: ${conversationId}`, data.data);
    return data;

  } catch (error) {
    console.error('删除对话失败:', error);
    throw error instanceof Error ? error : new Error('删除对话失败');
  }
};
