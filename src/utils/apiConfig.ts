/**
 * API配置文件
 * 统一管理所有API的基础配置
 * 自动根据环境切换API地址
 */

import { getCurrentApiConfig, getEnvironmentInfo } from './envConfig';

// 获取当前环境的API配置
const currentApiConfig = getCurrentApiConfig();

// API基础URL配置 - 自动根据环境切换
export const API_CONFIG = {
  // 认证API基础URL - 开发环境使用代理，生产环境使用直接地址
  AUTH_BASE_URL: currentApiConfig.AUTH_BASE_URL,

  // SenseTime API基础URL - 开发环境使用代理，生产环境使用直接地址
  SENSETIME_BASE_URL: currentApiConfig.SENSETIME_BASE_URL,

  // TTS API基础URL - 开发环境使用代理，生产环境使用直接地址
  TTS_BASE_URL: currentApiConfig.TTS_BASE_URL,

  // 头像显示基础URL - 始终使用直接地址
  AVATAR_BASE_URL: 'https://api.st.vup.tools/api/v1/user/avatar',

  // 超时配置
  TIMEOUT: 30000, // 30秒
};

// 在控制台输出当前环境信息（仅在开发环境）
if (import.meta.env.DEV) {
  console.log('🔧 API配置信息:', getEnvironmentInfo());
}

// 认证API端点
export const AUTH_ENDPOINTS = {
  SEND_CODE: '/api/v1/auth/send-code',
  LOGIN: '/api/v1/auth/login',
  REFRESH: '/api/v1/auth/refresh',
  LOGOUT: '/api/v1/auth/logout',
  USER_PROFILE: '/api/v1/user/profile',
  AVATAR_UPLOAD: '/api/v1/user/avatar',
  UPDATE_USERNAME: '/api/v1/user/update-username',
  PROVINCES: '/api/v1/user/provinces',
  CITIES: '/api/v1/user/cities',
  UPDATE_ADDRESS: '/api/v1/user/update-address',
};

// 聊天API端点（使用与认证API相同的基础URL）
export const CHAT_ENDPOINTS = {
  COMPLETIONS: '/api/v1/chat/completions',
  CREATE_CONVERSATION: '/api/v1/conversations/', // 创建新会话
  DELETE_CONVERSATION: '/api/v1/conversations/delete', // 🔥 新增：删除会话端点
  CHAT_HISTORY: '/api/v1/chat/history', // 聊天历史记录端点
  SUMMARY_TOPICS: '/api/v1/chat/summary_topics', // 新的话题总结端点
  TTS: '/api/v1/tts/', // 新的TTS端点
  ASR: '/api/v1/car/asr', // 新的ASR端点
  RECOMMEND_TOPICS: '/car/recommend_topics', // 保持原有端点
  VEHICLE_PICTURES: '/api/v1/car/vehicle_cars', // 新的车辆图片端点
  VEHICLE_DAMAGE: '/api/v1/car/vehicle_cars', // 新的车辆损伤端点
  RECENT_RECOMMENDATIONS: '/api/v1/chat/recommendations/recent', // 历史推车记录端点
};

// 构建完整的API URL
export const buildAuthApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.AUTH_BASE_URL}${endpoint}`;
};

// 构建聊天API URL（复用认证API的基础URL）
export const buildChatApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.AUTH_BASE_URL}${endpoint}`;
};

/**
 * 获取当前认证API的完整基础URL（用于调试）
 */
export const getAuthApiBaseUrl = (): string => {
  return API_CONFIG.AUTH_BASE_URL;
};

/**
 * 更新认证API基础URL（用于动态切换）
 * 注意：这只会影响后续的API调用，不会更新Vite代理配置
 * 在生产环境中，这个功能可以用于切换不同的API服务器
 */
export const updateAuthApiBaseUrl = (newBaseUrl: string): void => {
  API_CONFIG.AUTH_BASE_URL = newBaseUrl;
  console.log('🔄 认证API基础URL已更新为:', newBaseUrl);
  console.log('📋 当前API配置:', API_CONFIG);
};
