/**
 * 会话存储管理工具
 * 管理聊天会话的本地存储，包括会话列表、当前会话等
 */

import { generateUUID } from './chatApi';
import type { APIConversation } from './auth';
import type {
  ServerMessage,
  ServerRecommendation,
  ServerComparison,
  ChatHistoryItem
} from './chatApi';

// 存储的消息数据结构
export interface StoredMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  carRecommendations?: any[]; // 车辆推荐数据
  activeTaskType?: string; // 任务类型
  slots?: Record<string, any>; // slots数据
}

// 推荐记录数据结构
export interface CarRecommendationRecord {
  id: string; // 推荐记录的唯一ID
  messageId: string; // 关联的消息ID
  conversationId: string; // 关联的会话ID
  recommendations: any[]; // 推荐的车辆数据
  activeTaskType?: string; // 任务类型
  timestamp: number; // 创建时间
}

// 会话数据结构
export interface Conversation {
  id: string;
  title: string;
  created_at: number;
  updated_at: number;
  message_count: number;
}

// 带消息的完整会话数据结构
export interface ConversationWithMessages extends Conversation {
  messages: StoredMessage[];
}

// 本地存储键名
const STORAGE_KEYS = {
  CONVERSATIONS: 'chat_conversations',
  CURRENT_CONVERSATION_ID: 'current_conversation_id',
  USER_ID: 'chat_user_id',
  CONVERSATION_MESSAGES: 'chat_conversation_messages', // 消息存储键前缀
  CAR_RECOMMENDATIONS: 'chat_car_recommendations' // 推荐记录存储键前缀
};

/**
 * 获取所有会话列表
 * @returns Conversation[] 会话列表
 */
export const getConversations = (): Conversation[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取会话列表失败:', error);
    return [];
  }
};

/**
 * 保存会话列表
 * @param conversations 会话列表
 */
export const saveConversations = (conversations: Conversation[]): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
  } catch (error) {
    console.error('保存会话列表失败:', error);
  }
};

/**
 * 创建新会话（使用真实API）
 * @param title 会话标题（可选，默认为"新的会话"）
 * @returns Promise<string> 新会话的ID
 */
export const createNewConversation = async (title: string = '新的会话'): Promise<string> => {
  try {
    // 动态导入以避免循环依赖
    const { createConversationAPI } = await import('./auth');

    // 调用真实API创建会话
    const result = await createConversationAPI(title);

    if (result.success && result.conversationId) {
      const conversationId = result.conversationId;
      const now = Date.now();

      const newConversation: Conversation = {
        id: conversationId,
        title,
        created_at: now,
        updated_at: now,
        message_count: 0
      };

      const conversations = getConversations();
      conversations.unshift(newConversation); // 添加到列表开头
      saveConversations(conversations);

      // 设置为当前会话
      setCurrentConversationId(conversationId);

      console.log('✅ 会话创建成功，API返回ID:', conversationId);
      return conversationId;
    } else {
      throw new Error(result.message || '创建会话失败');
    }
  } catch (error) {
    console.error('❌ API创建会话失败，使用本地生成:', error);

    // API失败时的降级处理：使用本地生成的UUID
    const conversationId = generateUUID();
    const now = Date.now();

    const newConversation: Conversation = {
      id: conversationId,
      title,
      created_at: now,
      updated_at: now,
      message_count: 0
    };

    const conversations = getConversations();
    conversations.unshift(newConversation);
    saveConversations(conversations);

    setCurrentConversationId(conversationId);

    console.log('⚠️ 使用本地生成的会话ID:', conversationId);
    return conversationId;
  }
};

/**
 * 更新会话信息
 * @param conversationId 会话ID
 * @param updates 要更新的字段
 */
export const updateConversation = (
  conversationId: string, 
  updates: Partial<Pick<Conversation, 'title' | 'message_count'>>
): void => {
  const conversations = getConversations();
  const index = conversations.findIndex(conv => conv.id === conversationId);
  
  if (index !== -1) {
    conversations[index] = {
      ...conversations[index],
      ...updates,
      updated_at: Date.now()
    };
    saveConversations(conversations);
  }
};

/**
 * 删除整个会话（包括会话信息和所有消息）
 * @param conversationId 会话ID
 */
export const deleteConversation = (conversationId: string): void => {
  // 删除会话消息
  deleteConversationMessages(conversationId);

  // 从会话列表中移除
  const conversations = getConversations();
  const filteredConversations = conversations.filter(conv => conv.id !== conversationId);
  saveConversations(filteredConversations);

  // 如果删除的是当前会话，清除当前会话ID
  if (getCurrentConversationId() === conversationId) {
    clearCurrentConversationId();
  }
};

/**
 * 获取当前会话ID
 * @returns string | null 当前会话ID
 */
export const getCurrentConversationId = (): string | null => {
  return localStorage.getItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
};

/**
 * 设置当前会话ID
 * @param conversationId 会话ID
 */
export const setCurrentConversationId = (conversationId: string): void => {
  localStorage.setItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID, conversationId);
};

/**
 * 清除当前会话ID
 */
export const clearCurrentConversationId = (): void => {
  localStorage.removeItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
};

/**
 * 获取指定会话的详细信息
 * @param conversationId 会话ID
 * @returns Conversation | null 会话信息
 */
export const getConversationById = (conversationId: string): Conversation | null => {
  const conversations = getConversations();
  return conversations.find(conv => conv.id === conversationId) || null;
};

/**
 * 开始新对话
 * 创建新会话并设置为当前会话
 * @returns Promise<string> 新会话ID
 */
export const startNewConversation = async (): Promise<string> => {
  return await createNewConversation();
};

/**
 * 过滤和转换API会话数据
 * @param apiConversations API返回的会话数据
 * @returns 过滤后的本地会话数据
 */
const filterAndConvertAPIConversations = (apiConversations: APIConversation[]): Conversation[] => {
  const now = Date.now();
  const tenMinutes = 10 * 60 * 1000; // 10分钟的毫秒数

  return apiConversations
    .filter(conv => {
      // 过滤条件：如果updated_at为null且created_at超过10分钟，则不显示
      if (conv.updated_at === null) {
        const createdTime = new Date(conv.created_at).getTime();
        const timeDiff = now - createdTime;
        if (timeDiff > tenMinutes) {
          return false; // 过滤掉
        }
      }
      return true; // 保留
    })
    .map(conv => ({
      id: conv.conversation_id,
      title: conv.title,
      created_at: new Date(conv.created_at).getTime(),
      updated_at: conv.updated_at ? new Date(conv.updated_at).getTime() : new Date(conv.created_at).getTime(),
      message_count: 0 // API暂时不返回消息数量，设为0
    }))
    .sort((a, b) => b.updated_at - a.updated_at); // 按updated_at降序排列
};

// localStorage键名
const CONVERSATION_HISTORY_CACHE_KEY = 'conversation_history_cache';
const CONVERSATION_HISTORY_CACHE_TIME_KEY = 'conversation_history_cache_time';

// 历史推车记录缓存键名
const CAR_RECOMMENDATIONS_CACHE_KEY = 'car_recommendations_cache';
const CAR_RECOMMENDATIONS_CACHE_TIME_KEY = 'car_recommendations_cache_time';

/**
 * 保存会话历史到localStorage缓存
 * @param groupedConversations 分组的会话历史
 */
const saveConversationHistoryCache = (groupedConversations: any) => {
  try {
    localStorage.setItem(CONVERSATION_HISTORY_CACHE_KEY, JSON.stringify(groupedConversations));
    localStorage.setItem(CONVERSATION_HISTORY_CACHE_TIME_KEY, Date.now().toString());
    console.log('📋 会话历史已缓存到localStorage');
  } catch (error) {
    console.error('❌ 保存会话历史缓存失败:', error);
  }
};

/**
 * 从localStorage获取缓存的会话历史
 * @returns 缓存的分组会话历史或null
 */
const getConversationHistoryCache = () => {
  try {
    const cached = localStorage.getItem(CONVERSATION_HISTORY_CACHE_KEY);
    const cacheTime = localStorage.getItem(CONVERSATION_HISTORY_CACHE_TIME_KEY);

    if (cached && cacheTime) {
      const parsedCache = JSON.parse(cached);
      const cacheTimestamp = parseInt(cacheTime);

      // 检查缓存是否过期（24小时）
      const isExpired = Date.now() - cacheTimestamp > 24 * 60 * 60 * 1000;

      if (!isExpired) {
        console.log('📋 使用localStorage缓存的会话历史');
        return parsedCache;
      } else {
        console.log('⏰ 会话历史缓存已过期，将重新获取');
        clearConversationHistoryCache();
      }
    }

    return null;
  } catch (error) {
    console.error('❌ 获取会话历史缓存失败:', error);
    return null;
  }
};

/**
 * 清除会话历史缓存
 */
const clearConversationHistoryCache = () => {
  try {
    localStorage.removeItem(CONVERSATION_HISTORY_CACHE_KEY);
    localStorage.removeItem(CONVERSATION_HISTORY_CACHE_TIME_KEY);
    console.log('🗑️ 会话历史缓存已清除');
  } catch (error) {
    console.error('❌ 清除会话历史缓存失败:', error);
  }
};

/**
 * 导出清除会话历史缓存函数（供外部调用）
 */
export const clearConversationHistoryCache_Export = clearConversationHistoryCache;

/**
 * 保存历史推车记录到localStorage缓存
 * @param groupedRecommendations 分组的推车记录
 */
const saveCarRecommendationsCache = (groupedRecommendations: Record<string, CarRecommendationRecord[]>) => {
  try {
    localStorage.setItem(CAR_RECOMMENDATIONS_CACHE_KEY, JSON.stringify(groupedRecommendations));
    localStorage.setItem(CAR_RECOMMENDATIONS_CACHE_TIME_KEY, Date.now().toString());
    console.log('📋 历史推车记录已缓存到localStorage');
  } catch (error) {
    console.error('❌ 保存历史推车记录缓存失败:', error);
  }
};

/**
 * 从localStorage获取缓存的历史推车记录
 * @returns 缓存的分组推车记录或null
 */
const getCarRecommendationsCache = (): Record<string, CarRecommendationRecord[]> | null => {
  try {
    const cached = localStorage.getItem(CAR_RECOMMENDATIONS_CACHE_KEY);
    const cacheTime = localStorage.getItem(CAR_RECOMMENDATIONS_CACHE_TIME_KEY);

    if (cached && cacheTime) {
      const parsedCache = JSON.parse(cached);
      const cacheTimestamp = parseInt(cacheTime);

      // 检查缓存是否过期（24小时）
      const isExpired = Date.now() - cacheTimestamp > 24 * 60 * 60 * 1000;

      if (!isExpired) {
        console.log('📋 使用localStorage缓存的历史推车记录');
        return parsedCache;
      } else {
        console.log('⏰ 历史推车记录缓存已过期，将重新获取');
        clearCarRecommendationsCache();
      }
    }

    return null;
  } catch (error) {
    console.error('❌ 获取历史推车记录缓存失败:', error);
    return null;
  }
};

/**
 * 清除历史推车记录缓存
 */
const clearCarRecommendationsCache = () => {
  try {
    localStorage.removeItem(CAR_RECOMMENDATIONS_CACHE_KEY);
    localStorage.removeItem(CAR_RECOMMENDATIONS_CACHE_TIME_KEY);
    console.log('🗑️ 历史推车记录缓存已清除');
  } catch (error) {
    console.error('❌ 清除历史推车记录缓存失败:', error);
  }
};

/**
 * 导出清除历史推车记录缓存函数（供外部调用）
 */
export const clearCarRecommendationsCache_Export = clearCarRecommendationsCache;

/**
 * 获取会话历史记录（优先使用缓存，支持强制刷新）
 * @param forceRefresh 是否强制从服务器刷新，默认false
 * @returns Promise<分组的会话历史>
 */
export const getGroupedConversations = async (forceRefresh: boolean = false) => {
  try {
    // 如果不强制刷新，先尝试使用缓存
    if (!forceRefresh) {
      const cachedHistory = getConversationHistoryCache();
      if (cachedHistory) {
        return cachedHistory;
      }
    }

    console.log(forceRefresh ? '🔄 强制刷新会话历史...' : '📋 首次获取会话历史...');

    // 动态导入以避免循环依赖
    const { getConversationsAPI } = await import('./auth');

    // 调用API获取会话列表
    const result = await getConversationsAPI();

    let conversations: Conversation[] = [];

    if (result.success && result.conversations) {
      // 使用API数据
      conversations = filterAndConvertAPIConversations(result.conversations);
      console.log('✅ 从API获取会话列表成功，数量:', conversations.length);

      // 更新本地缓存
      saveConversations(conversations);
    } else {
      // API失败时使用本地缓存
      console.warn('⚠️ API获取会话列表失败，使用本地缓存:', result.message);
      conversations = getConversations();
    }

    // 按时间分组
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;

    const groups = {
      today: [] as Conversation[],
      yesterday: [] as Conversation[],
      thisWeek: [] as Conversation[],
      older: [] as Conversation[]
    };

    conversations.forEach(conv => {
      const timeDiff = now - conv.updated_at;

      if (timeDiff < oneDay) {
        groups.today.push(conv);
      } else if (timeDiff < 2 * oneDay) {
        groups.yesterday.push(conv);
      } else if (timeDiff < oneWeek) {
        groups.thisWeek.push(conv);
      } else {
        groups.older.push(conv);
      }
    });

    // 保存到localStorage缓存
    saveConversationHistoryCache(groups);

    return groups;
  } catch (error) {
    console.error('❌ 获取会话历史失败，使用本地缓存:', error);

    // 完全失败时使用本地缓存的原有逻辑
    const conversations = getConversations();
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;

    const groups = {
      today: [] as Conversation[],
      yesterday: [] as Conversation[],
      thisWeek: [] as Conversation[],
      older: [] as Conversation[]
    };

    conversations.forEach(conv => {
      const timeDiff = now - conv.updated_at;

      if (timeDiff < oneDay) {
        groups.today.push(conv);
      } else if (timeDiff < 2 * oneDay) {
        groups.yesterday.push(conv);
      } else if (timeDiff < oneWeek) {
        groups.thisWeek.push(conv);
      } else {
        groups.older.push(conv);
      }
    });

    return groups;
  }
};

/**
 * 清理旧会话（保留最近的50个会话）
 */
export const cleanupOldConversations = (): void => {
  const conversations = getConversations();
  if (conversations.length > 50) {
    // 按更新时间排序，保留最新的50个
    const sortedConversations = [...conversations]
      .sort((a, b) => b.updated_at - a.updated_at);
    const conversationsToKeep = sortedConversations.slice(0, 50);
    const conversationsToDelete = sortedConversations.slice(50);

    // 删除旧会话的消息数据
    conversationsToDelete.forEach(conv => {
      cleanupConversationData(conv.id);
    });

    saveConversations(conversationsToKeep);
  }
};

/**
 * 增加会话消息计数
 * @param conversationId 会话ID
 */
export const incrementMessageCount = (conversationId: string): void => {
  const conversation = getConversationById(conversationId);
  if (conversation) {
    updateConversation(conversationId, {
      message_count: conversation.message_count + 1
    });
  }
};

// ==================== 消息存储功能 ====================

/**
 * 获取会话消息存储键名
 * @param conversationId 会话ID
 * @returns string 存储键名
 */
const getMessagesStorageKey = (conversationId: string): string => {
  return `${STORAGE_KEYS.CONVERSATION_MESSAGES}_${conversationId}`;
};

/**
 * 获取指定会话的所有消息
 * @param conversationId 会话ID
 * @returns StoredMessage[] 消息列表
 */
export const getConversationMessages = (conversationId: string): StoredMessage[] => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    const stored = localStorage.getItem(storageKey);
    const messages = stored ? JSON.parse(stored) : [];

    // 去重逻辑：根据消息ID去除重复项
    const uniqueMessages = messages.filter((message: StoredMessage, index: number, array: StoredMessage[]) => {
      return array.findIndex(m => m.id === message.id) === index;
    });

    // 如果发现重复消息，更新存储
    if (uniqueMessages.length !== messages.length) {
      console.log(`发现并清理了 ${messages.length - uniqueMessages.length} 条重复消息`);
      saveConversationMessages(conversationId, uniqueMessages);
    }

    return uniqueMessages;
  } catch (error) {
    console.error('获取会话消息失败:', error);
    return [];
  }
};

/**
 * 保存会话消息
 * @param conversationId 会话ID
 * @param messages 消息列表
 */
export const saveConversationMessages = (conversationId: string, messages: StoredMessage[]): void => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    localStorage.setItem(storageKey, JSON.stringify(messages));
  } catch (error) {
    console.error('保存会话消息失败:', error);
    // 如果存储失败，可能是容量不足，尝试清理旧数据
    cleanupOldConversations();
    try {
      const storageKey = getMessagesStorageKey(conversationId);
      localStorage.setItem(storageKey, JSON.stringify(messages));
    } catch (retryError) {
      console.error('重试保存会话消息仍然失败:', retryError);
    }
  }
};

/**
 * 添加消息到指定会话
 * @param conversationId 会话ID
 * @param message 要添加的消息
 */
export const addMessageToConversation = (conversationId: string, message: StoredMessage): void => {
  const messages = getConversationMessages(conversationId);

  // 检查是否已存在相同ID的消息
  const existingMessageIndex = messages.findIndex(m => m.id === message.id);
  if (existingMessageIndex !== -1) {
    console.log(`消息 ${message.id} 已存在，跳过添加`);
    return;
  }

  messages.push(message);
  saveConversationMessages(conversationId, messages);
};

/**
 * 更新会话中的指定消息
 * @param conversationId 会话ID
 * @param messageId 消息ID
 * @param updates 要更新的字段
 */
export const updateMessageInConversation = (
  conversationId: string,
  messageId: string,
  updates: Partial<StoredMessage>
): void => {
  const messages = getConversationMessages(conversationId);
  const messageIndex = messages.findIndex(msg => msg.id === messageId);

  if (messageIndex !== -1) {
    messages[messageIndex] = { ...messages[messageIndex], ...updates };
    saveConversationMessages(conversationId, messages);
  }
};

/**
 * 获取带消息的完整会话数据
 * @param conversationId 会话ID
 * @returns ConversationWithMessages | null 完整会话数据
 */
export const getConversationWithMessages = (conversationId: string): ConversationWithMessages | null => {
  const conversation = getConversationById(conversationId);
  if (!conversation) {
    return null;
  }

  const messages = getConversationMessages(conversationId);
  return {
    ...conversation,
    messages
  };
};

/**
 * 删除会话的所有消息
 * @param conversationId 会话ID
 */
export const deleteConversationMessages = (conversationId: string): void => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('删除会话消息失败:', error);
  }
};



/**
 * 清理指定会话ID的消息数据（在删除会话时调用）
 * @param conversationId 会话ID
 */
export const cleanupConversationData = (conversationId: string): void => {
  deleteConversationMessages(conversationId);
  deleteConversationRecommendations(conversationId);
};

// ==================== 推荐记录存储功能 ====================

/**
 * 获取推荐记录存储键名
 * @param conversationId 会话ID
 * @returns string 存储键名
 */
const getRecommendationsStorageKey = (conversationId: string): string => {
  return `${STORAGE_KEYS.CAR_RECOMMENDATIONS}_${conversationId}`;
};

/**
 * 获取指定会话的所有推荐记录
 * @param conversationId 会话ID
 * @returns CarRecommendationRecord[] 推荐记录列表
 */
export const getConversationRecommendations = (conversationId: string): CarRecommendationRecord[] => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    const stored = localStorage.getItem(storageKey);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取推荐记录失败:', error);
    return [];
  }
};

/**
 * 保存会话推荐记录
 * @param conversationId 会话ID
 * @param recommendations 推荐记录列表
 */
export const saveConversationRecommendations = (conversationId: string, recommendations: CarRecommendationRecord[]): void => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    localStorage.setItem(storageKey, JSON.stringify(recommendations));
  } catch (error) {
    console.error('保存推荐记录失败:', error);
  }
};

/**
 * 添加推荐记录到指定会话
 * @param conversationId 会话ID
 * @param messageId 关联的消息ID
 * @param recommendations 推荐数据
 * @param activeTaskType 任务类型
 * @returns string 推荐记录ID
 */
export const addRecommendationToConversation = (
  conversationId: string,
  messageId: string,
  recommendations: any[],
  activeTaskType?: string
): string => {
  const recordId = generateUUID();
  const record: CarRecommendationRecord = {
    id: recordId,
    messageId,
    conversationId,
    recommendations,
    activeTaskType,
    timestamp: Date.now()
  };

  const existingRecords = getConversationRecommendations(conversationId);
  existingRecords.push(record);
  saveConversationRecommendations(conversationId, existingRecords);

  return recordId;
};

/**
 * 根据消息ID获取推荐记录
 * @param conversationId 会话ID
 * @param messageId 消息ID
 * @returns CarRecommendationRecord | null 推荐记录
 */
export const getRecommendationByMessageId = (conversationId: string, messageId: string): CarRecommendationRecord | null => {
  const recommendations = getConversationRecommendations(conversationId);
  return recommendations.find(rec => rec.messageId === messageId) || null;
};

/**
 * 删除会话的所有推荐记录
 * @param conversationId 会话ID
 */
export const deleteConversationRecommendations = (conversationId: string): void => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('删除推荐记录失败:', error);
  }
};

/**
 * 删除指定的推荐记录
 * @param conversationId 会话ID
 * @param recordId 推荐记录ID
 */
export const deleteRecommendationRecord = (conversationId: string, recordId: string): void => {
  const recommendations = getConversationRecommendations(conversationId);
  const filteredRecommendations = recommendations.filter(rec => rec.id !== recordId);
  saveConversationRecommendations(conversationId, filteredRecommendations);
};

/**
 * 获取所有会话的推荐记录
 * @returns CarRecommendationRecord[] 所有推荐记录列表，按时间倒序排列
 */
export const getAllCarRecommendations = (): CarRecommendationRecord[] => {
  const conversations = getConversations();
  const allRecommendations: CarRecommendationRecord[] = [];

  conversations.forEach(conversation => {
    const conversationRecommendations = getConversationRecommendations(conversation.id);
    allRecommendations.push(...conversationRecommendations);
  });

  // 按时间倒序排列（最新的在前）
  return allRecommendations.sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * 按日期分组获取所有推荐记录（支持从服务器获取实时数据，默认使用缓存）
 * @param forceRefresh 是否强制从服务器刷新，默认false
 * @param limit 限制数量，默认为50
 * @returns Promise<Record<string, CarRecommendationRecord[]>> 按日期分组的推荐记录
 */
export const getGroupedCarRecommendations = async (
  forceRefresh: boolean = false,
  limit: number = 50
): Promise<Record<string, CarRecommendationRecord[]>> => {
  try {
    // 如果不强制刷新，先尝试使用缓存
    if (!forceRefresh) {
      const cachedRecommendations = getCarRecommendationsCache();
      if (cachedRecommendations) {
        return cachedRecommendations;
      }
    }

    let allRecommendations: CarRecommendationRecord[] = [];

    if (forceRefresh) {
      // 从服务器获取最新数据
      const { getRecentRecommendationsAPI } = await import('./chatApi');
      const response = await getRecentRecommendationsAPI(limit);

      if (response.success && response.data && Array.isArray(response.data)) {
        // 转换服务器数据为本地格式
        allRecommendations = response.data.map(item => {
          // 确保recommendations是数组
          let recommendations: any[] = [];

          if (item.recommendation_data) {
            if (Array.isArray(item.recommendation_data)) {
              recommendations = item.recommendation_data;
            } else {
              // 如果是单个对象，包装成数组
              recommendations = [item.recommendation_data];
            }
          }

          return {
            id: item.id,
            messageId: item.message_id,
            conversationId: item.conversation_id,
            recommendations: recommendations,
            activeTaskType: item.task_type,
            timestamp: new Date(item.created_at).getTime()
          };
        }).filter(record => record.recommendations.length > 0); // 过滤掉没有推荐数据的记录

        console.log('✅ 从服务器获取历史推车记录成功，数量:', allRecommendations.length);
      } else {
        console.warn('⚠️ 服务器返回数据为空或格式错误，使用本地缓存');
        allRecommendations = getAllCarRecommendations();
      }
    } else {
      // 首次加载时，如果没有缓存，尝试从服务器获取数据
      try {
        const { getRecentRecommendationsAPI } = await import('./chatApi');
        const response = await getRecentRecommendationsAPI(limit);

        if (response.success && response.data && Array.isArray(response.data)) {
          // 转换服务器数据为本地格式
          allRecommendations = response.data.map(item => {
            // 确保recommendations是数组
            let recommendations: any[] = [];

            if (item.recommendation_data) {
              if (Array.isArray(item.recommendation_data)) {
                recommendations = item.recommendation_data;
              } else {
                // 如果是单个对象，包装成数组
                recommendations = [item.recommendation_data];
              }
            }

            return {
              id: item.id,
              messageId: item.message_id,
              conversationId: item.conversation_id,
              recommendations: recommendations,
              activeTaskType: item.task_type,
              timestamp: new Date(item.created_at).getTime()
            };
          }).filter(record => record.recommendations.length > 0); // 过滤掉没有推荐数据的记录

          console.log('✅ 首次加载从服务器获取历史推车记录成功，数量:', allRecommendations.length);
        } else {
          console.warn('⚠️ 服务器返回数据为空或格式错误，使用本地存储');
          allRecommendations = getAllCarRecommendations();
        }
      } catch (error) {
        console.warn('⚠️ 从服务器获取数据失败，使用本地存储:', error);
        allRecommendations = getAllCarRecommendations();
      }
    }

    // 按日期分组
    const grouped: Record<string, CarRecommendationRecord[]> = {};

    allRecommendations.forEach(record => {
      const date = new Date(record.timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const threeDaysAgo = new Date(today);
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      let dateKey: string;

      if (date.toDateString() === today.toDateString()) {
        dateKey = '今天';
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = '昨天';
      } else if (date.toDateString() === threeDaysAgo.toDateString()) {
        dateKey = '三天前';
      } else {
        // 格式化为 "7月15日" 的形式
        dateKey = `${date.getMonth() + 1}月${date.getDate()}日`;
      }

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(record);
    });

    // 保存到localStorage缓存
    saveCarRecommendationsCache(grouped);

    return grouped;
  } catch (error) {
    console.error('❌ 获取推车记录失败，使用本地缓存:', error);
    // 出错时使用本地缓存
    const allRecommendations = getAllCarRecommendations();
    const grouped: Record<string, CarRecommendationRecord[]> = {};

    allRecommendations.forEach(record => {
      const date = new Date(record.timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const threeDaysAgo = new Date(today);
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      let dateKey: string;

      if (date.toDateString() === today.toDateString()) {
        dateKey = '今天';
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = '昨天';
      } else if (date.toDateString() === threeDaysAgo.toDateString()) {
        dateKey = '三天前';
      } else {
        // 格式化为 "7月15日" 的形式
        dateKey = `${date.getMonth() + 1}月${date.getDate()}日`;
      }

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(record);
    });

    return grouped;
  }
};

// ==================== 服务器数据转换功能 ====================

/**
 * 将服务器消息转换为本地存储格式
 * @param serverMessage 服务器消息数据
 * @param recommendations 关联的推荐数据
 * @param comparisons 关联的对比数据
 * @returns StoredMessage 本地存储格式的消息
 */
export const convertServerMessageToStored = (
  serverMessage: ServerMessage,
  recommendations: ServerRecommendation[] = [],
  comparisons: ServerComparison[] = []
): StoredMessage => {
  // 处理推荐数据
  let carRecommendations: any[] | undefined;
  let activeTaskType: string | undefined;
  let slots: Record<string, any> | undefined;

  if (recommendations.length > 0) {
    // 合并所有推荐数据
    carRecommendations = recommendations.flatMap(rec => rec.recommendation_data || []);
    // 使用第一个推荐的任务类型
    activeTaskType = recommendations[0]?.task_type;
    // 合并所有slots数据
    slots = recommendations.reduce((acc, rec) => ({ ...acc, ...rec.slots_data }), {});
  }

  // 处理对比数据（如果有的话，添加到推荐数据中）
  if (comparisons.length > 0) {
    const comparisonData = comparisons.flatMap(comp => comp.comparison_data || []);
    if (comparisonData.length > 0) {
      carRecommendations = [...(carRecommendations || []), ...comparisonData];
    }
  }

  return {
    id: serverMessage.message_id,
    type: serverMessage.message_type === 'assistant' ? 'ai' : 'user',
    content: serverMessage.content,
    timestamp: new Date(serverMessage.created_at).getTime(),
    carRecommendations: carRecommendations && carRecommendations.length > 0 ? carRecommendations : undefined,
    activeTaskType,
    slots: slots && Object.keys(slots).length > 0 ? slots : undefined
  };
};

/**
 * 将服务器推荐数据转换为本地推荐记录格式
 * @param serverRecommendation 服务器推荐数据
 * @returns CarRecommendationRecord 本地推荐记录格式
 */
export const convertServerRecommendationToLocal = (
  serverRecommendation: ServerRecommendation
): CarRecommendationRecord => {
  return {
    id: serverRecommendation.id,
    messageId: serverRecommendation.message_id,
    conversationId: serverRecommendation.conversation_id,
    recommendations: serverRecommendation.recommendation_data || [],
    activeTaskType: serverRecommendation.task_type,
    timestamp: new Date(serverRecommendation.created_at).getTime()
  };
};

/**
 * 批量转换聊天历史项为本地存储格式
 * @param historyItems 服务器聊天历史项列表
 * @returns StoredMessage[] 本地存储格式的消息列表
 */
export const convertChatHistoryToStored = (historyItems: ChatHistoryItem[]): StoredMessage[] => {
  return historyItems.map(item =>
    convertServerMessageToStored(item.message, item.recommendations, item.comparisons)
  );
};

/**
 * 合并服务器数据与本地缓存数据
 * @param conversationId 会话ID
 * @param serverMessages 服务器消息列表
 * @param serverRecommendations 服务器推荐列表
 * @returns 合并后的本地消息列表
 */
export const mergeServerDataWithLocal = (
  conversationId: string,
  serverMessages: StoredMessage[],
  serverRecommendations: CarRecommendationRecord[]
): StoredMessage[] => {
  // 获取本地现有消息
  const localMessages = getConversationMessages(conversationId);

  // 创建消息ID映射，用于快速查找


  // 合并消息列表
  const mergedMessages: StoredMessage[] = [];
  const processedIds = new Set<string>();

  // 首先添加所有服务器消息（优先使用服务器数据）
  serverMessages.forEach(serverMsg => {
    mergedMessages.push(serverMsg);
    processedIds.add(serverMsg.id);
  });

  // 然后添加本地独有的消息（服务器没有的）
  localMessages.forEach(localMsg => {
    if (!processedIds.has(localMsg.id)) {
      mergedMessages.push(localMsg);
      processedIds.add(localMsg.id);
    }
  });

  // 按时间戳排序
  mergedMessages.sort((a, b) => a.timestamp - b.timestamp);

  // 保存合并后的消息
  saveConversationMessages(conversationId, mergedMessages);

  // 保存推荐记录
  if (serverRecommendations.length > 0) {
    const localRecommendations = getConversationRecommendations(conversationId);
    const mergedRecommendations = [...serverRecommendations];

    // 添加本地独有的推荐记录
    localRecommendations.forEach(localRec => {
      const exists = serverRecommendations.some(serverRec => serverRec.id === localRec.id);
      if (!exists) {
        mergedRecommendations.push(localRec);
      }
    });

    saveConversationRecommendations(conversationId, mergedRecommendations);
  }

  return mergedMessages;
};

// ==================== 智能缓存管理功能 ====================

/**
 * 从服务器加载会话数据并与本地缓存合并
 * @param conversationId 会话ID
 * @param page 页码，默认为1
 * @param limit 每页数量，默认为50
 * @param forceRefresh 是否强制刷新，默认为false
 * @returns Promise<StoredMessage[]> 合并后的消息列表
 */
export const loadConversationFromServer = async (
  conversationId: string,
  page: number = 1,
  limit: number = 50,
  forceRefresh: boolean = false
): Promise<StoredMessage[]> => {
  try {
    // 动态导入以避免循环依赖
    const { getChatHistoryAPI } = await import('./chatApi');

    console.log(`🔄 开始从服务器加载会话 ${conversationId} 的历史记录...`);

    // 调用API获取聊天历史
    const response = await getChatHistoryAPI(conversationId, page, limit, !forceRefresh);

    if (!response.success || !response.data) {
      throw new Error(response.message || '获取聊天历史失败');
    }

    const { history, summary } = response.data;

    // 转换服务器数据为本地格式
    const serverMessages = convertChatHistoryToStored(history);

    // 转换推荐数据
    const serverRecommendations: CarRecommendationRecord[] = [];
    history.forEach(item => {
      item.recommendations.forEach(rec => {
        serverRecommendations.push(convertServerRecommendationToLocal(rec));
      });
    });

    // 更新会话摘要信息
    if (summary) {
      const conversation = getConversationById(conversationId);
      if (conversation) {
        updateConversation(conversationId, {
          title: summary.title,
          message_count: summary.message_count
        });
      }
    }

    // 合并服务器数据与本地缓存
    const mergedMessages = mergeServerDataWithLocal(
      conversationId,
      serverMessages,
      serverRecommendations
    );

    console.log(`✅ 成功加载并合并会话 ${conversationId} 的数据，消息数量: ${mergedMessages.length}`);
    return mergedMessages;

  } catch (error) {
    console.error(`❌ 从服务器加载会话 ${conversationId} 失败:`, error);

    // 降级到本地缓存
    console.log(`⚠️ 降级使用本地缓存数据`);
    return getConversationMessages(conversationId);
  }
};

/**
 * 同步会话数据（增量更新）
 * @param conversationId 会话ID
 * @param lastSyncTime 上次同步时间戳，用于增量更新
 * @returns Promise<{ hasNewData: boolean; messages: StoredMessage[] }> 同步结果
 */
export const syncConversationData = async (
  conversationId: string,
  lastSyncTime?: number
): Promise<{ hasNewData: boolean; messages: StoredMessage[] }> => {
  try {
    console.log(`🔄 开始同步会话 ${conversationId} 的数据...`);

    // 获取本地现有消息
    const localMessages = getConversationMessages(conversationId);
    const localMessageCount = localMessages.length;

    // 从服务器获取最新数据
    const serverMessages = await loadConversationFromServer(conversationId, 1, 100, true);

    // 检查是否有新数据
    const hasNewData = serverMessages.length > localMessageCount ||
                      (lastSyncTime ? serverMessages.some(msg => msg.timestamp > lastSyncTime) : false);

    if (hasNewData) {
      console.log(`✅ 检测到会话 ${conversationId} 有新数据，消息数量: ${localMessageCount} -> ${serverMessages.length}`);
    } else {
      console.log(`ℹ️ 会话 ${conversationId} 无新数据`);
    }

    return {
      hasNewData,
      messages: serverMessages
    };

  } catch (error) {
    console.error(`❌ 同步会话 ${conversationId} 数据失败:`, error);

    // 返回本地数据
    return {
      hasNewData: false,
      messages: getConversationMessages(conversationId)
    };
  }
};

/**
 * 批量同步多个会话的数据
 * @param conversationIds 会话ID列表
 * @returns Promise<Record<string, { hasNewData: boolean; messages: StoredMessage[] }>> 同步结果映射
 */
export const batchSyncConversations = async (
  conversationIds: string[]
): Promise<Record<string, { hasNewData: boolean; messages: StoredMessage[] }>> => {
  const results: Record<string, { hasNewData: boolean; messages: StoredMessage[] }> = {};

  // 并发同步，但限制并发数量避免过载
  const concurrencyLimit = 3;
  const chunks = [];

  for (let i = 0; i < conversationIds.length; i += concurrencyLimit) {
    chunks.push(conversationIds.slice(i, i + concurrencyLimit));
  }

  for (const chunk of chunks) {
    const promises = chunk.map(async (conversationId) => {
      const result = await syncConversationData(conversationId);
      results[conversationId] = result;
    });

    await Promise.all(promises);
  }

  return results;
};
