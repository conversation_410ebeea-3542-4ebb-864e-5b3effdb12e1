/**
 * 省市数据管理工具
 * 负责从API获取省市数据，缓存管理，以及数据格式转换
 */

import { getProvincesAPI, getCitiesAPI, ProvinceInfo, CityInfo } from './auth';
import { Province, City } from '../data/chinaAreaData';
import { chinaAreaData } from '../data/chinaAreaData';

// 缓存键名
const CACHE_KEYS = {
  PROVINCES: 'cached_provinces',
  CITIES_PREFIX: 'cached_cities_',
  CACHE_TIMESTAMP: 'area_cache_timestamp'
};

// 缓存有效期（1小时）
const CACHE_DURATION = 60 * 60 * 1000;

/**
 * 检查缓存是否有效
 * @returns boolean 缓存是否有效
 */
const isCacheValid = (): boolean => {
  try {
    const timestamp = localStorage.getItem(CACHE_KEYS.CACHE_TIMESTAMP);
    if (!timestamp) return false;
    
    const cacheTime = parseInt(timestamp, 10);
    const now = Date.now();
    
    return (now - cacheTime) < CACHE_DURATION;
  } catch (error) {
    console.error('检查缓存有效性失败:', error);
    return false;
  }
};

/**
 * 更新缓存时间戳
 */
const updateCacheTimestamp = (): void => {
  try {
    localStorage.setItem(CACHE_KEYS.CACHE_TIMESTAMP, Date.now().toString());
  } catch (error) {
    console.error('更新缓存时间戳失败:', error);
  }
};

/**
 * 将API省份数据转换为组件所需格式
 * @param apiProvinces API返回的省份数据
 * @returns Province[] 转换后的省份数据
 */
const convertApiProvincesToLocal = (apiProvinces: ProvinceInfo[]): Province[] => {
  return apiProvinces.map(province => ({
    code: province.code,
    name: province.name,
    cities: [] // 城市数据需要单独获取
  }));
};

/**
 * 将API城市数据转换为组件所需格式
 * @param apiCities API返回的城市数据
 * @returns City[] 转换后的城市数据
 */
const convertApiCitiesToLocal = (apiCities: CityInfo[]): City[] => {
  return apiCities.map(city => ({
    code: city.code,
    name: city.name
  }));
};

/**
 * 从缓存获取省份数据
 * @returns Province[] | null 缓存的省份数据
 */
const getCachedProvinces = (): Province[] | null => {
  try {
    if (!isCacheValid()) return null;
    
    const cached = localStorage.getItem(CACHE_KEYS.PROVINCES);
    if (!cached) return null;
    
    return JSON.parse(cached);
  } catch (error) {
    console.error('获取缓存省份数据失败:', error);
    return null;
  }
};

/**
 * 缓存省份数据
 * @param provinces 省份数据
 */
const cacheProvinces = (provinces: Province[]): void => {
  try {
    localStorage.setItem(CACHE_KEYS.PROVINCES, JSON.stringify(provinces));
    updateCacheTimestamp();
  } catch (error) {
    console.error('缓存省份数据失败:', error);
  }
};

/**
 * 从缓存获取城市数据
 * @param provinceCode 省份代码
 * @returns City[] | null 缓存的城市数据
 */
const getCachedCities = (provinceCode: string): City[] | null => {
  try {
    if (!isCacheValid()) return null;
    
    const cached = localStorage.getItem(CACHE_KEYS.CITIES_PREFIX + provinceCode);
    if (!cached) return null;
    
    return JSON.parse(cached);
  } catch (error) {
    console.error('获取缓存城市数据失败:', error);
    return null;
  }
};

/**
 * 缓存城市数据
 * @param provinceCode 省份代码
 * @param cities 城市数据
 */
const cacheCities = (provinceCode: string, cities: City[]): void => {
  try {
    localStorage.setItem(CACHE_KEYS.CITIES_PREFIX + provinceCode, JSON.stringify(cities));
    updateCacheTimestamp();
  } catch (error) {
    console.error('缓存城市数据失败:', error);
  }
};

/**
 * 获取省份列表（优先API，失败时使用本地数据）
 * @returns Promise<Province[]> 省份列表
 */
export const getProvinces = async (): Promise<Province[]> => {
  try {
    // 先检查缓存
    const cachedProvinces = getCachedProvinces();
    if (cachedProvinces) {
      console.log('📋 使用缓存的省份数据');
      return cachedProvinces;
    }

    // 从API获取
    const result = await getProvincesAPI();
    if (result.success && result.provinces) {
      const provinces = convertApiProvincesToLocal(result.provinces);
      cacheProvinces(provinces);
      console.log('✅ 从API获取省份数据成功');
      return provinces;
    } else {
      throw new Error(result.message || 'API获取省份数据失败');
    }
  } catch (error) {
    console.warn('⚠️ API获取省份数据失败，使用本地数据:', error);
    // 使用本地静态数据作为备份
    return chinaAreaData;
  }
};

/**
 * 获取城市列表（优先API，失败时使用本地数据）
 * @param provinceCode 省份代码
 * @returns Promise<City[]> 城市列表
 */
export const getCities = async (provinceCode: string): Promise<City[]> => {
  try {
    // 先检查缓存
    const cachedCities = getCachedCities(provinceCode);
    if (cachedCities) {
      console.log(`📋 使用缓存的城市数据: ${provinceCode}`);
      return cachedCities;
    }

    // 从API获取
    const result = await getCitiesAPI(provinceCode);
    if (result.success && result.cities) {
      const cities = convertApiCitiesToLocal(result.cities);
      cacheCities(provinceCode, cities);
      console.log(`✅ 从API获取城市数据成功: ${provinceCode}`);
      return cities;
    } else {
      throw new Error(result.message || 'API获取城市数据失败');
    }
  } catch (error) {
    console.warn(`⚠️ API获取城市数据失败，使用本地数据: ${provinceCode}`, error);
    // 使用本地静态数据作为备份
    const localProvince = chinaAreaData.find(p => p.code === provinceCode);
    return localProvince ? localProvince.cities : [];
  }
};

/**
 * 获取完整的省市数据（包含城市）
 * @returns Promise<Province[]> 完整的省市数据
 */
export const getCompleteAreaData = async (): Promise<Province[]> => {
  try {
    const provinces = await getProvinces();
    
    // 为每个省份获取城市数据
    const provincesWithCities = await Promise.all(
      provinces.map(async (province) => {
        const cities = await getCities(province.code);
        return {
          ...province,
          cities
        };
      })
    );
    
    return provincesWithCities;
  } catch (error) {
    console.error('获取完整省市数据失败:', error);
    // 返回本地静态数据作为备份
    return chinaAreaData;
  }
};

/**
 * 清除省市数据缓存
 */
export const clearAreaDataCache = (): void => {
  try {
    // 清除省份缓存
    localStorage.removeItem(CACHE_KEYS.PROVINCES);
    
    // 清除所有城市缓存
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(CACHE_KEYS.CITIES_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
    
    // 清除时间戳
    localStorage.removeItem(CACHE_KEYS.CACHE_TIMESTAMP);
    
    console.log('✅ 省市数据缓存已清除');
  } catch (error) {
    console.error('清除省市数据缓存失败:', error);
  }
};

/**
 * 根据省市代码查找对应的名称
 * @param provinceCode 省份代码
 * @param cityCode 城市代码
 * @returns Promise<{provinceName: string, cityName: string} | null> 省市名称
 */
export const getAreaNamesByCodes = async (provinceCode: string, cityCode: string): Promise<{
  provinceName: string;
  cityName: string;
} | null> => {
  try {
    const provinces = await getProvinces();
    const province = provinces.find(p => p.code === provinceCode);
    
    if (!province) return null;
    
    const cities = await getCities(provinceCode);
    const city = cities.find(c => c.code === cityCode);
    
    if (!city) return null;
    
    return {
      provinceName: province.name,
      cityName: city.name
    };
  } catch (error) {
    console.error('根据代码获取省市名称失败:', error);
    return null;
  }
};
