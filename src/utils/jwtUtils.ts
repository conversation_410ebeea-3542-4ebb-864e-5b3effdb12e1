/**
 * JWT 工具函数
 * 用于处理 JWT 令牌的验证、解析和管理
 */

export interface JWTPayload {
  exp: number;
  iat: number;
  sub?: string;
  [key: string]: any;
}

/**
 * 从 localStorage 获取 JWT 令牌
 */
export const getToken = (): string | null => {
  try {
    return localStorage.getItem('jwt_access_token');
  } catch (error) {
    console.error('获取令牌失败:', error);
    return null;
  }
};

/**
 * 保存 JWT 令牌到 localStorage
 */
export const setToken = (token: string): void => {
  try {
    localStorage.setItem('jwt_access_token', token);
  } catch (error) {
    console.error('保存令牌失败:', error);
  }
};

/**
 * 保存登录数据到 localStorage（排除user_info）
 */
export const saveLoginData = (loginData: any): void => {
  try {
    // 保存access_token
    if (loginData.access_token) {
      localStorage.setItem('jwt_access_token', loginData.access_token);
    }

    // 保存refresh_token
    if (loginData.refresh_token) {
      localStorage.setItem('jwt_refresh_token', loginData.refresh_token);
    }

    // 保存其他登录信息
    const otherData = {
      token_type: loginData.token_type,
      expires_in: loginData.expires_in,
      device_id: loginData.device_id,
      login_timestamp: Date.now() // 添加登录时间戳
    };

    localStorage.setItem('login_data', JSON.stringify(otherData));

    console.log('登录数据已保存到localStorage');
  } catch (error) {
    console.error('保存登录数据失败:', error);
  }
};

/**
 * 获取refresh token
 */
export const getRefreshToken = (): string | null => {
  try {
    return localStorage.getItem('jwt_refresh_token');
  } catch (error) {
    console.error('获取refresh token失败:', error);
    return null;
  }
};

/**
 * 获取登录数据
 */
export const getLoginData = (): any | null => {
  try {
    const data = localStorage.getItem('login_data');
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('获取登录数据失败:', error);
    return null;
  }
};

/**
 * 清除 JWT 令牌和所有登录数据
 */
export const clearToken = (): void => {
  try {
    localStorage.removeItem('jwt_access_token');
    localStorage.removeItem('jwt_refresh_token');
    localStorage.removeItem('login_data');
    console.log('所有登录数据已清除');
  } catch (error) {
    console.error('清除令牌失败:', error);
  }
};

/**
 * 解析 JWT 令牌获取 payload
 */
export const decodeToken = (token: string): JWTPayload | null => {
  try {
    // JWT 格式: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('无效的 JWT 格式');
    }
    
    // 解码 payload (Base64URL)
    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch (error) {
    console.error('解析令牌失败:', error);
    return null;
  }
};

/**
 * 检查 JWT 令牌是否有效（未过期）
 */
export const isTokenValid = (token: string): boolean => {
  try {
    const payload = decodeToken(token);
    if (!payload || !payload.exp) {
      return false;
    }
    
    // 检查是否过期（exp 是秒级时间戳）
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp > currentTime;
  } catch (error) {
    console.error('验证令牌失败:', error);
    return false;
  }
};

/**
 * 获取令牌剩余有效时间（秒）
 */
export const getTokenRemainingTime = (token: string): number => {
  try {
    const payload = decodeToken(token);
    if (!payload || !payload.exp) {
      return 0;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    const remainingTime = payload.exp - currentTime;
    return Math.max(0, remainingTime);
  } catch (error) {
    console.error('获取令牌剩余时间失败:', error);
    return 0;
  }
};

/**
 * 检查当前存储的令牌是否有效
 */
export const isCurrentTokenValid = (): boolean => {
  const token = getToken();
  if (!token) {
    return false;
  }
  return isTokenValid(token);
};
