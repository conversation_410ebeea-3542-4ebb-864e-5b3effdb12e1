<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 渲染测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-message {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 18px;
            padding: 16px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #00A76F;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🐾 Markdown 渲染功能测试</h1>
    
    <div class="test-message">
        <h2>测试内容示例</h2>
        <p>以下是一些Markdown格式的测试内容，你可以在AI聊天中使用：</p>
        
        <h3>1. 基础格式</h3>
        <pre><code>**粗体文本** 和 *斜体文本*
`行内代码` 示例
</code></pre>
        
        <h3>2. 代码块</h3>
        <pre><code>```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
}
greet('汽车街用户');
```</code></pre>
        
        <h3>3. 列表</h3>
        <pre><code>### 推荐车型：
- **奔驰C级**: 豪华舒适，适合商务
- **宝马3系**: 操控性能优秀
- **奥迪A4**: 科技配置丰富

### 购车建议：
1. 确定预算范围
2. 选择合适的车型
3. 对比不同品牌
4. 试驾体验</code></pre>
        
        <h3>4. 表格</h3>
        <pre><code>| 车型 | 价格 | 油耗 |
|------|------|------|
| 奔驰C级 | 30-50万 | 7.5L |
| 宝马3系 | 28-48万 | 7.2L |
| 奥迪A4 | 26-45万 | 7.0L |</code></pre>
        
        <h3>5. 引用</h3>
        <pre><code>> 💡 **购车小贴士**：
> 在选择车辆时，建议优先考虑自己的实际需求，
> 而不是盲目追求品牌或配置。</code></pre>
    </div>
    
    <div class="test-message">
        <h2>🎯 如何测试</h2>
        <ol>
            <li>启动你的AI聊天应用</li>
            <li>复制上面的Markdown内容</li>
            <li>让AI回复包含这些格式的消息</li>
            <li>查看渲染效果是否正确</li>
        </ol>
        
        <p><strong>注意</strong>：现在AI消息气泡已经支持完整的Markdown渲染，包括：</p>
        <ul>
            <li>✅ 代码高亮</li>
            <li>✅ 表格渲染</li>
            <li>✅ 列表格式</li>
            <li>✅ 文本样式（粗体、斜体）</li>
            <li>✅ 引用块</li>
            <li>✅ 标题层级</li>
        </ul>
    </div>
</body>
</html>
