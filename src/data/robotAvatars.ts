/**
 * 机器人头像数据
 * 定义可选的智能精灵形象
 */

export interface RobotAvatar {
  id: string;
  name: string;
  color: string;
  bgColor: string;
  description: string;
}

export const robotAvatars: RobotAvatar[] = [
  {
    id: 'blue',
    name: '机器人 A',
    color: '#3B82F6',
    bgColor: '#DBEAFE',
    description: '友善可靠的智能助手'
  },
  {
    id: 'green',
    name: '机器人 B',
    color: '#00A76F',
    bgColor: '#D1FAE5',
    description: '活力充沛的汽车专家'
  }
];

/**
 * 根据ID获取机器人头像
 * @param id 机器人头像ID
 * @returns RobotAvatar | null
 */
export const getRobotAvatarById = (id: string): RobotAvatar | null => {
  return robotAvatars.find(avatar => avatar.id === id) || null;
};

/**
 * 获取默认机器人头像
 * @returns RobotAvatar
 */
export const getDefaultRobotAvatar = (): RobotAvatar => {
  return robotAvatars[0]; // 蓝色精灵
};

export default robotAvatars;
