/**
 * 语言数据定义
 * 支持的语言列表和相关信息
 */

import { ChinaFlag, USAFlag } from '../components/FlagIcons';

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flagComponent: React.ComponentType<{ size?: number; className?: string }>;
  description: string;
}

export const supportedLanguages: Language[] = [
  {
    code: 'zh-CN',
    name: '中文',
    nativeName: '简体中文',
    flagComponent: ChinaFlag,
    description: '中国大陆地区使用的简体中文'
  },
  {
    code: 'en-US',
    name: 'English',
    nativeName: 'English',
    flagComponent: USAFlag,
    description: '美国地区使用的英语'
  }
];

/**
 * 根据语言代码获取语言信息
 * @param code 语言代码
 * @returns Language | null
 */
export const getLanguageByCode = (code: string): Language | null => {
  return supportedLanguages.find(lang => lang.code === code) || null;
};

/**
 * 获取默认语言
 * @returns Language
 */
export const getDefaultLanguage = (): Language => {
  return supportedLanguages[0]; // 中文
};

/**
 * 获取语言显示名称
 * @param code 语言代码
 * @param useNativeName 是否使用本地名称
 * @returns string
 */
export const getLanguageDisplayName = (code: string, useNativeName: boolean = false): string => {
  const language = getLanguageByCode(code);
  if (!language) return '中文';
  
  return useNativeName ? language.nativeName : language.name;
};

/**
 * 检查语言代码是否支持
 * @param code 语言代码
 * @returns boolean
 */
export const isLanguageSupported = (code: string): boolean => {
  return supportedLanguages.some(lang => lang.code === code);
};

export default supportedLanguages;
