#!/bin/bash

# 生成自签名SSL证书用于本地HTTPS开发

echo "🔐 生成本地HTTPS开发证书..."

# 创建证书目录
mkdir -p certs

# 生成私钥
openssl genrsa -out certs/localhost.key 2048

# 创建证书签名请求配置
cat > certs/localhost.conf << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
CN=localhost

[v3_req]
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ***********
IP.3 = *************
IP.4 = ::1
EOF

# 生成证书
openssl req -new -x509 -key certs/localhost.key -out certs/localhost.crt -days 365 -config certs/localhost.conf -extensions v3_req

echo "✅ 证书生成完成！"
echo "📁 证书位置: certs/localhost.crt"
echo "🔑 私钥位置: certs/localhost.key"
echo ""
echo "⚠️  首次访问时，浏览器会显示安全警告，点击'高级'然后'继续访问'即可"
