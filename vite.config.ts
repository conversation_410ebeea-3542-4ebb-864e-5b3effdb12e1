import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import fs from "fs";

// 代理配置 - 开发和预览环境共享
const proxyConfig = {
  // 代理所有以 /api 开头的请求到 SenseTime API
  '/api': {
    target: 'https://api-gai.metishon.co',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, ''),
    secure: true,
    timeout: 30000, // 30秒超时
    configure: (proxy, _options) => {
      proxy.on('error', (err, req, res) => {
        console.log('proxy error', err);
        console.log('Request URL:', req.url);
        console.log('Error details:', {
          code: err.code,
          message: err.message,
          host: err.host,
          port: err.port
        });

        // 设置错误响应
        if (!res.headersSent) {
          res.writeHead(500, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          });
          res.end(JSON.stringify({
            error: 'Proxy Error',
            message: '代理服务器连接失败，请稍后重试',
            code: err.code,
            timestamp: new Date().toISOString()
          }));
        }
      });
      proxy.on('proxyReq', (proxyReq, req, _res) => {
        console.log('Sending Request to the Target:', req.method, req.url);
        // 设置更长的超时时间
        proxyReq.setTimeout(30000);
        // 添加keep-alive头
        proxyReq.setHeader('Connection', 'keep-alive');
      });
      proxy.on('proxyRes', (proxyRes, req, _res) => {
        console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
      });
    },
  },
  // 代理字节跳动TTS API
  '/tts-api': {
    target: 'https://openspeech.bytedance.com',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/tts-api/, ''),
    secure: true,
    configure: (proxy, _options) => {
      proxy.on('error', (err, _req, _res) => {
        console.log('TTS proxy error', err);
      });
      proxy.on('proxyReq', (proxyReq, req, _res) => {
        console.log('Sending TTS Request to the Target:', req.method, req.url);
      });
      proxy.on('proxyRes', (proxyRes, req, _res) => {
        console.log('Received TTS Response from the Target:', proxyRes.statusCode, req.url);
      });
    },
  },
  // 代理认证API到本地服务器
  '/auth-api': {
    target: 'https://api.st.vup.tools',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/auth-api/, ''),
    secure: false,
    timeout: 30000, // 30秒超时
    configure: (proxy, _options) => {
      proxy.on('error', (err, req, res) => {
        console.log('Auth API proxy error', err);
        console.log('Request URL:', req.url);

        // 设置错误响应
        if (!res.headersSent) {
          res.writeHead(500, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          });
          res.end(JSON.stringify({
            error: 'Auth API Proxy Error',
            message: '认证服务器连接失败，请稍后重试',
            code: err.code,
            timestamp: new Date().toISOString()
          }));
        }
      });
      proxy.on('proxyReq', (proxyReq, req, _res) => {
        console.log('Sending Auth Request to the Target:', req.method, req.url);
      });
      proxy.on('proxyRes', (proxyRes, req, _res) => {
        console.log('Received Auth Response from the Target:', proxyRes.statusCode, req.url);
      });
    },
  }
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  server: {
    // 临时使用HTTP进行测试
    // https: {
    //   key: fs.readFileSync('./certs/localhost.key'),
    //   cert: fs.readFileSync('./certs/localhost.crt'),
    // },
    // 允许外部访问（局域网）
    host: '0.0.0.0',
    // 端口配置
    port: 5173,
    proxy: proxyConfig
  },
  // 预览模式也使用相同的代理配置
  preview: {
    host: '0.0.0.0',
    port: 4173,
    proxy: proxyConfig
  }
});
