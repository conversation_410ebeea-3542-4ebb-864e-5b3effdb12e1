# 聊天API迁移总结

## 🔄 API变更概述

### 原API配置
- **基础URL**: `https://api-gai.metishon.co` (通过 `/api` 代理)
- **端点**: `/car/carchat`
- **请求体**: 
  ```json
  {
    "message": "用户消息",
    "conversation_id": "会话ID",
    "user_id": "用户ID"
  }
  ```

### 新API配置
- **基础URL**: `http://0.0.0.0:8000` (通过 `/auth-api` 代理)
- **端点**: `/api/v1/chat/completions`
- **请求体**: 
  ```json
  {
    "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
    "message": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！"
  }
  ```

## 📁 修改的文件

### 1. `src/utils/apiConfig.ts` (新增)
- 添加了聊天API端点配置
- 提供了 `buildChatApiUrl()` 函数
- 统一管理API配置

### 2. `src/utils/chatApi.ts`
- 更新了聊天API端点为 `/api/v1/chat/completions`
- 简化了请求体结构，移除了 `user_id` 字段
- 使用新的API配置管理
- 保持了流式响应处理逻辑

### 3. `src/pages/ChatPage.tsx`
- 更新了 `sendChatMessage` 调用
- 移除了 `user_id` 参数
- 调整了参数顺序为 `conversation_id`, `message`

## 🔧 技术实现

### 代理配置
聊天API现在复用认证API的代理配置：
```typescript
// vite.config.ts 中的代理配置
'/auth-api': {
  target: 'http://0.0.0.0:8000',
  changeOrigin: true,
  rewrite: (path) => path.replace(/^\/auth-api/, ''),
  // ...
}
```

### API调用示例
```typescript
// 新的API调用方式
await sendChatMessage({
  conversation_id: "550e8400-e29b-41d4-a716-446655440000",
  message: "帮我找一辆贰拾万元左右的奥迪 suv"
});
```

### 认证方式
- 继续使用JWT Bearer Token认证
- 从localStorage获取access_token
- 请求头格式：`Authorization: Bearer ${token}`

## ✅ 兼容性保证

### 保持不变的功能
- 流式响应处理
- 错误处理机制
- 响应数据结构
- 用户界面交互

### 其他API端点
以下端点仍使用原有配置（`/api` 代理）：
- `/car/summary_topics` - 话题总结
- `/car/asr` - 语音识别
- `/car/recommend_topics` - 推荐话题
- `/vehicle_cars` - 车辆相关API

## 🚀 部署说明

1. **开发环境**: 确保 `http://0.0.0.0:8000` 服务正常运行
2. **代理配置**: Vite代理会自动转发 `/auth-api` 请求
3. **认证**: 使用登录接口获取的JWT token
4. **测试**: 验证聊天功能是否正常工作

## 📝 注意事项

1. **URL切换**: 如需切换API基础URL，可通过 `updateAuthApiBaseUrl()` 函数
2. **错误处理**: 保持了原有的错误处理逻辑
3. **类型安全**: 更新了TypeScript接口定义
4. **向后兼容**: 其他API功能不受影响

## 🔍 验证清单

- [x] 代码编译通过
- [x] TypeScript类型检查通过
- [x] API端点配置正确
- [x] 请求体格式符合新API要求
- [x] JWT认证配置正确
- [x] 流式响应处理保持不变
- [x] 错误处理机制完整
