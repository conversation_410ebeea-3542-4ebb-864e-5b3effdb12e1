# 代理配置说明

## 开发环境和构建预览

已在 `vite.config.ts` 中配置了代理，将 `/api` 路径代理到 `https://api-gai.metishon.co`。
**重要更新**：现在开发环境和构建预览环境都使用相同的代理配置。

### 配置详情
```typescript
// 代理配置 - 开发和预览环境共享
const proxyConfig = {
  '/api': {
    target: 'https://api-gai.metishon.co',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, ''),
    secure: true
  },
  '/tts-api': {
    target: 'https://openspeech.bytedance.com',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/tts-api/, ''),
    secure: true
  }
};

export default defineConfig({
  server: {
    proxy: proxyConfig
  },
  preview: {
    proxy: proxyConfig  // 预览模式也使用相同的代理配置
  }
});
```

### 使用方法
- **开发环境**: `yarn dev` - 启动开发服务器（http://localhost:5173）
- **构建**: `yarn build` - 构建生产版本
- **预览**: `yarn preview` - 启动预览服务器（http://localhost:4173），包含代理配置
- **构建并预览**: `yarn build:preview` - 构建完成后自动启动预览服务器

## 生产环境配置

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass https://api-gai.sensetime.com/;
        proxy_set_header Host api-gai.sensetime.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_ssl_verify off;
        
        # CORS 头部
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }
}
```

### Apache 配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html
    
    # 前端路由支持
    <Directory "/var/www/html">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API代理
    ProxyPreserveHost On
    ProxyPass /api/ https://api-gai.sensetime.com/
    ProxyPassReverse /api/ https://api-gai.sensetime.com/
    
    # CORS 头部
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
</VirtualHost>
```

### Vercel 配置 (vercel.json)
```json
{
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "https://api-gai.sensetime.com/$1"
    }
  ]
}
```

### Netlify 配置 (_redirects)
```
/api/* https://api-gai.sensetime.com/:splat 200
```

## API 调用说明

现在所有API调用都使用 `/api` 前缀：
- 聊天接口：`/api/car/carchat`
- 语音识别：`/api/car/asr`
- 话题总结：`/api/car/summary_topics`

代理会自动将这些请求转发到 `https://api-gai.sensetime.com` 并移除 `/api` 前缀。
