<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API直连测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API直连部署测试</h1>
        
        <div class="test-item info">
            <h3>📋 测试说明</h3>
            <p>此页面用于验证项目是否正确配置为直连API模式。</p>
            <p><strong>预期结果</strong>: 所有API请求应该直接发送到原始API地址，而不是代理路径。</p>
        </div>

        <div class="test-item">
            <h3>🔍 环境检测</h3>
            <div id="env-info"></div>
        </div>

        <div class="test-item">
            <h3>🌐 API连通性测试</h3>
            <button onclick="testApiConnectivity()">开始测试</button>
            <div id="api-test-results"></div>
        </div>

        <div class="test-item">
            <h3>📝 测试日志</h3>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 环境检测
        function detectEnvironment() {
            const envInfo = document.getElementById('env-info');
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const protocol = window.location.protocol;
            const host = window.location.host;
            
            envInfo.innerHTML = `
                <p><strong>当前域名</strong>: ${host}</p>
                <p><strong>协议</strong>: ${protocol}</p>
                <p><strong>环境类型</strong>: ${isLocalhost ? '本地测试' : '生产部署'}</p>
                <p><strong>用户代理</strong>: ${navigator.userAgent}</p>
            `;
            
            log(`环境检测完成 - 域名: ${host}, 协议: ${protocol}`);
        }

        // API连通性测试
        async function testApiConnectivity() {
            const resultsDiv = document.getElementById('api-test-results');
            resultsDiv.innerHTML = '<p>🔄 正在测试API连通性...</p>';
            
            const apiTests = [
                {
                    name: '认证API',
                    url: 'https://api.st.vup.tools/api/v1/auth/send-code',
                    method: 'POST',
                    body: { phone: '13800138000' }
                },
                {
                    name: 'SenseTime API',
                    url: 'https://api-gai.metishon.co/chat/completions',
                    method: 'POST',
                    body: { model: 'test', messages: [] }
                }
            ];

            let results = '';
            
            for (const test of apiTests) {
                try {
                    log(`开始测试 ${test.name}...`);
                    
                    const response = await fetch(test.url, {
                        method: test.method,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(test.body)
                    });
                    
                    const status = response.status;
                    log(`${test.name} 响应状态: ${status}`);
                    
                    if (status === 0) {
                        results += `<div class="error">❌ ${test.name}: 网络错误或CORS问题</div>`;
                    } else if (status >= 200 && status < 500) {
                        results += `<div class="success">✅ ${test.name}: 连接成功 (状态码: ${status})</div>`;
                    } else {
                        results += `<div class="error">⚠️ ${test.name}: 服务器错误 (状态码: ${status})</div>`;
                    }
                    
                } catch (error) {
                    log(`${test.name} 测试失败: ${error.message}`);
                    results += `<div class="error">❌ ${test.name}: ${error.message}</div>`;
                }
            }
            
            results += `
                <div class="info">
                    <h4>📋 测试结果说明</h4>
                    <ul>
                        <li><strong>✅ 连接成功</strong>: API可以正常访问，直连配置正确</li>
                        <li><strong>❌ 网络错误或CORS问题</strong>: 可能需要API服务器配置CORS</li>
                        <li><strong>⚠️ 服务器错误</strong>: API服务器返回错误，但连接正常</li>
                    </ul>
                </div>
            `;
            
            resultsDiv.innerHTML = results;
            log('API连通性测试完成');
        }

        // 页面加载时执行
        window.onload = function() {
            log('页面加载完成，开始环境检测');
            detectEnvironment();
        };
    </script>
</body>
</html>
