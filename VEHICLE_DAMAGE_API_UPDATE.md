# 车辆损伤API接口更新

## 🔄 API变更概述

将车辆损伤信息获取接口从旧API迁移到新API，支持UUID格式的车辆ID和新的API路径。

### 原API配置
- **基础URL**: 旧API基础URL (通过 `/api` 代理)
- **端点**: `/vehicle_cars/{id}/damage`
- **参数**: `vehicleId: number`
- **示例**: `/vehicle_cars/123456/damage`

### 新API配置
- **基础URL**: `http://0.0.0.0:8000` (通过 `/auth-api` 代理)
- **端点**: `/api/v1/car/vehicle_cars/{id}/damage`
- **参数**: `vehicleId: string` (UUID格式)
- **示例**: `/api/v1/car/vehicle_cars/6d335e5f-13bb-4c97-88ac-495d8766a062/damage`

## 📁 修改的文件

### 1. `src/utils/apiConfig.ts`
**更新车辆损伤端点配置**：
```typescript
// 修改前
VEHICLE_DAMAGE: '/vehicle_cars', // 保持原有端点

// 修改后
VEHICLE_DAMAGE: '/api/v1/car/vehicle_cars', // 新的车辆损伤端点
```

### 2. `src/utils/chatApi.ts`
**更新getVehicleDamage函数**：

#### 参数类型变更
```typescript
// 修改前
export const getVehicleDamage = async (vehicleId: number): Promise<VehicleDamage[]>

// 修改后
export const getVehicleDamage = async (vehicleId: string): Promise<VehicleDamage[]>
```

#### API调用更新
```typescript
// 修改前
const response = await fetch(`${LEGACY_API_BASE_URL}${CHAT_ENDPOINTS.VEHICLE_DAMAGE}/${vehicleId}/damage`, {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});

// 修改后
const response = await fetch(buildChatApiUrl(`${CHAT_ENDPOINTS.VEHICLE_DAMAGE}/${vehicleId}/damage`), {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});
```

## 🔧 技术实现

### API路径构建
新的API调用使用 `buildChatApiUrl()` 函数来构建完整的URL：
- 基础URL: 通过认证API代理 (`/auth-api`)
- 完整路径: `/auth-api/api/v1/car/vehicle_cars/{vehicleId}/damage`
- 最终指向: `http://0.0.0.0:8000/api/v1/car/vehicle_cars/{vehicleId}/damage`

### 认证方式
- 继续使用JWT Bearer Token认证
- 从localStorage获取access_token
- 请求头格式：`Authorization: Bearer ${token}`

### 响应格式
响应格式保持不变，确保向后兼容：
```json
{
  "status": "success",
  "data": [
    {
      "id": 2243455,
      "vehicle_id": 1534654343,
      "category_name": "骨架",
      "damage_color": "无损",
      "damage_name": null,
      "item_name": "右后避震器",
      "level_name": null,
      "url": null
    }
  ]
}
```

## 📱 使用示例

### 新的调用方式
```typescript
// 使用UUID格式的车辆ID
const vehicleId = "6d335e5f-13bb-4c97-88ac-495d8766a062";
const damageInfo = await getVehicleDamage(vehicleId);

console.log('车辆损伤信息:', damageInfo);
```

### 错误处理
```typescript
try {
  const damageInfo = await getVehicleDamage(vehicleId);
  // 处理损伤数据
} catch (error) {
  console.error('获取车辆损伤信息失败:', error);
  // 错误处理逻辑
}
```

## 🔍 关键变更点

### 1. 参数类型变更
- **vehicleId**: `number` → `string`
- **格式**: 数字ID → UUID格式
- **示例**: `123456` → `"6d335e5f-13bb-4c97-88ac-495d8766a062"`

### 2. API路径变更
- **旧路径**: `/vehicle_cars/{id}/damage`
- **新路径**: `/api/v1/car/vehicle_cars/{id}/damage`
- **基础URL**: 从旧API切换到认证API

### 3. 代理配置
- **旧代理**: `/api` → 旧API服务器
- **新代理**: `/auth-api` → `http://0.0.0.0:8000`

## ✅ 兼容性保证

### 保持不变的功能
- 响应数据结构完全相同
- 错误处理机制保持一致
- 函数返回类型不变
- JWT认证方式不变

### 调用方代码影响
调用 `getVehicleDamage()` 函数的代码需要注意：
- 传入的vehicleId必须是字符串格式（UUID）
- 其他使用方式保持不变

## 🚀 部署说明

1. **API服务**: 确保 `http://0.0.0.0:8000` 的车辆损伤接口正常运行
2. **认证**: 使用登录接口获取的JWT token
3. **代理**: Vite代理会自动转发 `/auth-api` 请求
4. **测试**: 验证车辆损伤信息获取功能正常工作

## 📝 注意事项

1. **UUID格式**: 新API要求vehicleId为UUID字符串格式
2. **向后兼容**: 响应格式保持不变，确保现有代码正常工作
3. **错误处理**: 保持了原有的错误处理逻辑
4. **日志记录**: 添加了成功获取损伤信息的日志

## 🔍 验证清单

- [x] API端点配置正确
- [x] 参数类型更新为string
- [x] API调用使用新的基础URL
- [x] JWT认证配置正确
- [x] 响应处理逻辑保持不变
- [x] 错误处理机制完整
- [x] 代码编译通过
- [x] TypeScript类型检查通过

## 🔗 相关更新

此次更新与车辆图片API更新保持一致：
- 使用相同的基础URL和代理配置
- 采用相同的UUID参数格式
- 保持相同的认证方式
- 遵循相同的API路径规范

现在车辆损伤信息获取功能已经完全接入新API，支持UUID格式的车辆ID，并使用统一的认证API基础URL。
