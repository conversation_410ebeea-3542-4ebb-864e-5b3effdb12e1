# 宝塔面板部署说明

## 1. 项目构建

首先在本地构建项目：

```bash
yarn run build
```

构建完成后，会在 `dist` 目录下生成静态文件。

## 2. 上传文件

将 `dist` 目录下的所有文件上传到宝塔面板的网站根目录（通常是 `/www/wwwroot/你的域名/`）。

## 3. 设置伪静态规则

### 方法一：在宝塔面板中设置

1. 登录宝塔面板
2. 进入 "网站" 管理
3. 找到你的网站，点击 "设置"
4. 选择 "伪静态" 选项卡
5. 将 `bt-rewrite-simple.conf` 文件中的内容复制粘贴到伪静态规则框中
6. 点击 "保存"

### 方法二：直接编辑nginx配置

如果你有服务器管理权限，可以直接编辑nginx配置文件，将 `nginx-rewrite-rules.conf` 中的内容添加到对应的 `server` 块中。

## 4. ✅ 无需API代理设置

**重要更新**: 项目已升级为直连模式，**无需配置API反向代理**！

- ✅ 前端直接请求API服务器
- ✅ 减少服务器配置复杂度
- ✅ 提升API响应性能
- ✅ 简化部署流程

**注意**: 请确保API服务器已正确配置CORS跨域头，允许前端域名访问。

## 5. SSL证书配置（推荐）

1. 在网站设置中选择 "SSL"
2. 申请免费的Let's Encrypt证书
3. 开启 "强制HTTPS"

## 6. 性能优化设置

### 开启Gzip压缩
在网站设置的 "性能优化" 中开启Gzip压缩。

### 设置缓存
在 "缓存" 设置中：
- 静态文件缓存：7天
- 图片文件缓存：30天

## 7. 验证部署

部署完成后，访问你的域名，检查：

1. ✅ 首页能正常加载
2. ✅ 路由跳转正常（如 `/chat`, `/settings` 等）
3. ✅ API请求能正常发送到后端
4. ✅ 静态资源（图片、CSS、JS）加载正常

## 8. 常见问题

### 问题1：刷新页面出现404
**解决方案**: 检查伪静态规则是否正确设置。

### 问题2：API请求失败
**解决方案**: 
- 检查反向代理设置
- 确认后端服务正常运行
- 检查防火墙设置

### 问题3：静态资源加载失败
**解决方案**: 
- 检查文件上传是否完整
- 确认文件权限设置正确

## 9. 目录结构示例

部署后的网站根目录应该类似：

```
/www/wwwroot/你的域名/
├── index.html
├── assets/
│   ├── index-xxx.js
│   ├── index-xxx.css
│   └── ...
├── favicon.ico
└── 其他静态文件
```

## 10. 监控和维护

- 定期检查网站访问日志
- 监控API响应时间
- 及时更新SSL证书
- 定期备份网站文件
