/**
 * 验证构建后的API配置
 * 检查生产环境是否使用了正确的API地址
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取构建后的JS文件
const distDir = path.join(__dirname, 'dist', 'assets');
const jsFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.js'));

console.log('🔍 检查构建后的API配置...\n');

let foundProxyUrls = false;
let foundDirectUrls = false;

jsFiles.forEach(file => {
  const filePath = path.join(distDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否包含代理URL（不应该出现在生产环境）
  // 更精确地检测代理路径，避免误报API端点路径
  if (content.includes('"/auth-api"') || content.includes("'/auth-api'") ||
      content.includes('"/tts-api"') || content.includes("'/tts-api'") ||
      (content.includes('"/api"') && !content.includes('/api/v1'))) {
    console.log(`❌ 发现代理URL在文件: ${file}`);
    foundProxyUrls = true;
  }
  
  // 检查是否包含直接API URL（应该出现在生产环境）
  if (content.includes('https://api.st.vup.tools') || 
      content.includes('https://api-gai.metishon.co') ||
      content.includes('https://openspeech.bytedance.com')) {
    console.log(`✅ 发现直接API地址在文件: ${file}`);
    foundDirectUrls = true;
  }
});

console.log('\n📋 验证结果:');

if (!foundProxyUrls && foundDirectUrls) {
  console.log('✅ 构建配置正确！');
  console.log('✅ 生产环境使用直接API地址');
  console.log('✅ 无需配置反向代理');
  console.log('\n🚀 可以直接部署到静态文件服务器！');
} else if (foundProxyUrls) {
  console.log('❌ 构建配置有问题！');
  console.log('❌ 仍然包含代理URL');
  console.log('💡 请检查环境变量配置');
} else if (!foundDirectUrls) {
  console.log('⚠️  未找到API地址');
  console.log('💡 可能需要检查构建过程');
}

console.log('\n📝 部署提示:');
console.log('1. 将 dist 目录内容上传到Web服务器');
console.log('2. 配置SPA路由重定向（try_files $uri $uri/ /index.html）');
console.log('3. 无需配置API反向代理');
console.log('4. 确保API服务器支持CORS跨域请求');
