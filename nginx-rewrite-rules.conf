# 宝塔面板 - React SPA 伪静态规则
# 适用于汽车街客户端项目

# 1. API请求直接转发，不做重写
location ^~ /api/ {
    try_files $uri $uri/ =404;
}

# 2. 静态资源文件直接访问
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|txt|xml|pdf)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
}

# 3. assets目录下的文件直接访问
location ^~ /assets/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
}

# 4. 特殊文件直接访问
location ~* ^/(favicon\.ico|manifest\.json|robots\.txt|sitemap\.xml)$ {
    expires 1d;
    try_files $uri =404;
}

# 5. 所有其他请求重定向到 index.html (SPA路由)
location / {
    try_files $uri $uri/ /index.html;
}

# 6. 错误页面处理
error_page 404 /index.html;

# 7. 安全头设置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;

# 8. 压缩设置
location ~* \.(js|css|html|xml|txt)$ {
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
