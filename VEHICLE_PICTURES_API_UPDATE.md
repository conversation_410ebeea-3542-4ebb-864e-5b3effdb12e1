# 车辆图片API接口更新

## 🔄 API变更概述

将车辆图片获取接口从旧API迁移到新API，支持UUID格式的车辆ID和新的API路径。

### 原API配置
- **基础URL**: 旧API基础URL (通过 `/api` 代理)
- **端点**: `/vehicle_cars/{id}/pictures`
- **参数**: `vehicleId: number`
- **示例**: `/vehicle_cars/123456/pictures`

### 新API配置
- **基础URL**: `http://0.0.0.0:8000` (通过 `/auth-api` 代理)
- **端点**: `/api/v1/car/vehicle_cars/{id}/pictures`
- **参数**: `vehicleId: string` (UUID格式)
- **示例**: `/api/v1/car/vehicle_cars/6d335e5f-13bb-4c97-88ac-495d8766a062/pictures`

## 📁 修改的文件

### 1. `src/utils/apiConfig.ts`
**更新车辆图片端点配置**：
```typescript
// 修改前
VEHICLE_PICTURES: '/vehicle_cars', // 保持原有端点

// 修改后
VEHICLE_PICTURES: '/api/v1/car/vehicle_cars', // 新的车辆图片端点
```

### 2. `src/utils/chatApi.ts`
**更新getVehiclePictures函数**：

#### 参数类型变更
```typescript
// 修改前
export const getVehiclePictures = async (vehicleId: number): Promise<VehiclePicture[]>

// 修改后
export const getVehiclePictures = async (vehicleId: string): Promise<VehiclePicture[]>
```

#### API调用更新
```typescript
// 修改前
const response = await fetch(`${LEGACY_API_BASE_URL}${CHAT_ENDPOINTS.VEHICLE_PICTURES}/${vehicleId}/pictures`, {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});

// 修改后
const response = await fetch(buildChatApiUrl(`${CHAT_ENDPOINTS.VEHICLE_PICTURES}/${vehicleId}/pictures`), {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});
```

## 🔧 技术实现

### API路径构建
新的API调用使用 `buildChatApiUrl()` 函数来构建完整的URL：
- 基础URL: 通过认证API代理 (`/auth-api`)
- 完整路径: `/auth-api/api/v1/car/vehicle_cars/{vehicleId}/pictures`
- 最终指向: `http://0.0.0.0:8000/api/v1/car/vehicle_cars/{vehicleId}/pictures`

### 认证方式
- 继续使用JWT Bearer Token认证
- 从localStorage获取access_token
- 请求头格式：`Authorization: Bearer ${token}`

### 响应格式
响应格式保持不变，确保向后兼容：
```json
{
  "status": "success",
  "data": [
    {
      "id": 152472,
      "vehicle_id": 1534654343,
      "pic_name": "左前45度",
      "pic_type": 1,
      "pic_url": "http://images.autostreets.com/group1/M00/7A/EC/wKghH163nkyAbh98AAM8fsJFaIs049.jpg"
    }
  ]
}
```

## 📱 使用示例

### 新的调用方式
```typescript
// 使用UUID格式的车辆ID
const vehicleId = "6d335e5f-13bb-4c97-88ac-495d8766a062";
const pictures = await getVehiclePictures(vehicleId);

console.log('车辆图片列表:', pictures);
```

### 错误处理
```typescript
try {
  const pictures = await getVehiclePictures(vehicleId);
  // 处理图片数据
} catch (error) {
  console.error('获取车辆图片失败:', error);
  // 错误处理逻辑
}
```

## 🔍 关键变更点

### 1. 参数类型变更
- **vehicleId**: `number` → `string`
- **格式**: 数字ID → UUID格式
- **示例**: `123456` → `"6d335e5f-13bb-4c97-88ac-495d8766a062"`

### 2. API路径变更
- **旧路径**: `/vehicle_cars/{id}/pictures`
- **新路径**: `/api/v1/car/vehicle_cars/{id}/pictures`
- **基础URL**: 从旧API切换到认证API

### 3. 代理配置
- **旧代理**: `/api` → 旧API服务器
- **新代理**: `/auth-api` → `http://0.0.0.0:8000`

## ✅ 兼容性保证

### 保持不变的功能
- 响应数据结构完全相同
- 错误处理机制保持一致
- 函数返回类型不变
- JWT认证方式不变

### 调用方代码影响
调用 `getVehiclePictures()` 函数的代码需要注意：
- 传入的vehicleId必须是字符串格式（UUID）
- 其他使用方式保持不变

## 🚀 部署说明

1. **API服务**: 确保 `http://0.0.0.0:8000` 的车辆图片接口正常运行
2. **认证**: 使用登录接口获取的JWT token
3. **代理**: Vite代理会自动转发 `/auth-api` 请求
4. **测试**: 验证车辆图片获取功能正常工作

## 📝 注意事项

1. **UUID格式**: 新API要求vehicleId为UUID字符串格式
2. **向后兼容**: 响应格式保持不变，确保现有代码正常工作
3. **错误处理**: 保持了原有的错误处理逻辑
4. **日志记录**: 添加了成功获取图片的日志信息

## 🔍 验证清单

- [x] API端点配置正确
- [x] 参数类型更新为string
- [x] API调用使用新的基础URL
- [x] JWT认证配置正确
- [x] 响应处理逻辑保持不变
- [x] 错误处理机制完整
- [x] 代码编译通过
- [x] TypeScript类型检查通过

现在车辆图片获取功能已经完全接入新API，支持UUID格式的车辆ID，并使用统一的认证API基础URL。
